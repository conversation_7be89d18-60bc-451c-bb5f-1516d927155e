'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class Area extends Model {
    static associate(models) {
      Area.belongsTo(models.Country, {
        foreignKey: 'countryId',
        as: 'country'
      });
      Area.hasMany(models.Store, {
        foreignKey: 'areaId',
        as: 'stores'
      });
      Area.belongsToMany(models.Customer, {
        through: models.CustomerArea,
        foreignKey: 'area_id',
        otherKey: 'customer_id',
        as: 'customers'
      });
    }
  }
  
  Area.init({
    countryId: {
      type: DataTypes.INTEGER,
      references: {
        model: 'Countries',
        key: 'id'
      }
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false
    },
    notes: DataTypes.TEXT
  }, {
    sequelize,
    modelName: 'Area',
  });
  
  return Area;
}; 