<h1>الطلبات المُسلّمة أو المرفوضة</h1>
<table class="table table-bordered">
  <thead>
    <tr>
      <th>رقم الطلب</th>
      <th>العميل</th>
      <th>الحالة</th>
      <th>المجموع</th>
      <th>توصيل</th>
    </tr>
  </thead>
  <tbody>
    <% orders.forEach(order => { %>
      <tr>
        <td>
          <a href="/store/orders/<%= order.id %>">
            #<%= order.id %>
          </a>
        </td>
        <td>
          <a href="/store/customers/<%= order.customer.id %>">
            <%= order.customer.name %>
          </a>
        </td>
        <td><%= order.status === 'delivered' ? 'تم التسليم' : 'مرفوض' %></td>
        <td><%= order.totalPrice %> ل.س</td>
        <td>
          <% if (order.delivery) { %>
            <a href="/store/orders/<%= order.id %>/delivery" title="عرض التوصيل">
              🛵
            </a>
          <% } else { %>
            -
          <% } %>
        </td>
      </tr>
    <% }); %>
  </tbody>
</table>

<% if (totalPages > 1) { %>
  <nav aria-label="Page navigation" class="d-flex justify-content-center mt-4">
    <ul class="pagination">
      <% if (currentPage > 1) { %>
        <li class="page-item">
          <a class="page-link" href="?page=<%= currentPage - 1 %>">السابق</a>
        </li>
      <% } else { %>
        <li class="page-item disabled">
          <span class="page-link">السابق</span>
        </li>
      <% } %>
  
      <% for(let i = 1; i <= totalPages; i++) { %>
        <li class="page-item <%= currentPage === i ? 'active' : '' %>">
          <a class="page-link" href="?page=<%= i %>"><%= i %></a>
        </li>
      <% } %>
  
      <% if (currentPage < totalPages) { %>
        <li class="page-item">
          <a class="page-link" href="?page=<%= currentPage + 1 %>">التالي</a>
        </li>
      <% } else { %>
        <li class="page-item disabled">
          <span class="page-link">التالي</span>
        </li>
      <% } %>
    </ul>
  </nav>
  <% } %>