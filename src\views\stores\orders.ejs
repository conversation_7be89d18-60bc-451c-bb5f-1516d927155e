<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>إدارة الطلبات</h1>
        <div class="btn-group" role="group">
            <a href="/store/orders/pending" class="btn btn-warning">
                <i class="fas fa-clock"></i> الطلبات المعلقة
            </a>
            <a href="/store/orders/export" class="btn btn-success">
                <i class="fas fa-download"></i> تصدير
            </a>
        </div>
    </div>

    <!-- مكون البحث والفلتر -->
    <%- include('../partials/search-filter', {
        filters: filters || {},
        sortOptions: sortOptions || [],
        customFilters: '../partials/filters/orders-filters',
        currentUrl: currentUrl || '',
        pagination: pagination || null
    }) %>

    <div class="table-responsive">
        <table class="table table-striped table-hover">
            <thead class="thead-dark">
                <tr>
                    <th>
                        <a href="?sortBy=id&sortOrder=<%= (filters.sortBy === 'id' && filters.sortOrder === 'asc') ? 'desc' : 'asc' %>"
                           class="text-white text-decoration-none">
                            رقم الطلب
                            <% if (filters.sortBy === 'id') { %>
                                <i class="fas fa-sort-<%= filters.sortOrder === 'asc' ? 'up' : 'down' %>"></i>
                            <% } else { %>
                                <i class="fas fa-sort text-muted"></i>
                            <% } %>
                        </a>
                    </th>
                    <th>العميل</th>
                    <th>
                        <a href="?sortBy=status&sortOrder=<%= (filters.sortBy === 'status' && filters.sortOrder === 'asc') ? 'desc' : 'asc' %>"
                           class="text-white text-decoration-none">
                            الحالة
                            <% if (filters.sortBy === 'status') { %>
                                <i class="fas fa-sort-<%= filters.sortOrder === 'asc' ? 'up' : 'down' %>"></i>
                            <% } else { %>
                                <i class="fas fa-sort text-muted"></i>
                            <% } %>
                        </a>
                    </th>
                    <th>
                        <a href="?sortBy=totalAmount&sortOrder=<%= (filters.sortBy === 'totalAmount' && filters.sortOrder === 'asc') ? 'desc' : 'asc' %>"
                           class="text-white text-decoration-none">
                            المبلغ الإجمالي
                            <% if (filters.sortBy === 'totalAmount') { %>
                                <i class="fas fa-sort-<%= filters.sortOrder === 'asc' ? 'up' : 'down' %>"></i>
                            <% } else { %>
                                <i class="fas fa-sort text-muted"></i>
                            <% } %>
                        </a>
                    </th>
                    <th>
                        <a href="?sortBy=createdAt&sortOrder=<%= (filters.sortBy === 'createdAt' && filters.sortOrder === 'asc') ? 'desc' : 'asc' %>"
                           class="text-white text-decoration-none">
                            تاريخ الطلب
                            <% if (filters.sortBy === 'createdAt') { %>
                                <i class="fas fa-sort-<%= filters.sortOrder === 'asc' ? 'up' : 'down' %>"></i>
                            <% } else { %>
                                <i class="fas fa-sort text-muted"></i>
                            <% } %>
                        </a>
                    </th>
                    <th>التوصيل</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                <% orders.forEach(order => { %>
                    <tr>
                        <td>
                            <a href="/store/orders/<%= order.id %>" class="font-weight-bold">
                                #<%= order.id %>
                            </a>
                        </td>
                        <td>
                            <% if (order.customer) { %>
                                <a href="/store/customers/<%= order.customer.id %>" class="text-decoration-none">
                                    <i class="fas fa-user"></i> <%= order.customer.name %>
                                </a>
                            <% } else { %>
                                <span class="text-muted">عميل محذوف</span>
                            <% } %>
                        </td>
                        <td>
                            <% if (order.status === 'delivered') { %>
                                <span class="badge badge-success">تم التسليم</span>
                            <% } else if (order.status === 'cancelled') { %>
                                <span class="badge badge-danger">ملغي</span>
                            <% } else if (order.status === 'pending') { %>
                                <span class="badge badge-warning">في الانتظار</span>
                            <% } else if (order.status === 'confirmed') { %>
                                <span class="badge badge-info">مؤكد</span>
                            <% } else if (order.status === 'preparing') { %>
                                <span class="badge badge-primary">قيد التحضير</span>
                            <% } else if (order.status === 'ready') { %>
                                <span class="badge badge-secondary">جاهز للتوصيل</span>
                            <% } else if (order.status === 'out_for_delivery') { %>
                                <span class="badge badge-dark">في الطريق</span>
                            <% } else { %>
                                <span class="badge badge-light"><%= order.status %></span>
                            <% } %>
                        </td>
                        <td>
                            <span class="badge badge-success font-weight-bold">
                                $<%= order.totalAmount || order.totalPrice || 0 %>
                            </span>
                        </td>
                        <td>
                            <% if (order.createdAt) { %>
                                <small class="text-muted">
                                    <%= new Date(order.createdAt).toLocaleDateString('ar-EG') %>
                                    <br>
                                    <%= new Date(order.createdAt).toLocaleTimeString('ar-EG') %>
                                </small>
                            <% } else { %>
                                <span class="text-muted">غير محدد</span>
                            <% } %>
                        </td>
                        <td>
                            <% if (order.delivery) { %>
                                <a href="/store/orders/<%= order.id %>/delivery"
                                   class="btn btn-sm btn-info"
                                   title="عرض التوصيل">
                                    <i class="fas fa-truck"></i>
                                </a>
                            <% } else { %>
                                <span class="text-muted">
                                    <i class="fas fa-minus"></i>
                                </span>
                            <% } %>
                        </td>
                        <td class="action-buttons">
                            <div class="btn-group" role="group">
                                <a href="/store/orders/<%= order.id %>"
                                   class="btn btn-sm btn-info"
                                   title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <% if (order.status === 'pending') { %>
                                    <form action="/store/orders/<%= order.id %>/confirm" method="POST" class="d-inline">
                                        <button type="submit"
                                                class="btn btn-sm btn-success"
                                                title="تأكيد الطلب">
                                            <i class="fas fa-check"></i>
                                        </button>
                                    </form>
                                <% } %>
                                <% if (['pending', 'confirmed'].includes(order.status)) { %>
                                    <form action="/store/orders/<%= order.id %>/cancel" method="POST" class="d-inline">
                                        <button type="submit"
                                                class="btn btn-sm btn-danger"
                                                title="إلغاء الطلب"
                                                onclick="return confirm('هل أنت متأكد من إلغاء هذا الطلب؟')">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </form>
                                <% } %>
                            </div>
                        </td>
                    </tr>
                <% }); %>

                <% if (!orders || orders.length === 0) { %>
                    <tr>
                        <td colspan="7" class="text-center py-4">
                            <div class="text-muted">
                                <i class="fas fa-shopping-cart fa-3x mb-3"></i>
                                <p class="mb-0">لا توجد طلبات مطابقة للبحث</p>
                                <% if (Object.keys(filters || {}).length > 0) { %>
                                    <a href="/store/orders" class="btn btn-sm btn-primary mt-2">
                                        <i class="fas fa-times"></i> مسح الفلاتر
                                    </a>
                                <% } %>
                            </div>
                        </td>
                    </tr>
                <% } %>
            </tbody>
        </table>
    </div>

    <!-- الـ Pagination الجديد -->
    <%- include('../partials/pagination', {
        pagination: pagination || null,
        currentUrl: currentUrl || '',
        originalUrl: originalUrl || ''
    }) %>
</div>