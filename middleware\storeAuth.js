const { verifyToken, extractToken } = require('../utils/jwt');
const { Store } = require('../models');

const publicPaths = [
    '/store/auth/login',
    '/store/auth/register',
    '/store/auth/logout',
    '/assets',
    '/css',
    '/js',
    '/favicon.ico',
    '/public',
    '/uploads'
];

const isPublicPath = (path) => {
    return publicPaths.some(publicPath => path.startsWith(publicPath));
};

const requireStoreAuth = async (req, res, next) => {
    // Allow access to public paths
    if (isPublicPath(req.path)) {
        return next();
    }

    const token = extractToken(req);
    if (!token) {
        return res.redirect('/store/login');
    }

    try {
        const decoded = verifyToken(token);

        // Check if user type is store
        if (decoded.userType !== 'store') {
            return res.redirect('/store/login');
        }

        // Get store from database
        const store = await Store.findOne({
            where: {
                id: decoded.id,
                status: 'active'
            }
        });

        if (!store) {
            return res.redirect('/store/login');
        }

        // Add user and store to request object
        req.user = decoded;
        req.store = store;
        next();
    } catch (error) {
        console.error('Store auth error:', error);
        return res.redirect('/store/login');
    }
};

module.exports = requireStoreAuth;