<div class="container mt-4">
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="fw-bold">
      Stores in "<%= category.name %>"
    </h2>
    <a href="/customers/home" class="btn btn-secondary">← Back</a>
  </div>

  <% if (stores.length === 0) { %>
    <div class="alert alert-info text-center">
      No stores available in this category and area.
    </div>
  <% } else { %>
    <div class="row">
      <% stores.forEach(store => { %>
        <div class="col-md-4 mb-4">
          <a href="/customers/stores/<%= store.id %>" class="text-decoration-none text-dark">
            <div class="card shadow-sm border-0 h-100">
              <div class="card-body">
                <h5 class="card-title fw-bold"><%= store.name %></h5>
                <p class="mb-2"><strong>Address:</strong> <%= store.address %></p>
                <p class="mb-2"><strong>Status:</strong>
                  <% if (store.status === 'active') { %>
                    <span class="badge bg-success">Active</span>
                  <% } else if (store.status === 'inactive') { %>
                    <span class="badge bg-danger">Inactive</span>
                  <% } else { %>
                    <span class="badge bg-secondary">Unknown</span>
                  <% } %>
                </p>
              </div>
            </div>
          </a>
        </div>
      <% }); %>
    </div>
  <% } %>
</div>
