/**
 * سكريبت إصلاح مشكلة Order.storeId cannot be null
 */

const { execSync } = require('child_process');
const path = require('path');

console.log('🔧 بدء إصلاح مشكلة السلة...\n');

try {
    // الانتقال إلى مجلد src
    if (process.cwd().endsWith('src')) {
        console.log('📁 نحن بالفعل في مجلد src');
    } else {
        process.chdir('src');
        console.log('📁 تم الانتقال إلى مجلد src');
    }

    console.log('🗃️ تشغيل migrations لضمان وجود جدول Cart...');
    try {
        execSync('npx sequelize-cli db:migrate --migrations-path migrations --config config/config.js', {
            stdio: 'inherit'
        });
        console.log('✅ تم تشغيل migrations بنجاح');
    } catch (migrationError) {
        console.log('⚠️ Migrations مكتملة مسبقاً');
    }

    console.log('\n🔍 تشغيل تشخيص السلة...');
    try {
        execSync('node ../debug-cart.js', { stdio: 'inherit' });
        console.log('✅ تم التشخيص بنجاح');
    } catch (debugError) {
        console.log('⚠️ حدث خطأ في التشخيص، سنتابع...');
    }

    console.log('\n🚀 تشغيل الخادم...');
    console.log('📋 الإصلاحات المطبقة:');
    console.log('  ✅ إضافة جميع attributes للمتجر في CartService');
    console.log('  ✅ إضافة تحقق من وجود store.id قبل إنشاء الطلب');
    console.log('  ✅ إضافة logs تفصيلية لتتبع المشكلة');
    console.log('  ✅ تحسين معالجة الأخطاء');
    console.log('  ✅ إضافة تنظيف تلقائي للعناصر غير الصالحة');

    console.log('\n🌐 الروابط المتاحة:');
    console.log('  📱 API: http://localhost:3001/api');
    console.log('  🛒 السلة: http://localhost:3001/api/mobile/cart');
    console.log('  💳 الدفع: http://localhost:3001/api/mobile/checkout');

    console.log('\n📱 اختبار API:');
    console.log('  1. تسجيل دخول: POST /api/customers/login');
    console.log('  2. إضافة للسلة: POST /api/mobile/cart/add');
    console.log('  3. عرض السلة: GET /api/mobile/cart');
    console.log('  4. بيانات الدفع: GET /api/mobile/checkout');
    console.log('  5. إنشاء طلب: POST /api/mobile/checkout');

    console.log('\n⚡ تشغيل الخادم...\n');

    // تشغيل الخادم
    execSync('node app.js', { stdio: 'inherit' });

} catch (error) {
    console.error('\n❌ خطأ في الإصلاح:', error.message);
    
    console.log('\n🔧 خطوات الإصلاح اليدوي:');
    console.log('1. تأكد من وجود منتجات مرتبطة بمتاجر:');
    console.log('   SELECT p.name, s.name as store_name FROM Products p JOIN Stores s ON p.storeId = s.id LIMIT 5;');
    
    console.log('\n2. تأكد من وجود عناصر في السلة:');
    console.log('   SELECT * FROM Carts LIMIT 5;');
    
    console.log('\n3. تحقق من العلاقات:');
    console.log('   - Product.belongsTo(Store)');
    console.log('   - Cart.belongsTo(Product)');
    console.log('   - Cart.belongsTo(Customer)');
    
    console.log('\n4. جرب تشغيل التشخيص:');
    console.log('   node debug-cart.js');
    
    process.exit(1);
}
