<!-- مكون البحث والفلتر العام -->
<div class="search-filter-container mb-4">
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <button class="btn btn-link" type="button" data-toggle="collapse" data-target="#searchFilterCollapse" aria-expanded="false">
                    <i class="fas fa-search"></i> البحث والفلتر
                </button>
            </h5>
        </div>
        
        <div id="searchFilterCollapse" class="collapse <%= (filters && Object.keys(filters).length > 0) ? 'show' : '' %>">
            <div class="card-body">
                <form method="GET" action="" id="searchFilterForm">
                    <div class="row">
                        <!-- حقل البحث العام -->
                        <div class="col-md-4 mb-3">
                            <label for="search" class="form-label">البحث العام</label>
                            <input type="text" 
                                   class="form-control" 
                                   id="search" 
                                   name="search" 
                                   value="<%= filters.search || '' %>"
                                   placeholder="ابحث في جميع الحقول...">
                        </div>

                        <!-- فلاتر مخصصة حسب الصفحة -->
                        <% if (typeof customFilters !== 'undefined' && customFilters) { %>
                            <%- include(customFilters, { filters: filters || {} }) %>
                        <% } %>

                        <!-- خيارات الترتيب -->
                        <div class="col-md-3 mb-3">
                            <label for="sortBy" class="form-label">ترتيب حسب</label>
                            <select class="form-control" id="sortBy" name="sortBy">
                                <% if (typeof sortOptions !== 'undefined' && sortOptions) { %>
                                    <% sortOptions.forEach(option => { %>
                                        <option value="<%= option.value %>" 
                                                <%= (filters.sortBy === option.value) ? 'selected' : '' %>>
                                            <%= option.label %>
                                        </option>
                                    <% }) %>
                                <% } else { %>
                                    <option value="createdAt" <%= (filters.sortBy === 'createdAt' || !filters.sortBy) ? 'selected' : '' %>>تاريخ الإنشاء</option>
                                    <option value="updatedAt" <%= (filters.sortBy === 'updatedAt') ? 'selected' : '' %>>تاريخ التحديث</option>
                                <% } %>
                            </select>
                        </div>

                        <div class="col-md-2 mb-3">
                            <label for="sortOrder" class="form-label">الاتجاه</label>
                            <select class="form-control" id="sortOrder" name="sortOrder">
                                <option value="desc" <%= (filters.sortOrder === 'desc' || !filters.sortOrder) ? 'selected' : '' %>>تنازلي</option>
                                <option value="asc" <%= (filters.sortOrder === 'asc') ? 'selected' : '' %>>تصاعدي</option>
                            </select>
                        </div>

                        <!-- عدد العناصر في الصفحة -->
                        <div class="col-md-2 mb-3">
                            <label for="limit" class="form-label">عدد النتائج</label>
                            <select class="form-control" id="limit" name="limit">
                                <option value="10" <%= (filters.limit == '10') ? 'selected' : '' %>>10</option>
                                <option value="20" <%= (filters.limit == '20' || !filters.limit) ? 'selected' : '' %>>20</option>
                                <option value="50" <%= (filters.limit == '50') ? 'selected' : '' %>>50</option>
                                <option value="100" <%= (filters.limit == '100') ? 'selected' : '' %>>100</option>
                            </select>
                        </div>
                    </div>

                    <!-- أزرار التحكم -->
                    <div class="row">
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i> بحث
                            </button>
                            <a href="<%= currentUrl.split('?')[0] %>" class="btn btn-secondary">
                                <i class="fas fa-times"></i> مسح الفلاتر
                            </a>
                            <button type="button" class="btn btn-info" onclick="exportResults()">
                                <i class="fas fa-download"></i> تصدير
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- معلومات النتائج -->
<% if (typeof pagination !== 'undefined' && pagination) { %>
    <div class="results-info mb-3">
        <div class="row align-items-center">
            <div class="col-md-6">
                <p class="mb-0 text-muted">
                    عرض <%= pagination.startItem %> إلى <%= pagination.endItem %> 
                    من أصل <%= pagination.totalCount %> نتيجة
                </p>
            </div>
            <div class="col-md-6 text-right">
                <% if (typeof activeFiltersCount !== 'undefined' && activeFiltersCount > 0) { %>
                    <span class="badge badge-info">
                        <i class="fas fa-filter"></i> <%= activeFiltersCount %> فلتر نشط
                    </span>
                <% } %>
            </div>
        </div>
    </div>
<% } %>

<script>
// تطبيق الفلاتر تلقائياً عند التغيير
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('searchFilterForm');
    const inputs = form.querySelectorAll('input, select');
    
    // تطبيق البحث عند الكتابة (مع تأخير)
    let searchTimeout;
    const searchInput = document.getElementById('search');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                if (this.value.length >= 2 || this.value.length === 0) {
                    form.submit();
                }
            }, 500);
        });
    }
    
    // تطبيق الفلاتر عند التغيير
    inputs.forEach(input => {
        if (input.id !== 'search') {
            input.addEventListener('change', function() {
                form.submit();
            });
        }
    });
});

// تصدير النتائج
function exportResults() {
    const currentUrl = new URL(window.location);
    currentUrl.searchParams.set('export', 'csv');
    window.open(currentUrl.toString(), '_blank');
}

// حفظ حالة الفلاتر في localStorage
function saveFilterState() {
    const formData = new FormData(document.getElementById('searchFilterForm'));
    const filters = {};
    for (let [key, value] of formData.entries()) {
        filters[key] = value;
    }
    localStorage.setItem('tableFilters_' + window.location.pathname, JSON.stringify(filters));
}

// استرجاع حالة الفلاتر من localStorage
function loadFilterState() {
    const saved = localStorage.getItem('tableFilters_' + window.location.pathname);
    if (saved) {
        const filters = JSON.parse(saved);
        Object.keys(filters).forEach(key => {
            const element = document.querySelector(`[name="${key}"]`);
            if (element) {
                element.value = filters[key];
            }
        });
    }
}
</script>

<style>
.search-filter-container .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.search-filter-container .btn-link {
    text-decoration: none;
    color: #495057;
    font-weight: 500;
}

.search-filter-container .btn-link:hover {
    color: #007bff;
    text-decoration: none;
}

.results-info {
    background-color: #f8f9fa;
    padding: 10px 15px;
    border-radius: 5px;
    border: 1px solid #dee2e6;
}

.badge {
    font-size: 0.875em;
}
</style>
