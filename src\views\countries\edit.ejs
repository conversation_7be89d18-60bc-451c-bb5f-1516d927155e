<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Edit Country</h1>
        <a href="/admin/countries" class="btn btn-secondary">Back to Countries</a>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-body">
                    <form action="/admin/countries/<%= country.id %>" method="POST">
                        <input type="hidden" name="_method" value="PUT">
                        
                        <div class="mb-3">
                            <label for="name" class="form-label">Country Name</label>
                            <input type="text" class="form-control" id="name" name="name" 
                                   value="<%= country.name %>" required>
                        </div>

                        <div class="mb-3">
                            <label for="notes" class="form-label">Notes</label>
                            <textarea class="form-control" id="notes" name="notes" 
                                      rows="3"><%= country.notes || '' %></textarea>
                        </div>

                        <button type="submit" class="btn btn-primary">Update Country</button>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Associated Areas</h5>
                </div>
                <div class="card-body">
                    <% if (country.areas && country.areas.length > 0) { %>
                        <ul class="list-group">
                            <% country.areas.forEach(area => { %>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <%= area.name %>
                                    <a href="/admin/areas/<%= area.id %>/edit" class="btn btn-sm btn-warning">
                                        <i class="fas fa-edit"></i> Edit
                                    </a>
                                </li>
                            <% }); %>
                        </ul>
                    <% } else { %>
                        <div class="alert alert-info">No areas associated with this country.</div>
                    <% } %>
                    
                    <div class="mt-3">
                        <a href="/admin/areas/create?countryId=<%= country.id %>" class="btn btn-success">
                            <i class="fas fa-plus"></i> Add New Area
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div> 