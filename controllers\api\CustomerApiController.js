const { Customer, Store, Product, Order, OrderDetail, Category, Area, Country } = require('../../models');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const { Op } = require('sequelize');

class CustomerApiController {
    
    /**
     * تسجيل دخول العميل
     * POST /api/customers/login
     */
    async login(req, res) {
        try {
            const { name, password } = req.body;
            
            // التحقق من وجود البيانات المطلوبة
            if (!name || !password) {
                return res.status(400).json({
                    success: false,
                    message: 'البريد الإلكتروني وكلمة المرور مطلوبان',
                    data: null
                });
            }

            // البحث عن العميل
            const customer = await Customer.findOne({
                where: { 
                    [Op.or]: [
                        { name: name },
                        { phoneNumber: name } // يمكن تسجيل الدخول بالهاتف أيضاً
                    ]
                }
            });

            if (!customer) {
                return res.status(401).json({
                    success: false,
                    message: 'بيانات تسجيل الدخول غير صحيحة',
                    data: null
                });
            }

            // التحقق من كلمة المرور
            const isValidPassword = await bcrypt.compare(password, customer.password);
            if (!isValidPassword) {
                return res.status(401).json({
                    success: false,
                    message: 'بيانات تسجيل الدخول غير صحيحة',
                    data: null
                });
            }

            // إنشاء JWT token
            const access_token = jwt.sign(
                { 
                    customerId: customer.id,
                    name: customer.name,
                    type: 'customer'
                },
                process.env.JWT_SECRET || 'your-secret-key',
                { expiresIn: '30d' }
            );

            // إرجاع البيانات
            res.json({
                success: true,
                message: 'تم تسجيل الدخول بنجاح',
                data: {
                    access_token
                }
            });

        } catch (error) {
            console.error('خطأ في تسجيل دخول العميل:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    /**
     * تسجيل عميل جديد
     * POST /api/customers/register
     */
    async register(req, res) {
        try {
            const { name, email, phone, password, address, areaId } = req.body;

            // التحقق من البيانات المطلوبة
            if (!name || !email || !phone || !password) {
                return res.status(400).json({
                    success: false,
                    message: 'جميع البيانات الأساسية مطلوبة',
                    data: null
                });
            }

            // التحقق من عدم وجود العميل مسبقاً
            const existingCustomer = await Customer.findOne({
                where: {
                    [Op.or]: [
                        { email: email },
                        { phone: phone }
                    ]
                }
            });

            if (existingCustomer) {
                return res.status(409).json({
                    success: false,
                    message: 'البريد الإلكتروني أو رقم الهاتف مستخدم مسبقاً',
                    data: null
                });
            }

            // تشفير كلمة المرور
            const hashedPassword = await bcrypt.hash(password, 12);

            // إنشاء العميل الجديد
            const customer = await Customer.create({
                name,
                email,
                phone,
                password: hashedPassword,
                address,
                areaId
            });

            // إنشاء JWT token
            const token = jwt.sign(
                { 
                    customerId: customer.id,
                    email: customer.email,
                    type: 'customer'
                },
                process.env.JWT_SECRET || 'your-secret-key',
                { expiresIn: '30d' }
            );

            res.status(201).json({
                success: true,
                message: 'تم إنشاء الحساب بنجاح',
                data: {
                    token,
                    customer: {
                        id: customer.id,
                        name: customer.name,
                        email: customer.email,
                        phone: customer.phone,
                        address: customer.address,
                        areaId: customer.areaId,
                        createdAt: customer.createdAt
                    }
                }
            });

        } catch (error) {
            console.error('خطأ في تسجيل العميل:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    /**
     * الحصول على بيانات العميل
     * GET /api/customers/profile
     */
    async getProfile(req, res) {
        try {
            const customerId = req.customer.id;

            const customer = await Customer.findByPk(customerId, {
                include: [
                    {
                        model: Area,
                         as: 'areas',
                        include: {model: Country,
                            as: 'country',
                            attributes: ['name']
                         }
                    }
                ],
                attributes: { exclude: ['password'] }
            });

            if (!customer) {
                return res.status(404).json({
                    success: false,
                    message: 'العميل غير موجود',
                    data: null
                });
            }

            res.json({
                success: true,
                message: 'تم جلب البيانات بنجاح',
                data: { customer }
            });

        } catch (error) {
            console.error('خطأ في جلب بيانات العميل:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    /**
     * تحديث بيانات العميل
     * PUT /api/customers/profile
     */
    async updateProfile(req, res) {
        try {
            const customerId = req.customer.id;
            const { name, phone } = req.body;

            const customer = await Customer.findByPk(customerId);
            if (!customer) {
                return res.status(404).json({
                    success: false,
                    message: 'العميل غير موجود',
                    data: null
                });
            }

            // تحديث البيانات
            await customer.update({
                name: name || customer.name,
                phone: phone || customer.phone,
            });

            res.json({
                success: true,
                message: 'تم تحديث البيانات بنجاح',
                data: {
                    customer: {
                        id: customer.id,
                        name: customer.name,
                        email: customer.email,
                        phone: customer.phone,
                        include: [
                            {
                                model: Area,
                                as: 'areas',
                                include: {model: Country,
                                    as: 'country',
                                    attributes: ['name']
                                 }
                            }
                        ]
                    }
                }
            });

        } catch (error) {
            console.error('خطأ في تحديث بيانات العميل:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    /**
     * تغيير كلمة المرور
     * PUT /api/customers/change-password
     */
    async changePassword(req, res) {
        try {
            const customerId = req.customer.id;
            const { currentPassword, newPassword } = req.body;

            if (!currentPassword || !newPassword) {
                return res.status(400).json({
                    success: false,
                    message: 'كلمة المرور الحالية والجديدة مطلوبتان',
                    data: null
                });
            }

            const customer = await Customer.findByPk(customerId);
            if (!customer) {
                return res.status(404).json({
                    success: false,
                    message: 'العميل غير موجود',
                    data: null
                });
            }

            // التحقق من كلمة المرور الحالية
            const isValidPassword = await bcrypt.compare(currentPassword, customer.password);
            if (!isValidPassword) {
                return res.status(401).json({
                    success: false,
                    message: 'كلمة المرور الحالية غير صحيحة',
                    data: null
                });
            }

            // تشفير كلمة المرور الجديدة
            const hashedPassword = await bcrypt.hash(newPassword, 12);

            // تحديث كلمة المرور
            await customer.update({ password: hashedPassword });

            res.json({
                success: true,
                message: 'تم تغيير كلمة المرور بنجاح',
                data: null
            });

        } catch (error) {
            console.error('خطأ في تغيير كلمة المرور:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    async addAddress(req, res) {
    try {
        const customerId = 1;//req.customer.id;
        console.log(req.body);
        const { areaId, address } = req.body;

        // التحقق من المدخلات
        if (!areaId || !address) {
            return res.status(400).json({
                success: false,
                message: 'يجب توفير المنطقة والعنوان',
                data: null
            });
        }

        const newAddress = await CustomerArea.create({
            customer_id: customerId,
            area_id: areaId,
            address
        });

        res.json({
            success: true,
            message: 'تم إضافة العنوان بنجاح',
            data: newAddress
        });

    } catch (err) {
        console.error('Address addition error:', err);
        res.status(500).json({
            success: false,
            message: 'حدث خطأ أثناء إضافة العنوان',
            data: null
        });
    }
    }

    async deleteAddress(req, res) {
        const addressId = req.params.id;
        const customerId = req.user.id;

        try {
            const deleted = await CustomerArea.destroy({
                where: {
                    area_id: addressId,
                    customer_id: customerId
                }
            });

            if (!deleted) {
                return res.status(404).json({
                    success: false,
                    message: 'العنوان غير موجود أو لا يخص هذا المستخدم',
                    data: null
                });
            }

            res.json({
                success: true,
                message: 'تم حذف العنوان بنجاح',
                data: { deletedId: addressId }
            });

        } catch (err) {
            console.error('Address deletion error:', err);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ أثناء حذف العنوان',
                data: null
            });
        }
    }


    /**
     * تسجيل الخروج
     * POST /api/customers/logout
     */
    async logout(req, res) {
        try {
            // في JWT لا نحتاج لحذف شيء من الخادم
            // يمكن للتطبيق حذف التوكن محلياً
            res.json({
                success: true,
                message: 'تم تسجيل الخروج بنجاح',
                data: null
            });

        } catch (error) {
            console.error('خطأ في تسجيل الخروج:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    
}

module.exports = new CustomerApiController();
