'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class CustomerArea extends Model {
    static associate(models) {
      // The associations are handled in the Customer and Area models
    }
  }
  
  CustomerArea.init({
    customer_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Customers',
        key: 'id'
      }
    },
    area_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Areas',
        key: 'id'
      }
    },
    address: {                         // ✅ أضف هذا الحقل الجديد
      type: DataTypes.STRING,
      allowNull: true
    },
    notes: DataTypes.TEXT
  }, {
    sequelize,
    modelName: 'CustomerArea',
    indexes: [
      {
        unique: true,
        fields: ['customer_id', 'area_id']
      }
    ]
  });
  
  return CustomerArea;
}; 