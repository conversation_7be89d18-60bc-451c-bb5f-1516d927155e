'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('Areas', {
      id: {
        type: Sequelize.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      countryId: {
        type: Sequelize.INTEGER,
        references: {
          model: 'Countries', // لازم يكون جدول Countries موجود أصلاً
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
        allowNull: true
      },
      name: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      notes: {
        type: Sequelize.TEXT,
        allowNull: true
      }
      // لا تستخدم createdAt, updatedAt إذا الموديل ما يستخدم timestamps
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('Areas');
  }
};
