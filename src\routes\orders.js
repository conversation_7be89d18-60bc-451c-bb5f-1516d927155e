const express = require('express');
const router = express.Router();
const ordersController = require('../controllers/OrdersController');

router.get('/', ordersController.index.bind(ordersController));
router.get('/create', ordersController.create.bind(ordersController));
router.post('/', ordersController.store.bind(ordersController));
router.get('/:id', ordersController.show.bind(ordersController));
//router.get('/:id/edit', ordersController.edit.bind(ordersController));
//router.post('/:id', ordersController.update.bind(ordersController));
//router.post('/:id/delete', ordersController.delete.bind(ordersController));

module.exports = router;
