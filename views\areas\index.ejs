<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Areas</h1>
        <a href="/admin/areas/create" class="btn btn-primary">Add New Area</a>
    </div>

    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Country</th>
                            <th>Stores</th>
                            <th>Customers</th>
                            <th>Notes</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <% areas.forEach(area => { %>
                            <tr>
                                <td><%= area.id %></td>
                                <td><%= area.name %></td>
                                <td><%= area.country ? area.country.name : 'N/A' %></td>
                                <!-- المتاجر -->
                                <td>
                                    <a href="/admin/areas/<%= area.id %>/stores">
                                    <%= area.stores ? area.stores.length : 0 %>
                                    </a>
                                </td>
                                
                                <!-- الزبائن -->
                                <td>
                                    <a href="/admin/areas/<%= area.id %>/customers">
                                    <%= area.customers ? area.customers.length : 0 %>
                                    </a>
                                </td>
                                <td><%= area.notes || '-' %></td>
                                <td class="action-buttons">
                                    <a href="/admin/areas/<%= area.id %>/edit" class="btn btn-sm btn-warning">
                                        <i class="fas fa-edit"></i> Edit
                                    </a>
                                    <form action="/admin/areas/<%= area.id %>/delete" method="POST" class="d-inline">
                                        <button type="submit" class="btn btn-sm btn-danger" 
                                                onclick="return confirm('Are you sure you want to delete this area? This will affect associated stores and customers.')">
                                            <i class="fas fa-trash"></i> Delete
                                        </button>
                                    </form>
                                </td>
                            </tr>
                        <% }); %>
                    </tbody>
                </table>
            </div>
            <% if (totalPages > 1) { %>
                <nav aria-label="Page navigation" class="d-flex justify-content-center mt-4">
                  <ul class="pagination">
                    <% if (currentPage > 1) { %>
                      <li class="page-item">
                        <a class="page-link" href="?page=<%= currentPage - 1 %>">السابق</a>
                      </li>
                    <% } else { %>
                      <li class="page-item disabled">
                        <span class="page-link">السابق</span>
                      </li>
                    <% } %>
                
                    <% for(let i = 1; i <= totalPages; i++) { %>
                      <li class="page-item <%= currentPage === i ? 'active' : '' %>">
                        <a class="page-link" href="?page=<%= i %>"><%= i %></a>
                      </li>
                    <% } %>
                
                    <% if (currentPage < totalPages) { %>
                      <li class="page-item">
                        <a class="page-link" href="?page=<%= currentPage + 1 %>">التالي</a>
                      </li>
                    <% } else { %>
                      <li class="page-item disabled">
                        <span class="page-link">التالي</span>
                      </li>
                    <% } %>
                  </ul>
                </nav>
                <% } %>
            <% if (areas.length === 0) { %>
                <div class="alert alert-info">No areas found.</div>
            <% } %>
        </div>
    </div>
</div> 