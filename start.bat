@echo off
chcp 65001 >nul
cls

echo 🏪 مرحباً بك في نظام إدارة المتاجر الذكي
echo ================================================

REM التحقق من وجود Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت. يرجى تثبيت Node.js أولاً
    echo يمكنك تحميله من: https://nodejs.org
    pause
    exit /b 1
)

REM التحقق من وجود npm
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm غير مثبت. يرجى تثبيت npm أولاً
    pause
    exit /b 1
)

echo ✅ Node.js مثبت بنجاح

REM التحقق من وجود ملف .env
if not exist .env (
    echo ⚠️  ملف .env غير موجود. سيتم إنشاؤه من .env.example
    if exist .env.example (
        copy .env.example .env >nul
        echo ✅ تم إنشاء ملف .env من .env.example
        echo 📝 يرجى تعديل ملف .env بالإعدادات المناسبة
    ) else (
        echo ❌ ملف .env.example غير موجود
        pause
        exit /b 1
    )
)

REM تثبيت التبعيات
echo 📦 جاري تثبيت التبعيات...
npm install
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت التبعيات
    pause
    exit /b 1
)

echo ✅ تم تثبيت التبعيات بنجاح

REM إنشاء المجلدات المطلوبة
echo 📁 إنشاء المجلدات المطلوبة...

if not exist uploads mkdir uploads
if not exist logs mkdir logs
if not exist backups mkdir backups
if not exist temp mkdir temp

echo ✅ تم إنشاء المجلدات بنجاح

REM قراءة PORT من ملف .env
set PORT=3001
for /f "tokens=2 delims==" %%a in ('findstr "^PORT=" .env 2^>nul') do set PORT=%%a

REM تشغيل التطبيق
echo 🚀 جاري تشغيل التطبيق...
echo ================================================
echo 🌐 سيتم تشغيل التطبيق على: http://localhost:%PORT%
echo 📊 لوحة تحكم الإدارة: http://localhost:%PORT%/admin
echo 🏪 واجهة المتاجر: http://localhost:%PORT%/store
echo 👤 واجهة العملاء: http://localhost:%PORT%/customers
echo ================================================
echo للإيقاف اضغط Ctrl+C
echo.

REM تشغيل التطبيق في وضع التطوير
npm run dev

pause
