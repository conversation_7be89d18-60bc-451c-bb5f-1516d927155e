/**
 * سكريبت تشخيص مشاكل السلة
 */

const { Customer, Product, Store, Cart, Category } = require('./src/models');

async function debugCart() {
    try {
        console.log('🔍 بدء تشخيص السلة...\n');

        // 1. التحقق من وجود العملاء
        const customers = await Customer.findAll({ limit: 1 });
        if (customers.length === 0) {
            console.log('❌ لا يوجد عملاء في قاعدة البيانات');
            return;
        }
        const customer = customers[0];
        console.log('✅ تم العثور على عميل:', customer.name);

        // 2. التحقق من وجود المنتجات مع المتاجر
        const products = await Product.findAll({
            include: [
                {
                    model: Store,
                    as: 'store',
                    attributes: ['id', 'name']
                }
            ],
            limit: 3
        });

        if (products.length === 0) {
            console.log('❌ لا توجد منتجات في قاعدة البيانات');
            return;
        }

        console.log('✅ المنتجات المتاحة:');
        products.forEach(product => {
            console.log(`  - ${product.name} (متجر: ${product.store?.name || 'غير محدد'}, ID: ${product.store?.id || 'غير محدد'})`);
        });

        // 3. إضافة منتج للسلة
        const CartService = require('./src/services/CartService');
        const firstProduct = products[0];

        if (!firstProduct.store || !firstProduct.store.id) {
            console.log('❌ المنتج الأول لا يحتوي على متجر صالح');
            return;
        }

        console.log('\n🛒 إضافة منتج للسلة...');
        await CartService.addToCart(customer.id, firstProduct.id, 1);
        console.log('✅ تم إضافة المنتج للسلة');

        // 4. جلب محتويات السلة
        console.log('\n📋 جلب محتويات السلة...');
        const cartData = await CartService.getCart(customer.id);
        
        console.log('📊 بيانات السلة:');
        console.log('  - عدد العناصر:', cartData.cartItems.length);
        console.log('  - عدد المتاجر:', cartData.storeGroups.length);
        console.log('  - إجمالي السعر:', cartData.summary.totalPrice);

        if (cartData.storeGroups.length > 0) {
            console.log('\n🏪 تفاصيل المتاجر:');
            cartData.storeGroups.forEach((group, index) => {
                console.log(`  متجر ${index + 1}:`);
                console.log(`    - ID: ${group.store?.id || 'غير محدد'}`);
                console.log(`    - الاسم: ${group.store?.name || 'غير محدد'}`);
                console.log(`    - عدد العناصر: ${group.itemsCount}`);
                console.log(`    - المجموع الفرعي: ${group.subtotal}`);
            });
        }

        // 5. اختبار إنشاء طلب
        console.log('\n📦 اختبار إنشاء طلب...');
        
        if (cartData.storeGroups.length === 0) {
            console.log('❌ لا توجد مجموعات متاجر في السلة');
            return;
        }

        const firstStoreGroup = cartData.storeGroups[0];
        if (!firstStoreGroup.store || !firstStoreGroup.store.id) {
            console.log('❌ المتجر الأول لا يحتوي على ID صالح');
            console.log('بيانات المتجر:', JSON.stringify(firstStoreGroup.store, null, 2));
            return;
        }

        console.log('✅ بيانات المتجر صالحة للطلب');
        console.log(`  - ID المتجر: ${firstStoreGroup.store.id}`);
        console.log(`  - اسم المتجر: ${firstStoreGroup.store.name}`);

        // 6. تنظيف السلة
        console.log('\n🧹 تنظيف السلة...');
        await CartService.clearCart(customer.id);
        console.log('✅ تم تنظيف السلة');

        console.log('\n🎉 انتهى التشخيص بنجاح!');

    } catch (error) {
        console.error('❌ خطأ في التشخيص:', error);
        console.error('تفاصيل الخطأ:', error.stack);
    }
}

// تشغيل التشخيص
debugCart().then(() => {
    console.log('\n✅ تم الانتهاء من التشخيص');
    process.exit(0);
}).catch(error => {
    console.error('❌ خطأ عام:', error);
    process.exit(1);
});
