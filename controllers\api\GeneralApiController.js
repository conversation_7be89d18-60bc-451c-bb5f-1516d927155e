const { Category, Area, Country, Store, Product } = require('../../models');
const { Op } = require('sequelize');

class GeneralApiController {

    /**
     * الحصول على جميع الفئات
     * GET /api/categories
     */
    async getCategories(req, res) {
        try {
            const { includeCount = false } = req.query;

            let includeArray = [];
            
            if (includeCount === 'true') {
                includeArray.push({
                    model: Product,
                    attributes: [],
                    where: { isActive: true },
                    required: false
                });
            }

            const categories = await Category.findAll({
                include: includeArray,
                attributes: includeCount === 'true' ? 
                    ['id', 'name', 'description', 'image', 'createdAt', [
                        Category.sequelize.fn('COUNT', Category.sequelize.col('Products.id')), 
                        'productsCount'
                    ]] : 
                    ['id', 'name', 'description', 'image', 'createdAt'],
                group: includeCount === 'true' ? ['Category.id'] : undefined,
                order: [['name', 'ASC']]
            });

            const formattedCategories = categories.map(category => ({
                id: category.id,
                name: category.name,
                description: category.description,
                image: category.image ? `/uploads/${category.image}` : null,
                productsCount: includeCount === 'true' ? parseInt(category.dataValues.productsCount) || 0 : undefined,
                createdAt: category.createdAt
            }));

            res.json({
                success: true,
                message: 'تم جلب الفئات بنجاح',
                data: { categories: formattedCategories }
            });

        } catch (error) {
            console.error('خطأ في جلب الفئات:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    /**
     * الحصول على فئة واحدة مع منتجاتها
     * GET /api/categories/:id
     */
    async getCategoryById(req, res) {
        try {
            const { id } = req.params;
            const { page = 1, limit = 10 } = req.query;

            const category = await Category.findByPk(id);
            if (!category) {
                return res.status(404).json({
                    success: false,
                    message: 'الفئة غير موجودة',
                    data: null
                });
            }

            // جلب المنتجات مع الصفحات
            const offset = (page - 1) * limit;
            const { count, rows: products } = await Product.findAndCountAll({
                where: {
                    categoryId: id,
                    isActive: true
                },
                include: [
                    {
                        model: Store,
                        attributes: ['id', 'name', 'rating']
                    }
                ],
                limit: parseInt(limit),
                offset: parseInt(offset),
                order: [['createdAt', 'DESC']]
            });

            const formattedProducts = products.map(product => ({
                id: product.id,
                name: product.name,
                description: product.description,
                price: parseFloat(product.price),
                image: product.image ? `/uploads/${product.image}` : null,
                store: product.Store ? {
                    id: product.Store.id,
                    name: product.Store.name,
                    rating: product.Store.rating || 0
                } : null
            }));

            res.json({
                success: true,
                message: 'تم جلب بيانات الفئة بنجاح',
                data: {
                    category: {
                        id: category.id,
                        name: category.name,
                        description: category.description,
                        image: category.image ? `/uploads/${category.image}` : null
                    },
                    products: formattedProducts,
                    pagination: {
                        currentPage: parseInt(page),
                        totalPages: Math.ceil(count / limit),
                        totalItems: count,
                        itemsPerPage: parseInt(limit)
                    }
                }
            });

        } catch (error) {
            console.error('خطأ في جلب بيانات الفئة:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    /**
     * الحصول على جميع المدن
     * GET /api/countries
     */
    async getCountries(req, res) {
        try {
            const { includeAreas = false } = req.query;

            let includeArray = [];
            if (includeAreas === 'true') {
                includeArray.push({
                    model: Area,
                    attributes: ['id', 'name']
                });
            }

            const countries = await Country.findAll({
                include: includeArray,
                attributes: ['id', 'name', 'createdAt'],
                order: [['name', 'ASC']]
            });

            const formattedCountries = countries.map(country => ({
                id: country.id,
                name: country.name,
                areas: includeAreas === 'true' && country.Areas ? 
                    country.Areas.map(area => ({
                        id: area.id,
                        name: area.name
                    })) : undefined,
                createdAt: country.createdAt
            }));

            res.json({
                success: true,
                message: 'تم جلب المدن بنجاح',
                data: { countries: formattedCountries }
            });

        } catch (error) {
            console.error('خطأ في جلب المدن:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    /**
     * الحصول على جميع المناطق
     * GET /api/areas
     */
    async getAreas(req, res) {
        try {
            const { countryId = null, includeStoresCount = false } = req.query;

            const whereClause = {};
            if (countryId) {
                whereClause.countryId = countryId;
            }

            let includeArray = [
                {
                    model: Country,
                    attributes: ['id', 'name']
                }
            ];

            if (includeStoresCount === 'true') {
                includeArray.push({
                    model: Store,
                    attributes: [],
                    where: { isActive: true },
                    required: false
                });
            }

            const areas = await Area.findAll({
                where: whereClause,
                include: includeArray,
                attributes: includeStoresCount === 'true' ? 
                    ['id', 'name', 'countryId', 'createdAt', [
                        Area.sequelize.fn('COUNT', Area.sequelize.col('Stores.id')), 
                        'storesCount'
                    ]] : 
                    ['id', 'name', 'countryId', 'createdAt'],
                group: includeStoresCount === 'true' ? ['Area.id', 'Country.id'] : undefined,
                order: [['name', 'ASC']]
            });

            const formattedAreas = areas.map(area => ({
                id: area.id,
                name: area.name,
                country: area.Country ? {
                    id: area.Country.id,
                    name: area.Country.name
                } : null,
                storesCount: includeStoresCount === 'true' ? 
                    parseInt(area.dataValues.storesCount) || 0 : undefined,
                createdAt: area.createdAt
            }));

            res.json({
                success: true,
                message: 'تم جلب المناطق بنجاح',
                data: { areas: formattedAreas }
            });

        } catch (error) {
            console.error('خطأ في جلب المناطق:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    /**
     * الحصول على منطقة واحدة مع متاجرها
     * GET /api/areas/:id
     */
    async getAreaById(req, res) {
        try {
            const { id } = req.params;
            const { page = 1, limit = 10 } = req.query;

            const area = await Area.findByPk(id, {
                include: [
                    {
                        model: Country,
                        attributes: ['id', 'name']
                    }
                ]
            });

            if (!area) {
                return res.status(404).json({
                    success: false,
                    message: 'المنطقة غير موجودة',
                    data: null
                });
            }

            // جلب المتاجر مع الصفحات
            const offset = (page - 1) * limit;
            const { count, rows: stores } = await Store.findAndCountAll({
                where: {
                    areaId: id,
                    isActive: true
                },
                attributes: ['id', 'name', 'description', 'image', 'rating'],
                limit: parseInt(limit),
                offset: parseInt(offset),
                order: [['rating', 'DESC'], ['createdAt', 'DESC']]
            });

            const formattedStores = stores.map(store => ({
                id: store.id,
                name: store.name,
                description: store.description,
                image: store.image ? `/uploads/${store.image}` : null,
                rating: store.rating || 0
            }));

            res.json({
                success: true,
                message: 'تم جلب بيانات المنطقة بنجاح',
                data: {
                    area: {
                        id: area.id,
                        name: area.name,
                        country: area.Country ? {
                            id: area.Country.id,
                            name: area.Country.name
                        } : null
                    },
                    stores: formattedStores,
                    pagination: {
                        currentPage: parseInt(page),
                        totalPages: Math.ceil(count / limit),
                        totalItems: count,
                        itemsPerPage: parseInt(limit)
                    }
                }
            });

        } catch (error) {
            console.error('خطأ في جلب بيانات المنطقة:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    /**
     * البحث العام
     * GET /api/search
     */
    async globalSearch(req, res) {
        try {
            const { q = '', type = 'all', limit = 5 } = req.query;

            if (!q.trim()) {
                return res.status(400).json({
                    success: false,
                    message: 'نص البحث مطلوب',
                    data: null
                });
            }

            const results = {};

            // البحث في المتاجر
            if (type === 'all' || type === 'stores') {
                const stores = await Store.findAll({
                    where: {
                        isActive: true,
                        [Op.or]: [
                            { name: { [Op.like]: `%${q}%` } },
                            { description: { [Op.like]: `%${q}%` } }
                        ]
                    },
                    attributes: ['id', 'name', 'description', 'image', 'rating'],
                    limit: parseInt(limit),
                    order: [['rating', 'DESC']]
                });

                results.stores = stores.map(store => ({
                    id: store.id,
                    name: store.name,
                    description: store.description,
                    image: store.image ? `/uploads/${store.image}` : null,
                    rating: store.rating || 0,
                    type: 'store'
                }));
            }

            // البحث في المنتجات
            if (type === 'all' || type === 'products') {
                const products = await Product.findAll({
                    where: {
                        isActive: true,
                        [Op.or]: [
                            { name: { [Op.like]: `%${q}%` } },
                            { description: { [Op.like]: `%${q}%` } }
                        ]
                    },
                    include: [
                        {
                            model: Store,
                            attributes: ['id', 'name', 'rating']
                        },
                        {
                            model: Category,
                            attributes: ['id', 'name']
                        }
                    ],
                    attributes: ['id', 'name', 'description', 'price', 'image'],
                    limit: parseInt(limit),
                    order: [['createdAt', 'DESC']]
                });

                results.products = products.map(product => ({
                    id: product.id,
                    name: product.name,
                    description: product.description,
                    price: parseFloat(product.price),
                    image: product.image ? `/uploads/${product.image}` : null,
                    store: product.Store ? {
                        id: product.Store.id,
                        name: product.Store.name,
                        rating: product.Store.rating || 0
                    } : null,
                    category: product.Category ? product.Category.name : null,
                    type: 'product'
                }));
            }

            // البحث في الفئات
            if (type === 'all' || type === 'categories') {
                const categories = await Category.findAll({
                    where: {
                        name: { [Op.like]: `%${q}%` }
                    },
                    attributes: ['id', 'name', 'description', 'image'],
                    limit: parseInt(limit),
                    order: [['name', 'ASC']]
                });

                results.categories = categories.map(category => ({
                    id: category.id,
                    name: category.name,
                    description: category.description,
                    image: category.image ? `/uploads/${category.image}` : null,
                    type: 'category'
                }));
            }

            res.json({
                success: true,
                message: 'تم البحث بنجاح',
                data: {
                    query: q,
                    results: results
                }
            });

        } catch (error) {
            console.error('خطأ في البحث العام:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }
}

module.exports = new GeneralApiController();
