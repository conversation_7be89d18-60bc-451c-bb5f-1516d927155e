<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Create New Customer</h1>
        <a href="/admin/customers" class="btn btn-secondary">Back to Customers</a>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-body">
                    <form action="/admin/customers" method="POST">
                        <div class="mb-3">
                            <label for="name" class="form-label">Customer Name</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>

                        <div class="mb-3">
                            <label for="password" class="form-label">Password</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>

                        <div class="mb-3">
                            <label for="barcode" class="form-label">Barcode</label>
                            <input type="text" class="form-control" id="barcode" name="barcode" required>
                        </div>

                        <div class="mb-3">
                            <label for="phoneNumber" class="form-label">Phone Number</label>
                            <input type="tel" class="form-control" id="phoneNumber" name="phoneNumber" required>
                        </div>

                        <div class="mb-3">
                            <label for="discountRate" class="form-label">Discount Rate (%)</label>
                            <input type="number" step="0.01" min="0" max="100" class="form-control" 
                                   id="discountRate" name="discountRate" value="0" required>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Areas</label>
                            <div class="border rounded p-3">
                                <% areas.forEach(area => { %>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" 
                                               name="areas[]" value="<%= area.id %>" 
                                               id="area<%= area.id %>">
                                        <label class="form-check-label" for="area<%= area.id %>">
                                            <%= area.name %>
                                        </label>
                                    </div>
                                <% }); %>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="notes" class="form-label">Notes</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                        </div>

                        <button type="submit" class="btn btn-primary">Create Customer</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div> 