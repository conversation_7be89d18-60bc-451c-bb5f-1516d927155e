<div class="container">
    <h1 class="mb-4">Dashboard</h1>

    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <h5 class="card-title">Stores</h5>
                    <h2 class="card-text"><%= storeCount %></h2>
                    <a href="/stores" class="text-white">View All <i class="fas fa-arrow-right"></i></a>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <h5 class="card-title">Customers</h5>
                    <h2 class="card-text"><%= customerCount %></h2>
                    <a href="/customers" class="text-white">View All <i class="fas fa-arrow-right"></i></a>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <h5 class="card-title">Orders</h5>
                    <h2 class="card-text"><%= orderCount %></h2>
                    <a href="/orders" class="text-white">View All <i class="fas fa-arrow-right"></i></a>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <h5 class="card-title">Products</h5>
                    <h2 class="card-text"><%= productCount %></h2>
                    <a href="/products" class="text-white">View All <i class="fas fa-arrow-right"></i></a>
                </div>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Recent Orders</h5>
            <a href="/orders" class="btn btn-primary btn-sm">View All Orders</a>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Customer</th>
                            <th>Store</th>
                            <th>Total Price</th>
                            <th>Status</th>
                            <th>Created At</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <% recentOrders.forEach(order => { %>
                            <tr>
                                <td><%= order.id %></td>
                                <td><%= order.customer ? order.customer.name : 'N/A' %></td>
                                <td><%= order.store ? order.store.userName : 'N/A' %></td>
                                <td>$<%= order.totalPrice.toFixed(2) %></td>
                                <td>
                                    <span class="badge bg-<%= order.status === 'pending' ? 'warning' : 
                                                            order.status === 'completed' ? 'success' : 
                                                            order.status === 'cancelled' ? 'danger' : 'secondary' %>">
                                        <%= order.status.charAt(0).toUpperCase() + order.status.slice(1) %>
                                    </span>
                                </td>
                                <td><%= new Date(order.createdAt).toLocaleString() %></td>
                                <td>
                                    <a href="/orders/<%= order.id %>" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                </td>
                            </tr>
                        <% }); %>
                    </tbody>
                </table>
            </div>

            <% if (recentOrders.length === 0) { %>
                <div class="alert alert-info">No recent orders found.</div>
            <% } %>
        </div>
    </div>
</div>

<style>
.card {
    transition: transform 0.2s;
}
.card:hover {
    transform: translateY(-5px);
}
.bg-primary, .bg-success, .bg-warning, .bg-info {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}
</style> 