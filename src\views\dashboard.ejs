<% layout('layouts/main') -%>

<div class="container">
    <h1 class="mb-4 text-dark">🏪 نظام إدارة المتاجر الذكي</h1>

    <!-- Welcome Message -->
    <div class="alert alert-info">
        <h4 class="text-dark">مرحباً بك في نظام إدارة المتاجر الذكي!</h4>
        <p class="text-dark">منصة شاملة لإدارة المتاجر والطلبات والعملاء</p>
        <div class="mt-3">
            <a href="/admin/auth/login" class="btn btn-primary me-2">
                <i class="fas fa-user-shield"></i> دخول الإدارة
            </a>
            <a href="/store/auth/login" class="btn btn-success me-2">
                <i class="fas fa-store"></i> دخول المتجر
            </a>
            <a href="/customers/auth/login" class="btn btn-info">
                <i class="fas fa-user"></i> دخول العميل
            </a>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <h5 class="card-title">المتاجر</h5>
                    <h2 class="card-text"><%= storeCount || 0 %></h2>
                    <a href="/admin/stores" class="text-white">عرض الكل <i class="fas fa-arrow-left"></i></a>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <h5 class="card-title">العملاء</h5>
                    <h2 class="card-text"><%= customerCount || 0 %></h2>
                    <a href="/admin/customers" class="text-white">عرض الكل <i class="fas fa-arrow-left"></i></a>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <h5 class="card-title">الطلبات</h5>
                    <h2 class="card-text"><%= orderCount || 0 %></h2>
                    <a href="/admin/orders" class="text-white">عرض الكل <i class="fas fa-arrow-left"></i></a>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <h5 class="card-title">المنتجات</h5>
                    <h2 class="card-text"><%= productCount || 0 %></h2>
                    <a href="/store/products" class="text-white">عرض الكل <i class="fas fa-arrow-left"></i></a>
                </div>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Recent Orders</h5>
            <a href="/orders" class="btn btn-primary btn-sm">View All Orders</a>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Customer</th>
                            <th>Store</th>
                            <th>Total Price</th>
                            <th>Status</th>
                            <th>Created At</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <% recentOrders.forEach(order => { %>
                            <tr>
                                <td><%= order.id %></td>
                                <td><%= order.customer ? order.customer.name : 'N/A' %></td>
                                <td><%= order.store ? order.store.userName : 'N/A' %></td>
                                <td>$<%= order.totalPrice.toFixed(2) %></td>
                                <td>
                                    <span class="badge bg-<%= order.status === 'pending' ? 'warning' :
                                                            order.status === 'completed' ? 'success' :
                                                            order.status === 'cancelled' ? 'danger' : 'secondary' %>">
                                        <%= order.status.charAt(0).toUpperCase() + order.status.slice(1) %>
                                    </span>
                                </td>
                                <td><%= new Date(order.createdAt).toLocaleString() %></td>
                                <td>
                                    <a href="/orders/<%= order.id %>" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                </td>
                            </tr>
                        <% }); %>
                    </tbody>
                </table>
            </div>

            <% if (recentOrders.length === 0) { %>
                <div class="alert alert-info">No recent orders found.</div>
            <% } %>
        </div>
    </div>
</div>

<style>
/* تأكد من ظهور النص بشكل صحيح */
.text-dark {
    color: #343a40 !important;
}

h1, h2, h3, h4, h5, h6 {
    color: #343a40 !important;
}

.alert * {
    color: #343a40 !important;
}

.table th,
.table td {
    color: #343a40 !important;
}

.card {
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-5px);
}

.bg-primary, .bg-success, .bg-warning, .bg-info {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* إخفاء شاشة التحميل */
.loading-overlay {
    display: none !important;
}

/* تحسين الأزرار */
.btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
}
</style>