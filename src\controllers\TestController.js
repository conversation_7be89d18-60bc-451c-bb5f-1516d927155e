const { Store, Customer } = require('../models');

class TestController {
    // اختبار صفحة المتاجر بدون بحث وفلتر
    async testStores(req, res) {
        try {
            const stores = await Store.findAll({
                limit: 10
            });
            
            res.json({
                success: true,
                count: stores.length,
                stores: stores.map(store => ({
                    id: store.id,
                    name: store.name,
                    status: store.status
                }))
            });
        } catch (error) {
            console.error('Test stores error:', error);
            res.status(500).json({
                success: false,
                error: error.message,
                stack: error.stack
            });
        }
    }

    // اختبار صفحة العملاء بدون بحث وفلتر
    async testCustomers(req, res) {
        try {
            const customers = await Customer.findAll({
                limit: 10
            });
            
            res.json({
                success: true,
                count: customers.length,
                customers: customers.map(customer => ({
                    id: customer.id,
                    name: customer.name,
                    status: customer.status
                }))
            });
        } catch (error) {
            console.error('Test customers error:', error);
            res.status(500).json({
                success: false,
                error: error.message,
                stack: error.stack
            });
        }
    }

    // اختبار البحث والفلتر
    async testSearchFilter(req, res) {
        try {
            const { buildSearchAndFilter } = require('../utils/searchFilter');
            
            const searchFields = {
                text: ['name'],
                numeric: ['id']
            };

            const filterFields = {
                status: { type: 'exact' }
            };

            const whereClause = buildSearchAndFilter(req.query, searchFields, filterFields);
            
            res.json({
                success: true,
                query: req.query,
                whereClause: whereClause
            });
        } catch (error) {
            console.error('Test search filter error:', error);
            res.status(500).json({
                success: false,
                error: error.message,
                stack: error.stack
            });
        }
    }
}

module.exports = new TestController();
