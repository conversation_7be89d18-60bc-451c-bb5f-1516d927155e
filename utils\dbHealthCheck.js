// src/utils/dbHealthCheck.js

const { sequelize } = require('../models');

async function checkDatabaseHealth() {
  try {
    // التحقق من الاتصال
    await sequelize.authenticate();
    console.log('✅ Database connection is healthy');
    
    // التحقق من الجداول الأساسية
    const tables = await sequelize.getQueryInterface().showAllTables();
    const requiredTables = ['Users', 'Products', 'Orders', 'Stores', 'Customers'];
    const missingTables = requiredTables.filter(table => 
      !tables.includes(table.toLowerCase())
    );
    
    if (missingTables.length > 0) {
      console.warn('⚠️ Missing essential tables:', missingTables);
      return {
        healthy: false,
        message: `Missing essential tables: ${missingTables.join(', ')}`
      };
    }
    
    // اختبار استعلام بسيط
    try {
      await sequelize.query('SELECT 1', { type: sequelize.QueryTypes.SELECT });
    } catch (queryError) {
      console.error('❌ Database query test failed:', queryError.message);
      return {
        healthy: false,
        message: 'Database query test failed'
      };
    }
    
    return {
      healthy: true,
      message: 'Database is fully operational'
    };
  } catch (error) {
    console.error('❌ Database health check failed:', error.message);
    return {
      healthy: false,
      message: error.message
    };
  }
}

module.exports = checkDatabaseHealth;
