<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Edit Area</h1>
        <a href="/admin/areas" class="btn btn-secondary">Back to Areas</a>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-body">
                    <form action="/admin/areas/<%= area.id %>" method="POST">
                        <input type="hidden" name="_method" value="PUT">
                        
                        <div class="mb-3">
                            <label for="name" class="form-label">Area Name</label>
                            <input type="text" class="form-control" id="name" name="name" 
                                   value="<%= area.name %>" required>
                        </div>

                        <div class="mb-3">
                            <label for="countryId" class="form-label">Country</label>
                            <select class="form-control" id="countryId" name="countryId" required>
                                <option value="">Select a Country</option>
                                <% countries.forEach(country => { %>
                                    <option value="<%= country.id %>" 
                                            <%= country.id === area.countryId ? 'selected' : '' %>>
                                        <%= country.name %>
                                    </option>
                                <% }); %>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="notes" class="form-label">Notes</label>
                            <textarea class="form-control" id="notes" name="notes" 
                                      rows="3"><%= area.notes || '' %></textarea>
                        </div>

                        <button type="submit" class="btn btn-primary">Update Area</button>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Associated Stores</h5>
                </div>
                <div class="card-body">
                    <% if (area.stores && area.stores.length > 0) { %>
                        <div class="list-group">
                            <% area.stores.forEach(store => { %>
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-0"><%= store.userName %></h6>
                                        <small class="text-muted"><%= store.address %></small>
                                    </div>
                                    <a href="/admin/stores/<%= store.id %>/edit" class="btn btn-sm btn-warning">
                                        <i class="fas fa-edit"></i> Edit
                                    </a>
                                </div>
                            <% }); %>
                        </div>
                    <% } else { %>
                        <div class="alert alert-info">No stores in this area.</div>
                    <% } %>
                    
                    <div class="mt-3">
                        <a href="/admin/stores/create?areaId=<%= area.id %>" class="btn btn-success">
                            <i class="fas fa-plus"></i> Add New Store
                        </a>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Associated Customers</h5>
                </div>
                <div class="card-body">
                    <% if (area.customers && area.customers.length > 0) { %>
                        <div class="list-group">
                            <% area.customers.forEach(customer => { %>
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-0"><%= customer.name %></h6>
                                        <small class="text-muted"><%= customer.phoneNumber || 'No phone' %></small>
                                    </div>
                                    <a href="/admin/customers/<%= customer.id %>/edit" class="btn btn-sm btn-warning">
                                        <i class="fas fa-edit"></i> Edit
                                    </a>
                                </div>
                            <% }); %>
                        </div>
                    <% } else { %>
                        <div class="alert alert-info">No customers in this area.</div>
                    <% } %>
                    
                    <div class="mt-3">
                        <a href="/admin/customers/create?areaId=<%= area.id %>" class="btn btn-success">
                            <i class="fas fa-plus"></i> Add New Customer
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div> 