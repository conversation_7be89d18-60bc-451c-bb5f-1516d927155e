const { Store, Customer, Order, Product } = require('../models');

class DashboardController {
    async index(req, res) {
        try {
            const [
                storeCount,
                customerCount,
                orderCount,
                productCount,
                recentOrders
            ] = await Promise.all([
                Store.count(),
                Customer.count(),
                Order.count(),
                Product.count(),
                Order.findAll({
                    limit: 5,
                    order: [['createdAt', 'DESC']],
                    include: [
                        { model: Customer, as: 'customer' },
                        { model: Store, as: 'store' }
                    ]
                })
            ]);

            res.render('dashboard', {
                storeCount,
                customerCount,
                orderCount,
                productCount,
                recentOrders
            });
        } catch (error) {
            res.status(500).render('error', { error });
        }
    }
}

module.exports = new DashboardController(); 