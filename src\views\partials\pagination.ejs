<!-- مكون الـ Pagination المحسن -->
<% if (typeof pagination !== 'undefined' && pagination && pagination.totalPages > 1) { %>
    <nav aria-label="Page navigation" class="mt-4">
        <div class="row align-items-center">
            <div class="col-md-6">
                <p class="pagination-info mb-0 text-muted">
                    عرض <%= pagination.startItem %> إلى <%= pagination.endItem %> 
                    من أصل <%= pagination.totalCount %> نتيجة
                    (<%= pagination.totalPages %> صفحة)
                </p>
            </div>
            <div class="col-md-6">
                <ul class="pagination justify-content-end mb-0">
                    <!-- الصفحة الأولى -->
                    <% if (pagination.currentPage > 1) { %>
                        <li class="page-item">
                            <a class="page-link" href="<%= buildPageUrl(1) %>" title="الصفحة الأولى">
                                <i class="fas fa-angle-double-right"></i>
                            </a>
                        </li>
                    <% } %>

                    <!-- الصفحة السابقة -->
                    <% if (pagination.hasPrevPage) { %>
                        <li class="page-item">
                            <a class="page-link" href="<%= buildPageUrl(pagination.prevPage) %>" title="الصفحة السابقة">
                                <i class="fas fa-angle-right"></i>
                            </a>
                        </li>
                    <% } %>

                    <!-- أرقام الصفحات -->
                    <% 
                        let startPage = Math.max(1, pagination.currentPage - 2);
                        let endPage = Math.min(pagination.totalPages, pagination.currentPage + 2);
                        
                        // تأكد من عرض 5 صفحات على الأقل إذا كان ممكناً
                        if (endPage - startPage < 4) {
                            if (startPage === 1) {
                                endPage = Math.min(pagination.totalPages, startPage + 4);
                            } else if (endPage === pagination.totalPages) {
                                startPage = Math.max(1, endPage - 4);
                            }
                        }
                    %>

                    <!-- نقاط البداية إذا كانت الصفحة الحالية بعيدة عن البداية -->
                    <% if (startPage > 1) { %>
                        <li class="page-item">
                            <a class="page-link" href="<%= buildPageUrl(1) %>">1</a>
                        </li>
                        <% if (startPage > 2) { %>
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                        <% } %>
                    <% } %>

                    <!-- أرقام الصفحات الرئيسية -->
                    <% for (let i = startPage; i <= endPage; i++) { %>
                        <li class="page-item <%= (i === pagination.currentPage) ? 'active' : '' %>">
                            <% if (i === pagination.currentPage) { %>
                                <span class="page-link">
                                    <%= i %>
                                    <span class="sr-only">(current)</span>
                                </span>
                            <% } else { %>
                                <a class="page-link" href="<%= buildPageUrl(i) %>"><%= i %></a>
                            <% } %>
                        </li>
                    <% } %>

                    <!-- نقاط النهاية إذا كانت الصفحة الحالية بعيدة عن النهاية -->
                    <% if (endPage < pagination.totalPages) { %>
                        <% if (endPage < pagination.totalPages - 1) { %>
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                        <% } %>
                        <li class="page-item">
                            <a class="page-link" href="<%= buildPageUrl(pagination.totalPages) %>"><%= pagination.totalPages %></a>
                        </li>
                    <% } %>

                    <!-- الصفحة التالية -->
                    <% if (pagination.hasNextPage) { %>
                        <li class="page-item">
                            <a class="page-link" href="<%= buildPageUrl(pagination.nextPage) %>" title="الصفحة التالية">
                                <i class="fas fa-angle-left"></i>
                            </a>
                        </li>
                    <% } %>

                    <!-- الصفحة الأخيرة -->
                    <% if (pagination.currentPage < pagination.totalPages) { %>
                        <li class="page-item">
                            <a class="page-link" href="<%= buildPageUrl(pagination.totalPages) %>" title="الصفحة الأخيرة">
                                <i class="fas fa-angle-double-left"></i>
                            </a>
                        </li>
                    <% } %>
                </ul>
            </div>
        </div>

        <!-- انتقال سريع للصفحة -->
        <div class="row mt-3">
            <div class="col-md-6">
                <div class="input-group input-group-sm" style="max-width: 200px;">
                    <div class="input-group-prepend">
                        <span class="input-group-text">انتقال للصفحة:</span>
                    </div>
                    <input type="number" 
                           class="form-control" 
                           id="gotoPage" 
                           min="1" 
                           max="<%= pagination.totalPages %>" 
                           value="<%= pagination.currentPage %>"
                           onkeypress="if(event.key==='Enter') gotoPage()">
                    <div class="input-group-append">
                        <button class="btn btn-outline-secondary" type="button" onclick="gotoPage()">
                            <i class="fas fa-arrow-left"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-md-6 text-right">
                <small class="text-muted">
                    استخدم مفاتيح الأسهم للتنقل بين الصفحات
                </small>
            </div>
        </div>
    </nav>

    <script>
    // دالة الانتقال السريع للصفحة
    function gotoPage() {
        const pageInput = document.getElementById('gotoPage');
        const pageNum = parseInt(pageInput.value);
        const maxPages = <%= pagination.totalPages %>;
        
        if (pageNum >= 1 && pageNum <= maxPages) {
            window.location.href = buildPageUrl(pageNum);
        } else {
            alert('رقم الصفحة غير صحيح. يجب أن يكون بين 1 و ' + maxPages);
            pageInput.value = <%= pagination.currentPage %>;
        }
    }

    // دالة بناء رابط الصفحة
    function buildPageUrl(pageNum) {
        const url = new URL(window.location);
        url.searchParams.set('page', pageNum);
        return url.toString();
    }

    // التنقل بالكيبورد
    document.addEventListener('keydown', function(e) {
        // تأكد من أن المستخدم لا يكتب في حقل نص
        if (document.activeElement.tagName === 'INPUT' || document.activeElement.tagName === 'TEXTAREA') {
            return;
        }

        const currentPage = <%= pagination.currentPage %>;
        const totalPages = <%= pagination.totalPages %>;

        switch(e.key) {
            case 'ArrowLeft':
                if (currentPage < totalPages) {
                    window.location.href = buildPageUrl(currentPage + 1);
                }
                break;
            case 'ArrowRight':
                if (currentPage > 1) {
                    window.location.href = buildPageUrl(currentPage - 1);
                }
                break;
            case 'Home':
                if (currentPage > 1) {
                    window.location.href = buildPageUrl(1);
                }
                break;
            case 'End':
                if (currentPage < totalPages) {
                    window.location.href = buildPageUrl(totalPages);
                }
                break;
        }
    });
    </script>

    <style>
    .pagination-info {
        font-size: 0.9em;
    }

    .pagination .page-link {
        border-radius: 0.25rem;
        margin: 0 2px;
        border: 1px solid #dee2e6;
    }

    .pagination .page-item.active .page-link {
        background-color: #007bff;
        border-color: #007bff;
    }

    .pagination .page-link:hover {
        background-color: #e9ecef;
        border-color: #adb5bd;
    }

    .pagination .page-item.disabled .page-link {
        color: #6c757d;
        background-color: #fff;
        border-color: #dee2e6;
    }

    @media (max-width: 768px) {
        .pagination {
            justify-content: center !important;
        }
        
        .pagination-info {
            text-align: center;
            margin-bottom: 1rem;
        }
    }
    </style>
<% } %>

<%
// دالة مساعدة لبناء رابط الصفحة (متاحة في الـ template)
function buildPageUrl(pageNum) {
    const url = new URL(currentUrl || 'http://localhost' + originalUrl);
    url.searchParams.set('page', pageNum);
    return url.pathname + url.search;
}
%>
