<div class="container mt-4">
  <h2 class="fw-bold mb-4">Your Orders</h2>

  <% if (orders.length === 0) { %>
    <div class="alert alert-info">You have no orders yet.</div>
  <% } else { %>
    <% orders.forEach(order => { %>
      <div class="card mb-4 shadow-sm">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
          <span><strong>Order #<%= order.id %></strong></span>
          <span class="badge 
            <%= order.status === 'delivered' ? 'bg-success' : 
                order.status === 'cancelled' ? 'bg-danger' : 
                order.status === 'pending' ? 'bg-warning text-dark' : 
                'bg-secondary' %>">
            <%= order.status %>
          </span>
        </div>
        <div class="card-body">
          <ul class="list-group">
            <% order.orderDetails.forEach(item => { %>
              <li class="list-group-item d-flex justify-content-between align-items-center">
                <div>
                  <strong><%= item.product.name %></strong><br>
                  <small>Store: <%= item.product.store.name %></small>
                </div>
                <div>
                  <span>Qty: <%= item.quantity %></span><br>
                  <span class="text-success">$<%= item.totalPrice.toFixed(2) %></span>
                </div>
              </li>
            <% }) %>
          </ul>
          <div class="text-end mt-3">
            <strong>Total:</strong> 
            $<%= order.totalPrice.toFixed(2) %>
          </div>
        </div>
      </div>
    <% }); %>
  <% } %>
</div>
