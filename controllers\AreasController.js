const BaseController = require('./BaseController');
const { Area, Country, Customer, Store, Category, Order, DeliveryPerson } = require('../models');

class AreasController extends BaseController {
    constructor() {
        super(Area, 'areas');
    }

    // عرض قائمة المناطق مع بيانات الدولة والعملاء المرتبطين
    async index(req, res) {
        const page = parseInt(req.query.page) || 1;
        const limit = 20;
        const offset = (page - 1) * limit;
    
        try {
            const { count, rows: areas } = await Area.findAndCountAll({
                include: [
                    { model: Country, as: 'country' },
                    { model: Customer, as: 'customers', through: 'CustomerArea' },
                    { model: Store, as: 'stores' }
                ],
                distinct: true, // ← هذا هو المفتاح لمنع العدّ المكرر
                limit,
                offset,
                order: [['createdAt', 'DESC']]
            });
    
            const totalPages = Math.ceil(count / limit);
            console.log('Total areas:', count);
            console.log('Total pages:', totalPages);
            res.render('areas/index', {
                areas,
                currentPage: page,
                totalPages
            });
    
        } catch (error) {
            console.error('Error fetching areas:', error);
            res.status(500).render('error', { error: { message: 'Unable to fetch areas' } });
        }
    }
    
// عرض المتاجر التابعة لمنطقة
async areaStores(req, res) {
    const { id } = req.params;
    const page = parseInt(req.query.page) || 1;
    const limit = 10;
    const offset = (page - 1) * limit;
  
    try {
      const area = await Area.findByPk(id);
  
      if (!area) {
        return res.status(404).render('error', { error: { message: 'Area not found' } });
      }
  
      const { count, rows: stores } = await Store.findAndCountAll({
        include: [
            { model: Category, as: 'categories' },
            { model: Area, as: 'area' },
          ],
        where: { areaId: id },
        limit,
        offset,
        order: [['createdAt', 'DESC']]
      });
  
      const totalPages = Math.ceil(count / limit);
  
      res.render('admin/stores/index', {
        stores,
        currentPage: page,
        totalPages
      });
  
    } catch (error) {
      console.error('Error fetching stores:', error);
      res.status(500).render('error', { error: { message: 'Unable to fetch stores' } });
    }
  }
  
  
  // عرض الزبائن التابعة لمنطقة
  async areaCustomers(req, res) {
    const { id } = req.params;
    const page = parseInt(req.query.page) || 1;
    const limit = 10;
    const offset = (page - 1) * limit;
  
    try {
      const area = await Area.findByPk(id);
  
      if (!area) {
        return res.status(404).render('error', { error: { message: 'Area not found' } });
      }
  
      const { count, rows: customers } = await Customer.findAndCountAll({
        include: [{
          model: Area,
          as: 'areas',
          where: { id },
          through: 'CustomerArea'
        }],
        limit,
        offset,
        order: [['createdAt', 'DESC']]
      });
  
      const totalPages = Math.ceil(count / limit);
  
      res.render('admin/customers/index', {
        area,
        customers,
        currentPage: page,
        totalPages
      });
  
    } catch (error) {
      console.error('Error fetching customers:', error);
      res.status(500).render('error', { error: { message: 'Unable to fetch customers' } });
    }
  }
  
  
  
  
    // عرض صفحة إنشاء منطقة جديدة
    async create(req, res) {
        try {
            const countries = await Country.findAll();
            const countryId = req.query.countryId || null;
            res.render('areas/create', { countries, countryId });
        } catch (error) {
            console.error('Error loading create area form:', error);
            res.status(500).render('error', { error: { message: 'Unable to load create area form' } });
        }
    }

    // حفظ منطقة جديدة في قاعدة البيانات
    async store(req, res) {
        try {
            await Area.create(req.body);
            req.flash('success', 'Area created successfully');
            res.redirect('/admin/areas');
        } catch (error) {
            console.error('Error creating area:', error);
            res.status(500).render('error', { error: { message: 'Unable to create area' } });
        }
    }

    // عرض صفحة تعديل منطقة موجودة
    async edit(req, res) {
        try {
            const [area, countries] = await Promise.all([
                Area.findByPk(req.params.id, {
                    include: [
                        { model: Country, as: 'country' },
                        { model: Customer, as: 'customers', through: 'CustomerArea' }
                    ]
                }),
                Country.findAll()
            ]);
            if (!area) {
                return res.status(404).render('error', { error: { message: 'Area not found' } });
            }
            res.render('areas/edit', { area, countries });
        } catch (error) {
            console.error('Error loading edit area form:', error);
            res.status(500).render('error', { error: { message: 'Unable to load edit area form' } });
        }
    }

       // تحديث منطقة موجودة
       async update(req, res) {
        try {
            const area = await Area.findByPk(req.params.id);
            if (!area) {
                return res.status(404).render('error', { error: { message: 'Area not found' } });
            }

            await area.update(req.body);
            req.flash('success', 'Area updated successfully');
            res.redirect('/admin/areas');
        } catch (error) {
            console.error('Error updating area:', error);
            res.status(500).render('error', { error: { message: 'Unable to update area' } });
        }
    }

    // حذف منطقة موجودة
    async delete(req, res) {
        try {
            const area = await Area.findByPk(req.params.id);
            if (!area) {
                return res.status(404).render('error', { error: { message: 'Area not found' } });
            }

            await area.destroy();
            req.flash('success', 'Area deleted successfully');
            res.redirect('/admin/areas');
        } catch (error) {
            console.error('Error deleting area:', error);
            res.status(500).render('error', { error: { message: 'Unable to delete area' } });
        }
    }
}

module.exports = new AreasController();
