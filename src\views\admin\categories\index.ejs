<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Categories</h1>
        <a href="/admin/categories/create" class="btn btn-primary">Add New Category</a>
    </div>

    <div class="table-responsive">
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Name</th>
                    <th>Description</th>
                    <th>Stores Count</th>
                    <th>Status</th>
                    <th>Created At</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <% categories.forEach(category => { %>
                    <tr>
                        <td><%= category.id %></td>
                        <td><%= category.name %></td>
                        <td><%= category.description %></td>
                        <td><%= category.stores ? category.stores.length : 0 %></td>
                        <td>
                            <span class="badge bg-<%= category.status === 'active' ? 'success' : 'secondary' %>">
                                <%= category.status %>
                            </span>
                        </td>
                        <td><%= new Date(category.createdAt).toLocaleString() %></td>
                        <td>
                            <a href="/admin/categories/<%= category.id %>/edit" class="btn btn-sm btn-warning">Edit</a>
                            <form action="/admin/categories/<%= category.id %>/delete" method="POST" class="d-inline">
                                <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure?')">Delete</button>
                            </form>
                        </td>
                    </tr>
                <% }); %>
            </tbody>
        </table>
       
    </div>
    <% if (totalPages > 1) { %>
      <nav aria-label="Page navigation" class="d-flex justify-content-center mt-4">
        <ul class="pagination">
          <% if (currentPage > 1) { %>
            <li class="page-item">
              <a class="page-link" href="?page=<%= currentPage - 1 %>">السابق</a>
            </li>
          <% } else { %>
            <li class="page-item disabled">
              <span class="page-link">السابق</span>
            </li>
          <% } %>
      
          <% for(let i = 1; i <= totalPages; i++) { %>
            <li class="page-item <%= currentPage === i ? 'active' : '' %>">
              <a class="page-link" href="?page=<%= i %>"><%= i %></a>
            </li>
          <% } %>
      
          <% if (currentPage < totalPages) { %>
            <li class="page-item">
              <a class="page-link" href="?page=<%= currentPage + 1 %>">التالي</a>
            </li>
          <% } else { %>
            <li class="page-item disabled">
              <span class="page-link">التالي</span>
            </li>
          <% } %>
        </ul>
      </nav>
      <% } %>
    <% if (categories.length === 0) { %>
        <div class="alert alert-info">No categories found.</div>
    <% } %>
</div>
