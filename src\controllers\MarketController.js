const { Product, Store, Category, Order, OrderDetail, Image } = require('../models');
const { Op } = require('sequelize');

class MarketController {
    // Display homepage with featured products and active stores
    async home(req, res) {
        try {
            const [featuredProducts, activeStores] = await Promise.all([
                Product.findAll({
                    where: {
                        featured: true,
                        status: 'active',
                        quantity: { [Op.gt]: 0 }
                    },
                    include: [{
                        model: Store,
                        as: 'store',
                        where: { status: 'active' }
                    }],
                    limit: 8
                }),
                Store.findAll({
                    where: { status: 'active' },
                    limit: 10
                })
            ]);

            res.render('market/home', { featuredProducts, activeStores });
        } catch (error) {
            res.status(500).render('error', { error });
        }
    }

    // Browse products with optional filters and pagination
    async browseProducts(req, res) {
        try {
            const {
                category,
                store,
                minPrice,
                maxPrice,
                sort = 'newest',
                page = 1
            } = req.query;

            const where = {
                status: 'active',
                quantity: { [Op.gt]: 0 }
            };

            if (category) where.category = category;
            if (minPrice) where.price = { ...where.price, [Op.gte]: minPrice };
            if (maxPrice) where.price = { ...where.price, [Op.lte]: maxPrice };

            const include = [{
                model: Store,
                as: 'store',
                where: { status: 'active' }
            }];

            if (store) include[0].where.id = store;

            // Determine sorting order
            const order = [];
            switch (sort) {
                case 'price-asc':
                    order.push(['price', 'ASC']);
                    break;
                case 'price-desc':
                    order.push(['price', 'DESC']);
                    break;
                case 'name':
                    order.push(['name', 'ASC']);
                    break;
                default:
                    order.push(['createdAt', 'DESC']);
            }

            const limit = 12;
            const offset = (page - 1) * limit;

            const { count, rows: products } = await Product.findAndCountAll({
                where,
                include,
                order,
                limit,
                offset
            });

            const totalPages = Math.ceil(count / limit);

            res.render('market/browse', {
                products,
                currentPage: page,
                totalPages,
                filters: { category, store, minPrice, maxPrice, sort }
            });
        } catch (error) {
            res.status(500).render('error', { error });
        }
    }

    // View single product details with related products
    async viewProduct(req, res) {
        try {
            const product = await Product.findOne({
                where: {
                    id: req.params.id,
                    status: 'active'
                },
                include: [
                    {
                        model: Store,
                        as: 'store',
                        where: { status: 'active' }
                    },
                    {
                        model: Image,
                        as: 'images'
                    }
                ]
            });

            if (!product) {
                return res.status(404).render('error', {
                    error: { status: 404, message: 'Product not found' }
                });
            }

            // Related products from same store & category, excluding current product
            const relatedProducts = await Product.findAll({
                where: {
                    id: { [Op.ne]: product.id },
                    storeId: product.storeId,
                    category: product.category,
                    status: 'active',
                    quantity: { [Op.gt]: 0 }
                },
                limit: 4
            });

            res.render('market/product', { product, relatedProducts });
        } catch (error) {
            res.status(500).render('error', { error });
        }
    }

    // Add a product to the shopping cart stored in session
    async addToCart(req, res) {
        try {
            const { productId, quantity } = req.body;

            const product = await Product.findOne({
                where: {
                    id: productId,
                    status: 'active',
                    quantity: { [Op.gte]: quantity }
                },
                include: [{
                    model: Store,
                    as: 'store',
                    where: { status: 'active' }
                }]
            });

            if (!product) {
                req.flash('error', 'Product not available in requested quantity');
                return res.redirect('back');
            }

            // Initialize cart array in session if missing
            if (!req.session.cart) {
                req.session.cart = [];
            }

            const cartItem = req.session.cart.find(item => item.productId === productId);
            if (cartItem) {
                cartItem.quantity = parseInt(cartItem.quantity) + parseInt(quantity);
            } else {
                req.session.cart.push({
                    productId,
                    quantity: parseInt(quantity),
                    name: product.name,
                    price: product.getFinalPrice(),
                    storeId: product.storeId,
                    storeName: product.store.name
                });
            }

            req.flash('success', 'Product added to cart');
            res.redirect('back');
        } catch (error) {
            res.status(500).render('error', { error });
        }
    }

    // Display the current shopping cart grouped by store
    async viewCart(req, res) {
        try {
            const cart = req.session.cart || [];
            let total = 0;

            // Group cart items by storeId
            const storeGroups = {};
            cart.forEach(item => {
                if (!storeGroups[item.storeId]) {
                    storeGroups[item.storeId] = {
                        storeName: item.storeName,
                        items: [],
                        subtotal: 0
                    };
                }
                const itemTotal = item.price * item.quantity;
                storeGroups[item.storeId].items.push({ ...item, total: itemTotal });
                storeGroups[item.storeId].subtotal += itemTotal;
                total += itemTotal;
            });

            res.render('market/cart', { storeGroups, total });
        } catch (error) {
            res.status(500).render('error', { error });
        }
    }
}

module.exports = new MarketController();
