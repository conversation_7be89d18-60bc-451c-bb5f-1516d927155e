<div class="container mt-5">
    <h2>Deliveries for Driver: <%= driverName %></h2>
    <a href="/admin/stores/<%= storeId %>/drivers" class="btn btn-secondary mb-3">Back to Drivers</a>
  
    <% if (deliveries && deliveries.length > 0) { %>
      <table class="table table-bordered table-striped">
        <thead>
          <tr>
            <th>ID</th>
            <th>Order ID</th>
            <th>Customer</th>
            <th>Status</th>
            <th>Created At</th>
          </tr>
        </thead>
        <tbody>
          <% deliveries.forEach(d => { %>
            <tr>
              <td><%= d.id %></td>
              <td>
                <a href="/admin/orders/<%= d.order?.id %>">
                  #<%= d.order?.id %>
                </a>
              </td>
              <td>
                <a href="/admin/customers/<%= d.order?.customer?.id %>">
                  <%= d.order?.customer?.name %>
                </a>
              </td>
              <td><%= d.status %></td>
              <td><%= d.createdAt.toLocaleString() %></td>
            </tr>
          <% }) %>
        </tbody>
        <% if (totalPages > 1) { %>
          <nav aria-label="Page navigation" class="d-flex justify-content-center mt-4">
            <ul class="pagination">
              <% if (currentPage > 1) { %>
                <li class="page-item">
                  <a class="page-link" href="?page=<%= currentPage - 1 %>">السابق</a>
                </li>
              <% } else { %>
                <li class="page-item disabled">
                  <span class="page-link">السابق</span>
                </li>
              <% } %>
          
              <% for(let i = 1; i <= totalPages; i++) { %>
                <li class="page-item <%= currentPage === i ? 'active' : '' %>">
                  <a class="page-link" href="?page=<%= i %>"><%= i %></a>
                </li>
              <% } %>
          
              <% if (currentPage < totalPages) { %>
                <li class="page-item">
                  <a class="page-link" href="?page=<%= currentPage + 1 %>">التالي</a>
                </li>
              <% } else { %>
                <li class="page-item disabled">
                  <span class="page-link">التالي</span>
                </li>
              <% } %>
            </ul>
          </nav>
          <% } %>
      </table>
    <% } else { %>
      <div class="alert alert-info">No deliveries found for this driver.</div>
    <% } %>
  </div>
  