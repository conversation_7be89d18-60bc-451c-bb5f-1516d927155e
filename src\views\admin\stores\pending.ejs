<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Pending Store Approvals</h1>
        <div>
            <a href="/admin/stores" class="btn btn-primary">
                <i class="fas fa-arrow-left"></i> Back to All Stores
            </a>
        </div>
    </div>

    <% if (stores && stores.length > 0) { %>
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Store Name</th>
                        <th>Owner</th>
                        <th>Area</th>
                        <th>Phone</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <% stores.forEach(store => { %>
                        <tr>
                            <td><%= store.id %></td>
                            <td><%= store.name %></td>
                            <td><%= store.userName %></td>
                            <td><%= store.area ? store.area.name : 'N/A' %></td>
                            <td><%= store.phoneNumber || 'N/A' %></td>
                            <td><%= new Date(store.createdAt).toLocaleDateString() %></td>
                            <td class="action-buttons">
                                <form action="/admin/stores/<%= store.id %>/status" method="POST" class="d-inline">
                                    <input type="hidden" name="status" value="active">
                                    <button type="submit" class="btn btn-sm btn-success">
                                        <i class="fas fa-check"></i> Approve
                                    </button>
                                </form>
                                <form action="/admin/stores/<%= store.id %>/status" method="POST" class="d-inline">
                                    <input type="hidden" name="status" value="inactive">
                                    <button type="submit" class="btn btn-sm btn-danger">
                                        <i class="fas fa-times"></i> Reject
                                    </button>
                                </form>
                                <a href="/admin/stores/<%= store.id %>/edit" class="btn btn-sm btn-primary">
                                    <i class="fas fa-edit"></i> Edit
                                </a>
                            </td>
                        </tr>
                    <% }); %>
                </tbody>
            </table>
        </div>
    <% } else { %>
        <div class="alert alert-info">No pending store approvals.</div>
    <% } %>
</div> 