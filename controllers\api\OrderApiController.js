const { Order, OrderDetail, Product, Store, Customer, Category, Notification } = require('../../models');
const { Op } = require('sequelize');
const { sequelize } = require('../../models');

class OrderApiController {

    /**
     * إنشاء طلب جديد
     * POST /api/orders
     */
    async createOrder(req, res) {
        const transaction = await sequelize.transaction();
        
        try {
            const customerId = req.customer.id;
            const { storeId, products, deliveryAddress, notes } = req.body;

            // التحقق من البيانات المطلوبة
            if (!storeId || !products || !Array.isArray(products) || products.length === 0) {
                await transaction.rollback();
                return res.status(400).json({
                    success: false,
                    message: 'بيانات الطلب غير مكتملة',
                    data: null
                });
            }

            // التحقق من وجود المتجر
            const store = await Store.findByPk(storeId);
            if (!store || !store.isActive) {
                await transaction.rollback();
                return res.status(404).json({
                    success: false,
                    message: 'المتجر غير موجود أو غير نشط',
                    data: null
                });
            }

            // التحقق من المنتجات وحساب السعر الإجمالي
            let totalPrice = 0;
            const orderProducts = [];

            for (const item of products) {
                const product = await Product.findByPk(item.productId);
                if (!product || !product.isActive || product.storeId !== parseInt(storeId)) {
                    await transaction.rollback();
                    return res.status(400).json({
                        success: false,
                        message: `المنتج ${item.productId} غير متاح`,
                        data: null
                    });
                }

                const quantity = parseInt(item.quantity) || 1;
                const price = parseFloat(product.price);
                const subtotal = price * quantity;

                orderProducts.push({
                    productId: product.id,
                    quantity: quantity,
                    price: price,
                    subtotal: subtotal
                });

                totalPrice += subtotal;
            }

            // إنشاء الطلب
            const order = await Order.create({
                customerId: customerId,
                storeId: storeId,
                totalPrice: totalPrice,
                status: 'pending',
                deliveryAddress: deliveryAddress,
                notes: notes
            }, { transaction });

            // إنشاء تفاصيل الطلب
            for (const item of orderProducts) {
                await OrderDetail.create({
                    orderId: order.id,
                    productId: item.productId,
                    quantity: item.quantity,
                    price: item.price,
                    subtotal: item.subtotal
                }, { transaction });
            }

            // إنشاء إشعار للمتجر
            await Notification.create({
                type: 'order',
                title: 'طلب جديد',
                message: `تم استلام طلب جديد رقم #${order.id}`,
                targetType: 'store',
                targetId: storeId,
                relatedId: order.id,
                isRead: false
            }, { transaction });

            await transaction.commit();

            // جلب الطلب مع التفاصيل
            const createdOrder = await Order.findByPk(order.id, {
                include: [
                    {
                        model: Store,
                        attributes: ['id', 'name', 'phone', 'address']
                    },
                    {
                        model: OrderDetail,
                        include: [
                            {
                                model: Product,
                                attributes: ['id', 'name', 'image'],
                                include: [Category]
                            }
                        ]
                    }
                ]
            });

            res.status(201).json({
                success: true,
                message: 'تم إنشاء الطلب بنجاح',
                data: { 
                    order: this.formatOrder(createdOrder)
                }
            });

        } catch (error) {
            await transaction.rollback();
            console.error('خطأ في إنشاء الطلب:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    /**
     * الحصول على طلبات العميل
     * GET /api/orders
     */
    async getCustomerOrders(req, res) {
        try {
            const customerId = req.customer.id;
            const { 
                page = 1, 
                limit = 10, 
                status = null 
            } = req.query;

            const offset = (page - 1) * limit;
            const whereClause = { customerId: customerId };

            if (status) {
                whereClause.status = status;
            }

            const { count, rows: orders } = await Order.findAndCountAll({
                where: whereClause,
                include: [
                    {
                        model: Store,
                        attributes: ['id', 'name', 'phone', 'address', 'image']
                    },
                    {
                        model: OrderDetail,
                        include: [
                            {
                                model: Product,
                                attributes: ['id', 'name', 'image'],
                                include: [
                                    {
                                        model: Category,
                                        attributes: ['id', 'name']
                                    }
                                ]
                            }
                        ]
                    }
                ],
                limit: parseInt(limit),
                offset: parseInt(offset),
                order: [['createdAt', 'DESC']]
            });

            const formattedOrders = orders.map(order => this.formatOrder(order));

            res.json({
                success: true,
                message: 'تم جلب الطلبات بنجاح',
                data: {
                    orders: formattedOrders,
                    pagination: {
                        currentPage: parseInt(page),
                        totalPages: Math.ceil(count / limit),
                        totalItems: count,
                        itemsPerPage: parseInt(limit)
                    }
                }
            });

        } catch (error) {
            console.error('خطأ في جلب الطلبات:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    /**
     * الحصول على تفاصيل طلب واحد
     * GET /api/orders/:id
     */
    async getOrderById(req, res) {
        try {
            const { id } = req.params;
            const customerId = req.customer.id;

            const order = await Order.findOne({
                where: { 
                    id: id,
                    customerId: customerId 
                },
                include: [
                    {
                        model: Store,
                        attributes: ['id', 'name', 'phone', 'address', 'image']
                    },
                    {
                        model: OrderDetail,
                        include: [
                            {
                                model: Product,
                                attributes: ['id', 'name', 'description', 'image', 'price'],
                                include: [
                                    {
                                        model: Category,
                                        attributes: ['id', 'name']
                                    }
                                ]
                            }
                        ]
                    }
                ]
            });

            if (!order) {
                return res.status(404).json({
                    success: false,
                    message: 'الطلب غير موجود',
                    data: null
                });
            }

            res.json({
                success: true,
                message: 'تم جلب تفاصيل الطلب بنجاح',
                data: { 
                    order: this.formatOrder(order, true)
                }
            });

        } catch (error) {
            console.error('خطأ في جلب تفاصيل الطلب:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    /**
     * إلغاء طلب
     * PUT /api/orders/:id/cancel
     */
    async cancelOrder(req, res) {
        try {
            const { id } = req.params;
            const customerId = req.customer.id;

            const order = await Order.findOne({
                where: { 
                    id: id,
                    customerId: customerId 
                }
            });

            if (!order) {
                return res.status(404).json({
                    success: false,
                    message: 'الطلب غير موجود',
                    data: null
                });
            }

            // التحقق من إمكانية الإلغاء
            if (order.status !== 'pending') {
                return res.status(400).json({
                    success: false,
                    message: 'لا يمكن إلغاء هذا الطلب',
                    data: null
                });
            }

            // تحديث حالة الطلب
            await order.update({ status: 'cancelled' });

            // إنشاء إشعار للمتجر
            await Notification.create({
                type: 'order',
                title: 'تم إلغاء طلب',
                message: `تم إلغاء الطلب رقم #${order.id}`,
                targetType: 'store',
                targetId: order.storeId,
                relatedId: order.id,
                isRead: false
            });

            res.json({
                success: true,
                message: 'تم إلغاء الطلب بنجاح',
                data: null
            });

        } catch (error) {
            console.error('خطأ في إلغاء الطلب:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    /**
     * الحصول على إحصائيات الطلبات
     * GET /api/orders/stats
     */
    async getOrderStats(req, res) {
        try {
            const customerId = req.customer.id;

            const stats = await Order.findAll({
                where: { customerId: customerId },
                attributes: [
                    'status',
                    [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
                    [sequelize.fn('SUM', sequelize.col('totalPrice')), 'totalAmount']
                ],
                group: ['status'],
                raw: true
            });

            const totalOrders = await Order.count({
                where: { customerId: customerId }
            });

            const totalSpent = await Order.sum('totalPrice', {
                where: { 
                    customerId: customerId,
                    status: 'completed'
                }
            });

            const formattedStats = {
                totalOrders: totalOrders,
                totalSpent: totalSpent || 0,
                statusBreakdown: stats.reduce((acc, stat) => {
                    acc[stat.status] = {
                        count: parseInt(stat.count),
                        totalAmount: parseFloat(stat.totalAmount) || 0
                    };
                    return acc;
                }, {})
            };

            res.json({
                success: true,
                message: 'تم جلب الإحصائيات بنجاح',
                data: { stats: formattedStats }
            });

        } catch (error) {
            console.error('خطأ في جلب الإحصائيات:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    /**
     * تنسيق بيانات الطلب للإرجاع
     */
    formatOrder(order, includeDetails = false) {
        const formatted = {
            id: order.id,
            totalPrice: parseFloat(order.totalPrice),
            status: order.status,
            statusText: this.getStatusText(order.status),
            deliveryAddress: order.deliveryAddress,
            notes: order.notes,
            createdAt: order.createdAt,
            updatedAt: order.updatedAt,
            store: order.Store ? {
                id: order.Store.id,
                name: order.Store.name,
                phone: order.Store.phone,
                address: order.Store.address,
                image: order.Store.image ? `/uploads/${order.Store.image}` : null
            } : null,
            itemsCount: order.OrderDetails ? order.OrderDetails.length : 0
        };

        if (includeDetails && order.OrderDetails) {
            formatted.items = order.OrderDetails.map(detail => ({
                id: detail.id,
                quantity: detail.quantity,
                price: parseFloat(detail.price),
                subtotal: parseFloat(detail.subtotal),
                product: detail.Product ? {
                    id: detail.Product.id,
                    name: detail.Product.name,
                    description: detail.Product.description,
                    image: detail.Product.image ? `/uploads/${detail.Product.image}` : null,
                    category: detail.Product.Category ? {
                        id: detail.Product.Category.id,
                        name: detail.Product.Category.name
                    } : null
                } : null
            }));
        }

        return formatted;
    }

    /**
     * الحصول على نص الحالة بالعربية
     */
    getStatusText(status) {
        const statusTexts = {
            'pending': 'في الانتظار',
            'accepted': 'مقبول',
            'preparing': 'قيد التحضير',
            'ready': 'جاهز للتسليم',
            'delivered': 'تم التسليم',
            'completed': 'مكتمل',
            'cancelled': 'ملغي',
            'rejected': 'مرفوض'
        };
        return statusTexts[status] || status;
    }
}

module.exports = new OrderApiController();
