import dotenv from 'dotenv';
import path from 'path';
import '@types/jest';

// Load environment variables from .env.test
dotenv.config({ path: path.join(__dirname, '../../.env.test') });

// Set test environment
process.env.NODE_ENV = 'test';

// Mock console methods to keep test output clean
global.console = {
    ...console,
    log: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    info: jest.fn(),
    debug: jest.fn(),
} as Console;

// Increase timeout for all tests
jest.setTimeout(10000);

// Clean up after each test
afterEach(() => {
    jest.clearAllMocks();
}); 