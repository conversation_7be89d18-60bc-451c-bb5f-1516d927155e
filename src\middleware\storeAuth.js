const { Store } = require('../../models');

const publicPaths = [
    '/store/auth/login',
    '/store/auth/register',
    '/store/auth/logout',
    '/assets',
    '/css',
    '/js',
    '/favicon.ico'
];

const isPublicPath = (path) => {
    return publicPaths.some(publicPath => path.startsWith(publicPath));
};

const requireStoreAuth = async (req, res, next) => {
    // Allow access to public paths
    if (isPublicPath(req.path)) {
        return next();
    }

    // Check if store is authenticated
    if (!req.session.storeId) {
        return res.redirect('/store/auth/login');
    }

    try {
        // Get store from database
        const store = await Store.findOne({
            where: {
                id: req.session.storeId,
                status: 'active'
            }
        });

        if (!store) {
            req.session.destroy();
            return res.redirect('/store/auth/login');
        }

        // Add store to request object
        req.store = store;
        next();
    } catch (error) {
        res.status(500).render('error', { error });
    }
};

module.exports = requireStoreAuth; 