const express = require('express');
const router = express.Router();
const adminAuthController = require('../controllers/AdminAuthController');

// Authentication middleware
const requireAdminAuth = (req, res, next) => {
    if (!req.session.adminId) {
        return res.redirect('/admin/auth/login');
    }
    next();
};

// Super admin middleware
const requireSuperAdmin = (req, res, next) => {
    if (req.session.adminRole !== 'super_admin') {
        return res.status(403).render('error', {
            error: {
                status: 403,
                message: 'Access denied. Super admin privileges required.'
            }
        });
    }
    next();
};

// Login routes
router.get('/login', adminAuthController.showLogin);
router.post('/login', adminAuthController.login);
router.get('/logout', adminAuthController.logout);

// Password change routes (protected)
router.get('/change-password', requireAdminAuth, adminAuthController.showChangePassword);
router.post('/change-password', requireAdminAuth, adminAuthController.changePassword);


module.exports = {
    router,
    requireAdminAuth,
    requireSuperAdmin
}; 