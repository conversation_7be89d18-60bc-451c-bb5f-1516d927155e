class BaseController {
    constructor(model, modelName) {
        this.model = model;
        this.modelName = modelName;
    }

    async index(req, res, next) {
        try {
            const items = await this.model.findAll();
            res.render(`${this.modelName}/index`, { items });
        } catch (error) {
            console.error(`Error fetching ${this.modelName}:`, error);
            next(error); // استخدام معالج الأخطاء المركزي
        }
    }

    async create(req, res, next) {
        try {
            res.render(`${this.modelName}/create`);
        } catch (error) {
            console.error(`Error rendering create form for ${this.modelName}:`, error);
            next(error);
        }
    }

    async store(req, res, next) {
        try {
            await this.model.create(req.body);
            req.flash('success', `${this.modelName} created successfully`);
            res.redirect(`/${this.modelName}`);
        } catch (error) {
            console.error(`Error creating ${this.modelName}:`, error);
            next(error);
        }
    }

    async edit(req, res, next) {
        try {
            const item = await this.model.findByPk(req.params.id);
            if (!item) {
                const error = new Error('Item not found');
                error.statusCode = 404;
                return next(error);
            }
            res.render(`${this.modelName}/edit`, { item });
        } catch (error) {
            console.error(`Error editing ${this.modelName}:`, error);
            next(error);
        }
    }

    async update(req, res, next) {
        try {
            const item = await this.model.findByPk(req.params.id);
            if (!item) {
                const error = new Error('Item not found');
                error.statusCode = 404;
                return next(error);
            }
            await item.update(req.body);
            req.flash('success', `${this.modelName} updated successfully`);
            res.redirect(`/${this.modelName}`);
        } catch (error) {
            console.error(`Error updating ${this.modelName}:`, error);
            next(error);
        }
    }

    async delete(req, res, next) {
        try {
            const item = await this.model.findByPk(req.params.id);
            if (!item) {
                const error = new Error('Item not found');
                error.statusCode = 404;
                return next(error);
            }
            await item.destroy();
            req.flash('success', `${this.modelName} deleted successfully`);
            res.redirect(`/${this.modelName}`);
        } catch (error) {
            console.error(`Error deleting ${this.modelName}:`, error);
            next(error);
        }
    }
}

module.exports = BaseController;
