<div class="container mt-5">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>Drivers for Store: <%= store.name %></h2>
        <a href="/admin/stores" class="btn btn-secondary">Back to Stores</a>
    </div>

    <% if (deliveryPeoples && deliveryPeoples.length > 0) { %>
        <div class="table-responsive">
            <table class="table table-bordered table-striped">
                <thead>
                    <tr>
                      <th>ID</th>
                      <th>Name</th>
                      <th>Phone</th>
                      <th>Status</th>
                      <th>Deliveries</th> <!-- جديد -->
                      <th>Created At</th>
                    </tr>
                  </thead>
                  <tbody>
                    <% deliveryPeoples.forEach(deliveryPeople => { %>
                      <tr>
                        <td><%= deliveryPeople.id %></td>
                        <td><%= deliveryPeople.name %></td>
                        <td><%= deliveryPeople.phoneNumber %></td>
                        <td>
                          <% if (deliveryPeople.status === 'active') { %>
                            <span class="badge bg-success">Active</span>
                          <% } else if (deliveryPeople.status === 'inactive') { %>
                            <span class="badge bg-danger">Inactive</span>
                          <% } else { %>
                            <span class="badge bg-secondary">Unknown</span>
                          <% } %>
                        </td>
                        <td>
                          <a href="/admin/stores/<%= store.id %>/drivers/<%= deliveryPeople.id %>/deliveries" class="btn btn-sm btn-outline-primary">
                            <%= deliveryPeople.deliveries ? deliveryPeople.deliveries.length : 0 %> Deliveries
                          </a>
                        </td>
                        <td><%= deliveryPeople.createdAt.toLocaleDateString() %></td>
                      </tr>
                    <% }) %>
                  </tbody>                  
            </table>
            <% if (totalPages > 1) { %>
              <nav aria-label="Page navigation" class="d-flex justify-content-center mt-4">
                <ul class="pagination">
                  <% if (currentPage > 1) { %>
                    <li class="page-item">
                      <a class="page-link" href="?page=<%= currentPage - 1 %>">السابق</a>
                    </li>
                  <% } else { %>
                    <li class="page-item disabled">
                      <span class="page-link">السابق</span>
                    </li>
                  <% } %>
              
                  <% for(let i = 1; i <= totalPages; i++) { %>
                    <li class="page-item <%= currentPage === i ? 'active' : '' %>">
                      <a class="page-link" href="?page=<%= i %>"><%= i %></a>
                    </li>
                  <% } %>
              
                  <% if (currentPage < totalPages) { %>
                    <li class="page-item">
                      <a class="page-link" href="?page=<%= currentPage + 1 %>">التالي</a>
                    </li>
                  <% } else { %>
                    <li class="page-item disabled">
                      <span class="page-link">التالي</span>
                    </li>
                  <% } %>
                </ul>
              </nav>
              <% } %>
        </div>
    <% } else { %>
        <div class="alert alert-info">No drivers found for this store.</div>
    <% } %>
</div>
