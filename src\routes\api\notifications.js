const express = require('express');
const router = express.Router();
const { authenticateCustomer } = require('../../middleware/apiAuth');
const { Notification } = require('../../models');

/**
 * الحصول على إشعارات العميل
 * GET /api/notifications
 */
router.get('/', authenticateCustomer, async (req, res) => {
    try {
        const customerId = req.customer.id;
        const { page = 1, limit = 20, unreadOnly = false } = req.query;

        const offset = (page - 1) * limit;
        const whereClause = {
            targetType: 'customer',
            targetId: customerId
        };

        if (unreadOnly === 'true') {
            whereClause.isRead = false;
        }

        const { count, rows: notifications } = await Notification.findAndCountAll({
            where: whereClause,
            limit: parseInt(limit),
            offset: parseInt(offset),
            order: [['createdAt', 'DESC']]
        });

        const formattedNotifications = notifications.map(notification => ({
            id: notification.id,
            type: notification.type,
            title: notification.title,
            message: notification.message,
            isRead: notification.isRead,
            relatedId: notification.relatedId,
            createdAt: notification.createdAt
        }));

        res.json({
            success: true,
            message: 'تم جلب الإشعارات بنجاح',
            data: {
                notifications: formattedNotifications,
                pagination: {
                    currentPage: parseInt(page),
                    totalPages: Math.ceil(count / limit),
                    totalItems: count,
                    itemsPerPage: parseInt(limit)
                }
            }
        });

    } catch (error) {
        console.error('خطأ في جلب الإشعارات:', error);
        res.status(500).json({
            success: false,
            message: 'حدث خطأ في الخادم',
            data: null
        });
    }
});

/**
 * عدد الإشعارات غير المقروءة
 * GET /api/notifications/unread-count
 */
router.get('/unread-count', authenticateCustomer, async (req, res) => {
    try {
        const customerId = req.customer.id;

        const count = await Notification.count({
            where: {
                targetType: 'customer',
                targetId: customerId,
                isRead: false
            }
        });

        res.json({
            success: true,
            message: 'تم جلب عدد الإشعارات غير المقروءة',
            data: { unreadCount: count }
        });

    } catch (error) {
        console.error('خطأ في جلب عدد الإشعارات:', error);
        res.status(500).json({
            success: false,
            message: 'حدث خطأ في الخادم',
            data: null
        });
    }
});

/**
 * وضع علامة مقروء على إشعار
 * PUT /api/notifications/:id/read
 */
router.put('/:id/read', authenticateCustomer, async (req, res) => {
    try {
        const { id } = req.params;
        const customerId = req.customer.id;

        const notification = await Notification.findOne({
            where: {
                id: id,
                targetType: 'customer',
                targetId: customerId
            }
        });

        if (!notification) {
            return res.status(404).json({
                success: false,
                message: 'الإشعار غير موجود',
                data: null
            });
        }

        await notification.update({ isRead: true });

        res.json({
            success: true,
            message: 'تم وضع علامة مقروء على الإشعار',
            data: null
        });

    } catch (error) {
        console.error('خطأ في تحديث الإشعار:', error);
        res.status(500).json({
            success: false,
            message: 'حدث خطأ في الخادم',
            data: null
        });
    }
});

/**
 * وضع علامة مقروء على جميع الإشعارات
 * PUT /api/notifications/mark-all-read
 */
router.put('/mark-all-read', authenticateCustomer, async (req, res) => {
    try {
        const customerId = req.customer.id;

        await Notification.update(
            { isRead: true },
            {
                where: {
                    targetType: 'customer',
                    targetId: customerId,
                    isRead: false
                }
            }
        );

        res.json({
            success: true,
            message: 'تم وضع علامة مقروء على جميع الإشعارات',
            data: null
        });

    } catch (error) {
        console.error('خطأ في تحديث الإشعارات:', error);
        res.status(500).json({
            success: false,
            message: 'حدث خطأ في الخادم',
            data: null
        });
    }
});

module.exports = router;
