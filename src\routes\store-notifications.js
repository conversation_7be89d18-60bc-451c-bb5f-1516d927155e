const express = require('express');
const router = express.Router();
const storeNotificationsController = require('../controllers/StoreNotificationsController');

// Middleware للتحقق من تسجيل دخول المتجر
function requireStoreAuth(req, res, next) {
    if (req.session && req.session.storeId) {
        return next();
    }
    res.redirect('/stores/login');
}

// تطبيق middleware على جميع الطرق
router.use(requireStoreAuth);

// Web Routes
router.get('/', storeNotificationsController.index.bind(storeNotificationsController));
router.get('/:id', storeNotificationsController.show.bind(storeNotificationsController));
router.post('/:id/read', storeNotificationsController.markAsRead.bind(storeNotificationsController));
router.post('/mark-all-read', storeNotificationsController.markAllAsRead.bind(storeNotificationsController));
router.post('/:id/delete', storeNotificationsController.delete.bind(storeNotificationsController));

// API Routes
router.get('/api/unread-count', storeNotificationsController.getUnreadCount.bind(storeNotificationsController));
router.get('/api/recent', storeNotificationsController.getRecentNotifications.bind(storeNotificationsController));

module.exports = router;
