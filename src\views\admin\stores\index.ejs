<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Stores</h1>
        <a href="/admin/stores/create" class="btn btn-primary">Add New Store</a>
    </div>

    <div class="table-responsive">
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Store Name</th>
                    <th>Username</th>
                    <th>Categories</th>
                    <th>Area</th>
                    <th>Address</th>
                    <th>Orders</th>
                    <th>Status</th>
                    <th>Drivers</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <% stores.forEach(store => { %>
                    <tr>
                        <td><%= store.id %></td>
                        <td><%= store.name %></td>
                        <td><%= store.userName %></td>
                        <td>
                            <% if (store.categories && store.categories.length) { %>
                                <%= store.categories.map(c => c.name).join(', ') %>
                            <% } else { %>
                                N/A
                            <% } %>
                        </td>
                        <td><%= store.area ? store.area.name : 'N/A' %></td>
                        <td><%= store.address %></td>
                        <td>
                            <a href="/admin/stores/<%= store.id %>/orders">
                                <%= store.orders ? store.orders.length : 0 %>
                            </a>
                        </td>
                        <td>
                            <% if (store.status === 'active') { %>
                                <span class="badge bg-success">Active</span>
                            <% } else if (store.status === 'inactive') { %>
                                <span class="badge bg-danger">Inactive</span>
                            <% } else if (store.status === 'pending') { %>
                                <span class="badge bg-warning text-dark">Pending</span>
                            <% } else { %>
                                <span class="badge bg-secondary">Unknown</span>
                            <% } %>
                        </td>
                        <td>
                            <a href="/admin/stores/<%= store.id %>/drivers">
                              <%= store.deliveryPeople ? store.deliveryPeople.length : 0 %>
                            </a>
                          </td>                          
                        <td class="action-buttons">
                            <a href="/admin/stores/<%= store.id %>/edit" class="btn btn-sm btn-warning">
                                <i class="fas fa-edit"></i> Edit
                            </a>
                            <form action="/admin/stores/<%= store.id %>/delete" method="POST" class="d-inline">
                                <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this store?')">
                                    <i class="fas fa-trash"></i> Delete
                                </button>
                            </form>
                        </td>
                    </tr>
                <% }); %>
            </tbody>
        </table>
    </div>
   
<% if (totalPages > 1) { %>
<nav aria-label="Page navigation" class="d-flex justify-content-center mt-4">
  <ul class="pagination">
    <% if (currentPage > 1) { %>
      <li class="page-item">
        <a class="page-link" href="?page=<%= currentPage - 1 %>">السابق</a>
      </li>
    <% } else { %>
      <li class="page-item disabled">
        <span class="page-link">السابق</span>
      </li>
    <% } %>

    <% for(let i = 1; i <= totalPages; i++) { %>
      <li class="page-item <%= currentPage === i ? 'active' : '' %>">
        <a class="page-link" href="?page=<%= i %>"><%= i %></a>
      </li>
    <% } %>

    <% if (currentPage < totalPages) { %>
      <li class="page-item">
        <a class="page-link" href="?page=<%= currentPage + 1 %>">التالي</a>
      </li>
    <% } else { %>
      <li class="page-item disabled">
        <span class="page-link">التالي</span>
      </li>
    <% } %>
  </ul>
</nav>
<% } %>
