<h1>تفاصيل المنتج</h1>
<p><strong>الاسم:</strong> <%= product.name %></p>
<p><strong>الوصف:</strong> <%= product.description %></p>
<p><strong>السعر:</strong> <%= product.price %></p>

<hr>

<h3>الصور الحالية:</h3>
<div class="row">
  <% product.images.forEach(img => { %>
    <div class="col-md-3 mb-3">
      <img src="<%= img.image %>" class="img-fluid rounded border" />
    </div>
  <% }) %>
</div>

<hr>

<h3>إضافة صور جديدة</h3>
<form action="/store/products/<%= product.id %>" method="POST" enctype="multipart/form-data">
  <input type="file" name="images" multiple class="form-control mb-3" required />
  <button class="btn btn-primary">رفع الصور</button>
</form>
