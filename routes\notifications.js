const express = require('express');
const router = express.Router();
const notificationsController = require('../controllers/NotificationsController');

// Web Routes
router.get('/', notificationsController.index.bind(notificationsController));
router.get('/create', notificationsController.create.bind(notificationsController));
router.get('/:id', notificationsController.show.bind(notificationsController));
router.post('/', notificationsController.store.bind(notificationsController));
router.post('/:id/read', notificationsController.markAsRead.bind(notificationsController));
router.post('/mark-all-read', notificationsController.markAllAsRead.bind(notificationsController));
router.post('/:id/delete', notificationsController.delete.bind(notificationsController));

// API Routes
router.get('/api/unread-count', notificationsController.getUnreadCount.bind(notificationsController));
router.get('/api/user-notifications', notificationsController.getUserNotifications.bind(notificationsController));

module.exports = router;
