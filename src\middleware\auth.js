const publicPaths = [
    '/admin/auth/login',
    '/admin/auth/logout',
    '/login',
    '/logout',
    '/assets',
    '/css',
    '/js',
    '/favicon.ico'
  ];
  
  // تحقق هل المسار ضمن المسارات العامة (لا تحتاج لتسجيل دخول)
  const isPublicPath = (path) => {
    return publicPaths.some(publicPath => path.startsWith(publicPath));
  };
  
  // ميدلوير عام للتحقق من وجود مستخدم مسجل دخول
  const requireAuth = (req, res, next) => {
    if (isPublicPath(req.path)) {
      return next();
    }
  
    if (!req.session.userId) {
      // لو ما فيه مستخدم مسجل دخول، نعيد توجيه لصفحة تسجيل الدخول
      return res.redirect('/login');
    }
  
    next();
  };
  
  const auth = {
    // تحقق من وجود مستخدم مسجل دخول (عام)
    user: (req, res, next) => {
      if (!req.session.userId) {
        req.flash('error', 'Please login to continue');
        return res.redirect('/login');
      }
      next();
    },
  
    // تحقق اذا المستخدم عميل (customer)
    customer: (req, res, next) => {
      if (!req.session.userId || req.session.userRole !== 'customer') {
        req.flash('error', 'Access denied. Customer privileges required');
        return res.redirect('/login');
      }
      next();
    },
  
    // تحقق اذا المستخدم صاحب متجر (store_owner)
    storeOwner: (req, res, next) => {
      if (!req.session.userId || req.session.userRole !== 'store_owner') {
        req.flash('error', 'Access denied. Store owner privileges required');
        return res.redirect('/login');
      }
      next();
    },
  
    // تحقق اذا المستخدم أدمن (admin)
    admin: (req, res, next) => {
      if (!req.session.userId || req.session.userRole !== 'admin') {
        req.flash('error', 'Access denied. Admin privileges required');
        return res.redirect('/admin/login');
      }
      next();
    }
  };
  
  module.exports = {
    requireAuth,
    auth
  };
  