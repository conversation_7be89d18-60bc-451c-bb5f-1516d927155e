const { verifyToken, extractToken } = require('../utils/jwt');
const { Customer, Store, Admin } = require('../models');

const publicPaths = [
    '/admin/auth/login',
    '/admin/auth/logout',
    '/customers/login',
    '/customers/register',
    '/customers/logout',
    '/store/login',
    '/store/register',
    '/store/logout',
    '/login',
    '/logout',
    '/assets',
    '/css',
    '/js',
    '/favicon.ico',
    '/public',
    '/uploads'
];

// تحقق هل المسار ضمن المسارات العامة (لا تحتاج لتسجيل دخول)
const isPublicPath = (path) => {
    return publicPaths.some(publicPath => path.startsWith(publicPath));
};

// ميدلوير عام للتحقق من وجود مستخدم مسجل دخول
const requireAuth = async (req, res, next) => {
    if (isPublicPath(req.path)) {
        return next();
    }

    const token = extractToken(req);
    if (!token) {
        return res.redirect('/customers/login');
    }

    try {
        const decoded = verifyToken(token);
        req.user = decoded;
        next();
    } catch (error) {
        return res.redirect('/customers/login');
    }
};

const auth = {
    // تحقق من وجود مستخدم مسجل دخول (عام)
    user: async (req, res, next) => {
        const token = extractToken(req);
        if (!token) {
            return res.status(401).json({ error: 'Access denied. No token provided.' });
        }

        try {
            const decoded = verifyToken(token);
            req.user = decoded;
            next();
        } catch (error) {
            return res.status(401).json({ error: 'Invalid token.' });
        }
    },

    // تحقق اذا المستخدم عميل (customer)
    customer: async (req, res, next) => {
        const token = extractToken(req);
        if (!token) {
            return res.redirect('/customers/login');
        }

        try {
            const decoded = verifyToken(token);
            if (decoded.userType !== 'customer') {
                return res.redirect('/customers/login');
            }

            // التحقق من وجود العميل في قاعدة البيانات
            const customer = await Customer.findByPk(decoded.id);
            if (!customer || customer.status !== 'active') {
                return res.redirect('/customers/login');
            }

            req.user = decoded;
            req.customer = customer;
            next();
        } catch (error) {
            return res.redirect('/customers/login');
        }
    },

    // تحقق اذا المستخدم صاحب متجر (store_owner)
    store: async (req, res, next) => {
        const token = extractToken(req);
        if (!token) {
            return res.redirect('/store/login');
        }

        try {
            const decoded = verifyToken(token);
            if (decoded.userType !== 'store') {
                return res.redirect('/store/login');
            }

            // التحقق من وجود المتجر في قاعدة البيانات
            const store = await Store.findByPk(decoded.id);
            if (!store || store.status !== 'active') {
                return res.redirect('/store/login');
            }

            req.user = decoded;
            req.store = store;
            next();
        } catch (error) {
            return res.redirect('/store/login');
        }
    },

    // تحقق اذا المستخدم أدمن (admin)
    admin: async (req, res, next) => {
        const token = extractToken(req);
        if (!token) {
            return res.redirect('/admin/auth/login');
        }

        try {
            const decoded = verifyToken(token);
            if (decoded.userType !== 'admin') {
                return res.redirect('/admin/auth/login');
            }

            // التحقق من وجود الأدمن في قاعدة البيانات
            const admin = await Admin.findByPk(decoded.id);
            if (!admin || admin.status !== 'active') {
                return res.redirect('/admin/auth/login');
            }

            req.user = decoded;
            req.admin = admin;
            next();
        } catch (error) {
            return res.redirect('/admin/auth/login');
        }
    }
};

module.exports = {
    requireAuth,
    auth,
    isPublicPath
};
