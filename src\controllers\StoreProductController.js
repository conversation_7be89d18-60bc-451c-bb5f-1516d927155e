const { Product, Image, Order, OrderDetail } = require('../models');
const path = require('path');
const fs = require('fs');
const Joi = require('joi');

class StoreProductController {
    // التحقق من صحة بيانات المنتج
    validateProductData(data) {
        const schema = Joi.object({
            name: Joi.string().min(3).max(100).required(),
            description: Joi.string().allow('').max(1000),
            price: Joi.number().min(0).required(),
            quantity: Joi.number().integer().min(0).required(),
            category: Joi.string().allow(null, ''),
            discountPercentage: Joi.number().min(0).max(100).optional(),
            discountStartDate: Joi.date().optional(),
            discountEndDate: Joi.date().optional(),
            status: Joi.string().valid('active', 'inactive').optional()
        });
        return schema.validateAsync(data);
    }

    // قائمة منتجات المتجر
    async index(req, res) {
        try {
            const products = await Product.findAll({
                where: { storeId: req.session.storeId },
                include: ['images'],
                order: [['createdAt', 'DESC']]
            });
            res.render('store/products/index', { products });
        } catch (error) {
            console.error('Error fetching products:', error);
            res.status(500).render('error', { error });
        }
    }

    // عرض نموذج إنشاء منتج جديد
    async create(req, res) {
        res.render('store/products/create');
    }

    // حفظ منتج جديد
    async store(req, res) {
        try {
            await this.validateProductData(req.body);

            const productData = {
                ...req.body,
                storeId: req.session.storeId
            };

            const product = await Product.create(productData);

            // تحميل الصور
            if (req.files && req.files.images) {
                const images = Array.isArray(req.files.images) ? req.files.images : [req.files.images];

                for (const image of images) {
                    const fileName = `${Date.now()}-${image.name.replace(/\s+/g, '-')}`;
                    const uploadPath = path.join('public', 'uploads', 'products', fileName);

                    await image.mv(uploadPath);

                    await Image.create({
                        productId: product.id,
                        path: `/uploads/products/${fileName}`
                    });
                }
            }

            req.flash('success', 'Product created successfully');
            res.redirect('/store/products');
        } catch (error) {
            console.error('Error creating product:', error);
            res.status(500).render('error', { error });
        }
    }

    // عرض نموذج تعديل المنتج
    async edit(req, res) {
        try {
            const product = await Product.findOne({
                where: {
                    id: req.params.id,
                    storeId: req.session.storeId
                },
                include: ['images']
            });

            if (!product) {
                return res.status(404).render('error', {
                    error: { status: 404, message: 'Product not found' }
                });
            }

            res.render('store/products/edit', { product });
        } catch (error) {
            console.error('Error fetching product for edit:', error);
            res.status(500).render('error', { error });
        }
    }

    // تحديث المنتج
    async update(req, res) {
        try {
            await this.validateProductData(req.body);

            const product = await Product.findOne({
                where: {
                    id: req.params.id,
                    storeId: req.session.storeId
                }
            });

            if (!product) {
                return res.status(404).render('error', {
                    error: { status: 404, message: 'Product not found' }
                });
            }

            await product.update(req.body);

            // تحميل الصور الجديدة
            if (req.files && req.files.images) {
                const images = Array.isArray(req.files.images) ? req.files.images : [req.files.images];

                for (const image of images) {
                    const fileName = `${Date.now()}-${image.name.replace(/\s+/g, '-')}`;
                    const uploadPath = path.join('public', 'uploads', 'products', fileName);

                    await image.mv(uploadPath);

                    await Image.create({
                        productId: product.id,
                        path: `/uploads/products/${fileName}`
                    });
                }
            }

            req.flash('success', 'Product updated successfully');
            res.redirect('/store/products');
        } catch (error) {
            console.error('Error updating product:', error);
            res.status(500).render('error', { error });
        }
    }

    // حذف المنتج
    async delete(req, res) {
        try {
            const product = await Product.findOne({
                where: {
                    id: req.params.id,
                    storeId: req.session.storeId
                },
                include: ['images']
            });

            if (!product) {
                return res.status(404).render('error', {
                    error: { status: 404, message: 'Product not found' }
                });
            }

            // حذف ملفات الصور من النظام إذا حابب (اختياري)
            if (product.images && product.images.length) {
                for (const img of product.images) {
                    const filePath = path.join('public', img.path);
                    if (fs.existsSync(filePath)) {
                        fs.unlinkSync(filePath);
                    }
                }
            }

            await product.destroy();
            req.flash('success', 'Product deleted successfully');
            res.redirect('/store/products');
        } catch (error) {
            console.error('Error deleting product:', error);
            res.status(500).render('error', { error });
        }
    }

    // عرض الطلبات الخاصة بالمتجر
    async orders(req, res) {
        try {
            const orders = await Order.findAll({
                include: [{
                    model: OrderDetail,
                    as: 'orderDetails',
                    include: [{
                        model: Product,
                        as: 'product',
                        where: { storeId: req.session.storeId }
                    }]
                }],
                order: [['createdAt', 'DESC']]
            });

            res.render('store/orders/index', { orders });
        } catch (error) {
            console.error('Error fetching orders:', error);
            res.status(500).render('error', { error });
        }
    }

    // تحديث حالة الطلب
    async updateOrderStatus(req, res) {
        try {
            const { orderId } = req.params;
            const { status } = req.body;

            const order = await Order.findByPk(orderId, {
                include: [
                    {
                        model: Customer,
                        as: 'customer',
                        attributes: ['id', 'name', 'email', 'phone']
                    },
                    {
                        model: OrderDetail,
                        as: 'orderDetails',
                        include: [{
                            model: Product,
                            as: 'product',
                            where: { storeId: req.session.storeId }
                        }]
                    }
                ]
            });

            if (!order) {
                return res.status(404).render('error', {
                    error: { status: 404, message: 'Order not found' }
                });
            }

            const oldStatus = order.status;
            await order.update({ status });

            // إرسال إشعار للعميل عند تغيير حالة الطلب
            try {
                await this.sendOrderStatusNotification(order, oldStatus, status);
            } catch (notificationError) {
                console.error('Error sending order status notification:', notificationError);
                // لا نوقف العملية إذا فشل الإشعار
            }

            req.flash('success', 'Order status updated successfully');
            res.redirect('/store/orders');
        } catch (error) {
            console.error('Error updating order status:', error);
            res.status(500).render('error', { error });
        }
    }

    /**
     * إرسال إشعار تحديث حالة الطلب للعميل
     */
    async sendOrderStatusNotification(order, oldStatus, newStatus) {
        try {
            const NotificationService = require('../services/NotificationService');

            const statusMessages = {
                'pending': 'في انتظار المعالجة',
                'confirmed': 'تم تأكيد طلبك',
                'preparing': 'جاري تحضير طلبك',
                'ready': 'طلبك جاهز للاستلام',
                'delivered': 'تم تسليم طلبك',
                'cancelled': 'تم إلغاء طلبك'
            };

            const message = statusMessages[newStatus] || 'تم تحديث حالة طلبك';
            const notificationType = newStatus === 'cancelled' ? 'warning' :
                                   newStatus === 'delivered' ? 'success' : 'info';

            // حساب إجمالي المبلغ للمنتجات من هذا المتجر
            const storeTotal = order.orderDetails.reduce((total, detail) => {
                return total + (detail.quantity * detail.price);
            }, 0);

            await NotificationService.notifyCustomer(order.customer.id, {
                title: 'تحديث حالة الطلب',
                message: `${message} رقم #${order.id}\n\nالمبلغ: ${storeTotal} ريال\nعدد المنتجات: ${order.orderDetails.length}`,
                type: notificationType,
                priority: newStatus === 'ready' || newStatus === 'delivered' ? 'high' : 'normal',
                actionUrl: `/customers/orders/${order.id}`,
                actionText: 'عرض تفاصيل الطلب',
                data: {
                    orderId: order.id,
                    oldStatus: oldStatus,
                    newStatus: newStatus,
                    storeTotal: storeTotal,
                    itemsCount: order.orderDetails.length,
                    type: 'order_status_update'
                }
            });

            console.log(`Order status notification sent to customer ${order.customer.id} for order ${order.id}: ${oldStatus} -> ${newStatus}`);

        } catch (error) {
            console.error('Error in sendOrderStatusNotification:', error);
            throw error;
        }
    }
}

module.exports = new StoreProductController();
