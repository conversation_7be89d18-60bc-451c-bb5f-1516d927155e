<div class="container mt-4">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-body">
                    <h4>Welcome, <%= admin.fullName %></h4>
                    <p class="text-muted">Last login: <%= admin.lastLogin ? new Date(admin.lastLogin).toLocaleString() : 'Never' %></p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6 mb-4">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <h5 class="card-title">Total Cities</h5>
                    <h2 class="card-text"><%= countries %></h2>
                    <a href="/admin/countries" class="btn btn-light">Manage Cities</a>
                </div>
            </div>
        </div>
        <div class="col-md-6 mb-4">
            <div class="card bg-secondary text-white">
                <div class="card-body">
                    <h5 class="card-title">Total Areas</h5>
                    <h2 class="card-text"><%= areas %></h2>
                    <a href="/admin/areas" class="btn btn-light">Manage Areas</a>
                </div>
            </div>
        </div>
        <div class="col-md-6 mb-4">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <h5 class="card-title">Total Categories</h5>
                    <h2 class="card-text"><%= categories %></h2>
                    <a href="/admin/categories" class="btn btn-light">Manage Categories</a>
                </div>
            </div>
        </div>
        <div class="col-md-6 mb-4">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <h5 class="card-title">Total Stores</h5>
                    <h2 class="card-text"><%= stores %></h2>
                    <a href="/admin/stores" class="btn btn-light">Manage Stores</a>
                </div>
            </div>
        </div>
        <div class="col-md-6 mb-4">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <h5 class="card-title">Total Customers</h5>
                    <h2 class="card-text"><%= customers %></h2>
                    <a href="/admin/customers" class="btn btn-light">Manage Customers</a>
                </div>
            </div>
        </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex gap-2">
                        <a href="/admin/stores/pending" class="btn btn-warning">
                            Pending Store Approvals
                        </a>
                        <a href="/admin/customers/pending" class="btn btn-info">
                            Pending Customer Approvals
                        </a>
                        <a href="/admin/auth/change-password" class="btn btn-secondary">
                            Change Password
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div> 