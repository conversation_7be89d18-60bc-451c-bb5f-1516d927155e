<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Create New Order</h1>
        <a href="/orders" class="btn btn-secondary">Back to Orders</a>
    </div>

    <div class="row">
        <div class="col-md-8">
            <form action="/orders" method="POST" id="orderForm">
                <div class="mb-3">
                    <label for="customerId" class="form-label">Customer</label>
                    <select class="form-control" id="customerId" name="customerId" required>
                        <option value="">Select a Customer</option>
                        <% customers.forEach(customer => { %>
                            <option value="<%= customer.id %>"><%= customer.name %></option>
                        <% }); %>
                    </select>
                </div>

                <div class="mb-3">
                    <label for="storeId" class="form-label">Store</label>
                    <select class="form-control" id="storeId" name="storeId" required>
                        <option value="">Select a Store</option>
                        <% stores.forEach(store => { %>
                            <option value="<%= store.id %>"><%= store.userName %></option>
                        <% }); %>
                    </select>
                </div>

                <div class="mb-3">
                    <label class="form-label">Products</label>
                    <div id="productList">
                        <div class="product-item border p-3 mb-3">
                            <div class="row">
                                <div class="col-md-6">
                                    <label class="form-label">Product</label>
                                    <select class="form-control" name="orderDetails[0][productId]" required>
                                        <option value="">Select a Product</option>
                                        <% products.forEach(product => { %>
                                            <option value="<%= product.id %>" 
                                                    data-price="<%= product.price %>">
                                                <%= product.name %> - $<%= product.price %>
                                            </option>
                                        <% }); %>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Quantity</label>
                                    <input type="number" class="form-control quantity-input" 
                                           name="orderDetails[0][quantity]" 
                                           min="1" value="1" required>
                                </div>
                                <div class="col-md-2 d-flex align-items-end">
                                    <button type="button" class="btn btn-danger remove-product"
                                            onclick="removeProduct(this)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <button type="button" class="btn btn-secondary" onclick="addProduct()">
                        <i class="fas fa-plus"></i> Add Product
                    </button>
                </div>

                <div class="mb-3">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-control" id="status" name="status" required>
                        <option value="pending">Pending</option>
                        <option value="completed">Completed</option>
                        <option value="cancelled">Cancelled</option>
                    </select>
                </div>

                <div class="mb-3">
                    <label for="notes" class="form-label">Notes</label>
                    <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                </div>

                <button type="submit" class="btn btn-primary">Create Order</button>
            </form>
        </div>
    </div>
</div>

<script>
let productCount = 1;

function addProduct() {
    const template = document.querySelector('.product-item').cloneNode(true);
    template.querySelector('select').name = `orderDetails[${productCount}][productId]`;
    template.querySelector('.quantity-input').name = `orderDetails[${productCount}][quantity]`;
    template.querySelector('select').value = '';
    template.querySelector('.quantity-input').value = 1;
    document.getElementById('productList').appendChild(template);
    productCount++;
}

function removeProduct(button) {
    if (document.querySelectorAll('.product-item').length > 1) {
        button.closest('.product-item').remove();
    }
}
</script> 