<h1>معلومات الزبون</h1>

<table class="table table-bordered">
  <tr>
    <th>الاسم</th>
    <td><%= customer.name %></td>
  </tr>
  <tr>
    <th>رقم الهاتف</th>
    <td><%= customer.phoneNumber %></td>
  </tr>
  <tr>
    <th>الباركود</th>
    <td><%= customer.barcode %></td>
  </tr>
  <% if (customer.areas && customer.areas.length > 0) { %>
    <% customer.areas.forEach(area => { %>
      
        <tr>
          <th>المدينة</th>
          <td><%= area.country.name || '-' %></td>
        </tr>
        <tr>
          <th>المنطقة</th>
          <td><%= area.name %></td>
        </tr>
        <tr>
          <th>العنوان</th>
          <td><%= customer.address || '-' %></td>
        </tr>
      
    <% })} %>
  <tr>
    <th>نسبة الخصم</th>
    <td><%= customer.discountRate %> %</td>
  </tr>
  <tr>
    <th>الحالة</th>
    <td><%= customer.status === 'active' ? 'مفعل' : 'غير مفعل' %></td>
  </tr>
  <tr>
    <th>ملاحظات</th>
    <td><%= customer.notes || '-' %></td>
  </tr>
  <tr>
    <th>تاريخ الإنشاء</th>
    <td><%= new Date(customer.createdAt).toLocaleString() %></td>
  </tr>
</table>

<a href="javascript:history.back()" class="btn btn-secondary">رجوع</a>
