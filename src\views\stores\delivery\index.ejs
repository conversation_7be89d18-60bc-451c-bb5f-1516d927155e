<div class="container mt-4">
  <h3>Deliveries for <%= deliveryPersonName %></h3>

  <!-- ✅ جدول التوصيلات الجارية -->
  <h4>In-Progress Deliveries</h4>
  <table class="table table-bordered">
    <thead>
      <tr>
        <th>Order</th>
        <th>Customer</th>
        <th>Status</th>
        <th>Pickup Time</th>
        <th>Actions</th>
      </tr>
    </thead>
    <tbody>
      <% inProgressDeliveries.forEach(delivery => { %>
        <tr>
          <td>
            <a href="/orders/<%= delivery.order.id %>">
              #<%= delivery.order.id %>
            </a>
          </td>
          <td>
            <a href="/customers/<%= delivery.order.customer.id %>">
              <%= delivery.order.customer.name %>
            </a>
          </td>
          <td><%= delivery.status %></td>
          <td><%= delivery.pickupTime ? delivery.pickupTime.toLocaleString() : '-' %></td>
          
          <td>
              <form action="/store/deliveries/<%= delivery.id %>/complete" method="POST" style="display:inline;">
                <button type="submit" class="btn btn-sm btn-success" onclick="return confirm('هل أنت متأكد من تأكيد التوصيل؟');">
                  تم التوصيل
                </button>
              </form>
            
            <a href="/store/deliveries/<%= delivery.id %>/edit" class="btn btn-sm btn-secondary">Edit</a>
          </td>
        </tr>
      <% }) %>
    </tbody>
  </table>

  <!-- ✅ جدول التوصيلات المكتملة أو الملغاة -->
  <h4>Completed / Cancelled Deliveries</h4>
  <table class="table table-bordered">
    <thead>
      <tr>
        <th>Order</th>
        <th>Customer</th>
        <th>Status</th>
        <th>Delivery Time</th>
      </tr>
    </thead>
    <tbody>
      <% completedDeliveries.forEach(delivery => { %>
        <tr>
          <td>
            <a href="/store/orders/<%= delivery.order.id %>">
              #<%= delivery.order.id %>
            </a>
          </td>
          <td>
            <a href="/store/customers/<%= delivery.order.customer.id %>">
              <%= delivery.order.customer.name %>
            </a>
          </td>
          <td><%= delivery.status %></td>
          <td><%= delivery.deliveryTime ? delivery.deliveryTime.toLocaleString() : '-' %></td>
        </tr>
      <% }) %>
    </tbody>
  </table>
</div>
