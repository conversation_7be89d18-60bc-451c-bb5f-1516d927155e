<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Edit Store</h1>
        <a href="/stores" class="btn btn-secondary">Back to Stores</a>
    </div>

    <div class="row">
        <div class="col-md-6">
            <form action="/stores/<%= store.id %>" method="POST">
                <input type="hidden" name="_method" value="PUT">
                
                <div class="mb-3">
                    <label for="userName" class="form-label">Username</label>
                    <input type="text" class="form-control" id="userName" name="userName" value="<%= store.userName %>" required>
                </div>

                <div class="mb-3">
                    <label for="name" class="form-label">Store Name</label>
                    <input type="text" class="form-control" id="name" name="name" value="<%= store.name %>" required>
                </div>

                <div class="mb-3">
                    <label for="areaId" class="form-label">Area</label>
                    <select class="form-control" id="areaId" name="areaId" required>
                        <option value="">Select an Area</option>
                        <% areas.forEach(area => { %>
                            <option value="<%= area.id %>" <%= store.areaId === area.id ? 'selected' : '' %>>
                                <%= area.name %>
                            </option>
                        <% }); %>
                    </select>
                </div>

                <div class="mb-3">
                    <label for="address" class="form-label">Address</label>
                    <textarea class="form-control" id="address" name="address" rows="3" required><%= store.address %></textarea>
                </div>

                <div class="mb-3">
                    <label for="phoneNumber" class="form-label">Phone Number</label>
                    <input type="tel" class="form-control" id="phoneNumber" name="phoneNumber" value="<%= store.phoneNumber %>">
                </div>

                <div class="mb-3">
                    <label for="notes" class="form-label">Notes</label>
                    <textarea class="form-control" id="notes" name="notes" rows="3"><%= store.notes || '' %></textarea>
                </div>

                <button type="submit" class="btn btn-primary">Update Store</button>
            </form>
        </div>
    </div>
</div> 