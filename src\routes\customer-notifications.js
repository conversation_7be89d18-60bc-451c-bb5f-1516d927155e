const express = require('express');
const router = express.Router();
const customerNotificationsController = require('../controllers/CustomerNotificationsController');

// Middleware للتحقق من تسجيل دخول العميل
function requireCustomerAuth(req, res, next) {
    if (req.session && req.session.customerId) {
        return next();
    }
    res.redirect('/customers/login');
}

// تطبيق middleware على جميع الطرق
router.use(requireCustomerAuth);

// Web Routes
router.get('/', customerNotificationsController.index.bind(customerNotificationsController));
router.get('/:id', customerNotificationsController.show.bind(customerNotificationsController));
router.post('/:id/read', customerNotificationsController.markAsRead.bind(customerNotificationsController));
router.post('/mark-all-read', customerNotificationsController.markAllAsRead.bind(customerNotificationsController));
router.post('/:id/delete', customerNotificationsController.delete.bind(customerNotificationsController));

// API Routes
router.get('/api/unread-count', customerNotificationsController.getUnreadCount.bind(customerNotificationsController));
router.get('/api/recent', customerNotificationsController.getRecentNotifications.bind(customerNotificationsController));

module.exports = router;
