const express = require('express');
const router = express.Router();
const customerNotificationsController = require('../controllers/CustomerNotificationsController');
const { auth } = require('../middleware/auth');

// تطبيق middleware على جميع الطرق
router.use(auth.customer);

// Web Routes
router.get('/', customerNotificationsController.index.bind(customerNotificationsController));
router.get('/:id', customerNotificationsController.show.bind(customerNotificationsController));
router.post('/:id/read', customerNotificationsController.markAsRead.bind(customerNotificationsController));
router.post('/mark-all-read', customerNotificationsController.markAllAsRead.bind(customerNotificationsController));
router.post('/:id/delete', customerNotificationsController.delete.bind(customerNotificationsController));

// API Routes
router.get('/api/unread-count', customerNotificationsController.getUnreadCount.bind(customerNotificationsController));
router.get('/api/recent', customerNotificationsController.getRecentNotifications.bind(customerNotificationsController));

module.exports = router;
