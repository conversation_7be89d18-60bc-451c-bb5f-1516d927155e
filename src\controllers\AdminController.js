const { Admin, Store, Customer, Country, Area, Category, Order, OrderDetail, Product, DeliveryPerson ,Delivery} = require('../models');

class AdminStoreController {
  // Admin Dashboard
  async dashboard(req, res) {
    try {
      const [stores, customers, admin, countries, areas, categories] = await Promise.all([
        Store.count(),
        Customer.count(),
        Admin.findByPk(req.user.id),
        Country.count(),
        Area.count(),
        Category.count()
      ]);

      res.render('admin/dashboard', {
        stores,
        customers,
        admin,
        countries,
        areas,
        categories
      });
    } catch (error) {
      res.status(500).render('error', { error });
    }
  }

  // قائمة المتاجر
  async index(req, res) {
    const limit = 20;
    const page = parseInt(req.query.page) || 1;
    const offset = (page - 1) * limit;

    try {
      const { count, rows: stores } = await Store.findAndCountAll({
        include: [
          { model: Category, as: 'categories' },
          { model: Area, as: 'area' },
          { model: Order, as: 'orders' },
          { model: DeliveryPerson, as: 'deliveryPeople' }
        ],
        order: [['createdAt', 'DESC']],
        limit,
        offset
      });

      const totalPages = Math.ceil(count / limit);

      res.render('admin/stores/index', {
        stores,
        currentPage: page,
        totalPages
      });
    } catch (error) {
      console.error('Error fetching stores:', error);
      res.status(500).render('error', { error: { message: 'Unable to fetch stores' } });
    }
  }

  // عرض نموذج إنشاء متجر جديد
  async create(req, res) {
    try {
      const [areas, categories] = await Promise.all([
        Area.findAll(),
        Category.findAll()
      ]);
      res.render('admin/stores/create', { areas, categories });
    } catch (error) {
      console.error('Error fetching data for create store:', error);
      res.status(500).render('error', { error });
    }
  }

  // حفظ متجر جديد
  async store(req, res) {
    try {
      const { categoryIds, ...storeData } = req.body;
      const store = await Store.create(storeData);
      if (categoryIds) {
        const ids = Array.isArray(categoryIds) ? categoryIds : [categoryIds];
        await store.setCategories(ids);
      }
      req.flash('success', 'Store created successfully');
      res.redirect('/admin/stores');
    } catch (error) {
      console.error('Error creating store:', error);
      res.status(500).render('error', { error });
    }
  }

  // عرض نموذج تعديل متجر
  async edit(req, res) {
    try {
      const [store, areas, categories] = await Promise.all([
        Store.findByPk(req.params.id, { include: ['categories', 'area'] }),
        Area.findAll(),
        Category.findAll()
      ]);
      if (!store) {
        return res.status(404).render('error', { error: { status: 404, message: 'Store not found' } });
      }
      res.render('admin/stores/edit', { store, areas, categories });
    } catch (error) {
      console.error('Error loading edit store:', error);
      res.status(500).render('error', { error: { message: 'Unable to load store data' } });
    }
  }

  // تحديث بيانات متجر
  async update(req, res) {
    try {
      const { categoryIds, ...storeData } = req.body;
      const store = await Store.findByPk(req.params.id);
      if (!store) {
        return res.status(404).render('error', { error: { status: 404, message: 'Store not found' } });
      }
      await store.update(storeData);
      if (categoryIds) {
        const ids = Array.isArray(categoryIds) ? categoryIds : [categoryIds];
        await store.setCategories(ids);
      }
      req.flash('success', 'Store updated successfully');
      res.redirect('/admin/stores');
    } catch (error) {
      console.error('Error updating store:', error);
      res.status(500).render('error', { error: { message: 'Unable to update store' } });
    }
  }

  // حذف متجر
  async delete(req, res) {
    try {
      const store = await Store.findByPk(req.params.id);
      if (!store) {
        return res.status(404).render('error', { error: { status: 404, message: 'Store not found' } });
      }
      await store.destroy();
      req.flash('success', 'Store deleted successfully');
      res.redirect('/admin/stores');
    } catch (error) {
      console.error('Error deleting store:', error);
      res.status(500).render('error', { error: { message: 'Unable to delete store' } });
    }
  }


  // تحديث حالة متجر
  async updateStatus(req, res) {
    try {
      const { id } = req.params;
      const { status } = req.body;
      if (!['active', 'pending', 'inactive', 'banned'].includes(status)) {
        req.flash('error', 'Invalid status value');
        return res.redirect('/admin/stores/pending');
      }
      const store = await Store.findByPk(id);
      if (!store) {
        return res.status(404).render('error', { error: { status: 404, message: 'Store not found' } });
      }
      await store.update({ status });
      req.flash('success', `Store status updated to ${status}`);
      res.redirect('/admin/stores/pending');
    } catch (error) {
      console.error('Error updating store status:', error);
      res.status(500).render('error', { error: { message: 'Unable to update store status' } });
    }
  }


 // عرض الطلبات لمتجر معين مع pagination
async orders(req, res) {
  const storeId = req.params.id;
  const page = parseInt(req.query.page) || 1;
  const limit = 20;
  const offset = (page - 1) * limit;

  try {
    const store = await Store.findByPk(storeId);
    if (!store) {
      return res.status(404).render('error', { error: { message: 'Store not found' } });
    }

    // جلب الطلبات المرتبطة مع العملاء باستخدام limit و offset
    const { count, rows: orders } = await Order.findAndCountAll({
      where: { storeId },
      include: ['customer'],
      order: [['createdAt', 'DESC']],
      limit,
      offset
    });

    const totalPages = Math.ceil(count / limit);

    res.render('admin/orders', {
      store,
      orders,
      currentPage: page,
      totalPages
    });

  } catch (error) {
    console.error('Error fetching store orders:', error);
    res.status(500).render('error', { error: { message: 'Unable to fetch store orders' } });
  }
}


  // عرض تفاصيل الطلب
  async show(req, res) {
    try {
      const order = await Order.findByPk(req.params.id, {
        include: [
          { model: Customer, as: 'customer' },
          { model: Store, as: 'store' },
          { model: OrderDetail, as: 'orderDetails', include: [{ model: Product, as: 'product' }] }
        ]
      });
      if (!order) {
        return res.status(404).render('error', { error: { message: 'Order not found' } });
      }
      res.render('admin/orders/show', { order });
    } catch (error) {
      console.error('Error fetching order details:', error);
      res.status(500).render('error', { error: { message: 'Unable to fetch order details' } });
    }
  }

// عرض السائقين المرتبطين بمتجر معين مع pagination
async storeDrivers(req, res) {
  const { id: storeId } = req.params;
  const page = parseInt(req.query.page) || 1;
  const limit = 20;
  const offset = (page - 1) * limit;

  try {
    // التأكد من وجود المتجر فقط (بدون تحميل السائقين من هنا)
    const store = await Store.findByPk(storeId);
    if (!store) {
      return res.status(404).render('error', { error: { message: 'Store not found' } });
    }

    // جلب السائقين المرتبطين بالمتجر باستخدام limit/offset
    const { count, rows: deliveryPeoples } = await DeliveryPerson.findAndCountAll({
      where: { storeId },
      include: [{ model: Delivery, as: 'deliveries' }],
      order: [['createdAt', 'DESC']],
      limit,
      offset
    });

    const totalPages = Math.ceil(count / limit);

    res.render('admin/stores/drivers', {
      store,
      deliveryPeoples,
      currentPage: page,
      totalPages
    });

  } catch (error) {
    console.error('Error fetching drivers:', error);
    res.status(500).render('error', { error: { message: 'Unable to fetch drivers' } });
  }
}


async personDeliveriesForStore(req, res) {
  const { storeId, personId } = req.params;
  const page = parseInt(req.query.page) || 1;
  const limit = 20;
  const offset = (page - 1) * limit;

  try {
    const { count, rows: deliveries } = await Delivery.findAndCountAll({
      where: { deliveryPersonId: personId },
      include: [
        {
          model: Order,
          as: 'order',
          where: { storeId },
          include: ['customer']
        },
        {
          model: DeliveryPerson,
          as: 'courier'
        }
      ],
      order: [['createdAt', 'DESC']],
      limit,
      offset
    });

    const totalPages = Math.ceil(count / limit);
    const driverName = deliveries[0]?.courier?.name || 'Driver';

    res.render('admin/stores/driver-deliveries', {
      storeId,
      personId,
      driverName,
      deliveries,
      currentPage: page,
      totalPages
    });

  } catch (error) {
    console.error('Error fetching deliveries:', error);
    res.status(500).render('error', { error: { message: 'Unable to fetch deliveries for driver' } });
  }
}



}

module.exports = new AdminStoreController();
