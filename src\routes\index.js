const express = require('express');
const router = express.Router();

const homeRouter = require('./home');
const adminRouter = require('./admin');
const adminAuth = require('./admin-auth');  // استيراد الراوتر الخاص بـ admin auth
const customersRouter = require('./customers');
const imageRouter = require('./images');
const notificationsRouter = require('./notifications');
const storeNotificationsRouter = require('./store-notifications');
const customerNotificationsRouter = require('./customer-notifications');
const storesRouter = require('./stores');
const testRouter = require('./test');

//router.use('/', homeRouter);
router.use('/admin/auth', adminAuth.router); // <== ربط مسار تسجيل دخول الأدمن (المسارات المفتوحة)
router.use('/admin', adminRouter);          // <== باقي مسارات الأدمن (محمي بميدلوير requireAdminAuth)
router.use('/customers', customersRouter);
router.use('/customers/notifications', customerNotificationsRouter);
router.use('/images', imageRouter);
router.use('/notifications', notificationsRouter);
router.use('/store', storesRouter);
router.use('/store/notifications', storeNotificationsRouter);
router.use('/test', testRouter);

module.exports = router;
