const { Store, Area, Product, Order, DeliveryPerson , OrderDetail, Customer, Image, Delivery} = require('../models');
const upload = require('../middleware/upload');
const bcrypt = require('bcryptjs');
const Joi = require('joi');
const { generateAuthTokens } = require('../utils/jwt');

class StoreAuthController {
    // عرض نموذج تسجيل الدخول
    async showLogin(req, res) {
        res.render('stores/auth/login');
    }

    // عرض نموذج التسجيل مع جلب المناطق
    async showRegister(req, res) {
        try {
            const areas = await Area.findAll();
            res.render('stores/auth/register', { areas });
        } catch (error) {
            res.status(500).render('error', { error });
        }
    }

    // التحقق من صحة بيانات تسجيل الدخول باستخدام Joi
    async validateLogin(data) {
        const schema = Joi.object({
            userName: Joi.string().required(),
            password: Joi.string().required()
        });
        return schema.validateAsync(data);
    }

    // تسجيل الدخول
    async login(req, res) {
        try {
            console.log(req.body);
            await this.validateLogin(req.body);

            const { userName, password } = req.body;
            const store = await Store.findOne({ where: { userName } });

            if (!store) {
                return res.render('stores/auth/login', {
                    error: 'Invalid username or password'
                });
            }

            const isValidPassword = await bcrypt.compare(password, store.password);
            if (!isValidPassword) {
                return res.render('stores/auth/login', {
                    error: 'Invalid username or password'
                });
            }

            if (store.status !== 'active') {
                return res.render('stores/auth/login', {
                    error: 'Your store account is not active. Please contact support.'
                });
            }

            // إنشاء JWT tokens
            const tokens = generateAuthTokens(store, 'store');

            // تعيين الـ token في الـ cookies
            res.cookie('token', tokens.accessToken, {
                httpOnly: true,
                secure: process.env.NODE_ENV === 'production',
                maxAge: 24 * 60 * 60 * 1000, // 24 hours
                sameSite: 'strict'
            });

            res.cookie('refreshToken', tokens.refreshToken, {
                httpOnly: true,
                secure: process.env.NODE_ENV === 'production',
                maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
                sameSite: 'strict'
            });

            res.redirect('/store/dashboard');
        } catch (error) {
            console.error('Login error:', error);
            res.render('stores/auth/login', {
                error: error.isJoi ? 'Invalid input data' : 'An error occurred during login'
            });
        }
    }

    // التحقق من صحة بيانات التسجيل باستخدام Joi
    async validateRegister(data) {
        const schema = Joi.object({
            userName: Joi.string().min(3).max(30).required(),
            password: Joi.string().min(6).required(),
            areaId: Joi.number().integer().required(),
            phoneNumber: Joi.string().pattern(/^\+?\d{7,15}$/).required(),
            address: Joi.string().min(5).required()
        });
        return schema.validateAsync(data);
    }

    // التسجيل
    async register(req, res) {
        try {
            await this.validateRegister(req.body);

            const { userName, password, areaId, phoneNumber, address } = req.body;

            // التحقق من وجود اسم المستخدم مسبقًا
            const existingStore = await Store.findOne({ where: { userName } });
            if (existingStore) {
                const areas = await Area.findAll();
                return res.render('stores/auth/register', {
                    error: 'Username already exists',
                    areas
                });
            }

            // تشفير كلمة المرور قبل الحفظ
            const hashedPassword = await bcrypt.hash(password, 10);

            // إنشاء سجل المتجر
            await Store.create({
                userName,
                password: hashedPassword,
                areaId,
                phoneNumber,
                address,
                status: 'pending'
            });

            res.render('stores/auth/login', {
                success: 'Registration successful! Please wait for account activation.'
            });
        } catch (error) {
            console.error('Register error:', error);
            const areas = await Area.findAll();
            res.render('stores/auth/register', {
                error: error.isJoi ? 'Invalid input data' : 'An error occurred during registration',
                areas
            });
        }
    }

    // تسجيل الخروج
    async logout(req, res) {
        // مسح الـ cookies
        res.clearCookie('token');
        res.clearCookie('refreshToken');

        res.redirect('/store/login?success=' + encodeURIComponent('Logged out successfully'));
    }

    // لوحة تحكم المتجر (تتطلب تسجيل دخول)
    async dashboard(req, res) {
        try {
            const store = await Store.findByPk(req.user.id, {
                include: ['area']
            });

            if (!store) {
                return res.redirect('/stores/login');
            }

            // المنتجات التابعة للمتجر
            const productsCount = await Product.count({
                where: { storeId: store.id }
            });

            // جميع الطلبات التابعة للمتجر
            const ordersCount = await Order.count({
                where: { storeId: store.id,
                  status: ['delivered', 'cancelled']} // نعرض فقط الطلبات المسلمة أو الملغاة }
            });

            // الطلبات في حالة انتظار
            const pendingOrdersCount = await Order.count({
                where: {
                    storeId: store.id,
                    status: 'pending'
                }
            });
            const deliveryPeopleCount = await DeliveryPerson.count({
                where: {
                    storeId: store.id
                }
            });
            const deliveriesCount = await Delivery.count({
                include: [{
                  model: Order,
                  as: 'order',
                  where: { storeId : store.id}
                }]
              });

            res.render('stores/dashboard', {
                store,
                productsCount,
                ordersCount,
                pendingOrdersCount,
                deliveryPeopleCount,
                deliveriesCount
            });
        } catch (error) {
            console.error('Dashboard error:', error);
            res.status(500).render('error', { error });
        }
    }

    async products(req, res) {
      const storeId = req.user.id;
      const page = parseInt(req.query.page) || 1;
      const limit = 20;
      const offset = (page - 1) * limit;

      // استعلام مع العد والحد مع الأوفست
      const { count, rows: products } = await Product.findAndCountAll({
        where: { storeId },
        limit,
        offset,
        order: [['createdAt', 'DESC']],
        include: [{ model: Image, as: 'images' }]
      });

      const totalPages = Math.ceil(count / limit);

      res.render('stores/products', { products, currentPage: page, totalPages });
    }

      // عرض تفاصيل المنتج
    async editProduct(req, res) {
        const product = await Product.findByPk(req.params.id, {
        include: ['images']
        });

        if (!product) {
        return res.status(404).render('error', { error: { message: 'Product not found' } });
        }

        res.render('stores/productshow', { product });
    }

    // رفع الصور
    async updateProduct(req, res) {
        try {
          const productId = req.params.id;
          const files = req.files;            // multer يحفظ المصفوفة هنا
          if (!Array.isArray(files) || files.length === 0) {
            return res.status(400).render('error', { error: { message: 'لم يتم رفع ملفات بشكل صحيح' } });
          }

          // تحضير بيانات الحفظ
          const imagePaths = files.map(file => ({
            productId,
            image: `/uploads/${file.filename}`
          }));

          // احفظها في جدول الصور
          await Image.bulkCreate(imagePaths);

          res.redirect(`/store/products/${productId}/edit`);
        } catch (err) {
          console.error('Upload error:', err);
          res.status(500).render('error', { error: { message: 'خطأ أثناء رفع الصور' } });
        }
      }



      async orders(req, res) {
        const storeId = req.user.id;
        const limit = 20;
        const currentPage = parseInt(req.query.page) || 1;
        const offset = (currentPage - 1) * limit;

        try {
          const { count, rows: orders } = await Order.findAndCountAll({
            where: {
              storeId,
              status: ['delivered', 'cancelled']
            },
            include: [
              { model: Customer, as: 'customer' },
              { model: Delivery, as: 'delivery', required: false }
            ],
            order: [['createdAt', 'DESC']],
            limit,
            offset
          });

          const totalPages = Math.ceil(count / limit);

          res.render('stores/orders', {
            orders,
            currentPage,
            totalPages
          });
        } catch (error) {
          console.error("Error fetching orders:", error);
          res.status(500).render('error', {
            error: { message: 'Unable to fetch orders' }
          });
        }
      }


      async pendingOrders(req, res) {
        const storeId = req.user.id;
        const limit = 20;
        const currentPage = parseInt(req.query.page) || 1;
        const offset = (currentPage - 1) * limit;

        try {
          const { count, rows: orders } = await Order.findAndCountAll({
            where: {
              storeId,
              status: 'pending',
            },
            include: ['customer'],
            order: [['createdAt', 'DESC']],
            limit,
            offset
          });

          const totalPages = Math.ceil(count / limit);

          res.render('stores/pending', {
            orders,
            currentPage,
            totalPages
          });
        } catch (error) {
          console.error("Error fetching pending orders:", error);
          res.status(500).render('error', {
            error: { message: 'Unable to fetch pending orders' }
          });
        }
      }


      async show(req, res) {
        try {
          const order = await Order.findOne({
            where: { id: req.params.id, storeId: req.user.id },
            include: [
              {
                model: OrderDetail,
                as: 'orderDetails',
                include: [{ model: Product, as: 'product' }]
              },
              { model: Customer, as: 'customer' }
            ]
          });

          if (!order) {
            return res.status(404).render('error', { error: { message: 'Order not found' } });
          }

          res.render('stores/ordershow', { order });
        } catch (error) {
          console.error('Error fetching order details:', error);
          res.status(500).render('error', { error: { message: 'Unable to fetch order details' } });
        }
      }

      async acceptOrder(req, res) {
        const orderId = req.params.id;
        const storeId = req.user.id;

        try {
          // تحديث حالة الطلب فقط إذا كان من نفس المتجر
          const [updated] = await Order.update(
            { status: 'delivered' },  // أو القيمة التي تمثل "تم التوصيل" عندك
            {
              where: {
                id: orderId,
                storeId
              }
            }
          );

          if (updated) {
            res.redirect('/store/orders/pending'); // ترجع للصفحة السابقة بعد التحديث
          } else {
            res.status(404).send('Order not found or not authorized');
          }
        } catch (error) {
          console.error('Error accepting order:', error);
          res.status(500).send('Server error');
        }
      }

}

// Middleware للتحقق من تسجيل دخول المتجر (deprecated - use auth.store instead)
function ensureStoreLoggedIn(req, res, next) {
    const { auth } = require('../middleware/auth');
    return auth.store(req, res, next);
}

module.exports = {
    controller: new StoreAuthController(),
    ensureStoreLoggedIn
};
