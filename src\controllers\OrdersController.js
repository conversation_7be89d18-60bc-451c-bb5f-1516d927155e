const BaseController = require('./BaseController');
const { Order, Customer, Store, Product, OrderDetail, sequelize } = require('../models');
const { executeWithRetry } = require('../utils/databaseUtils');
const NotificationService = require('../services/NotificationService');
const logger = require('../utils/logger');

class OrdersController extends BaseController {
    constructor() {
        super(Order, 'orders');
    }

    async index(req, res) {
        try {
            // استخدام executeWithRetry لضمان استقرار العمليات على قاعدة البيانات
            const orders = await executeWithRetry(async (transaction) => {
                // التحقق من وجود جدول الطلبات
                try {
                    await sequelize.query('SELECT TOP 1 * FROM Orders', {
                        type: sequelize.QueryTypes.SELECT,
                        transaction
                    });
                } catch (tableError) {
                    console.error('Error checking Orders table:', tableError);
                    throw new Error('Orders table may not exist or is not accessible');
                }

                // جلب الطلبات مع العلاقات اللازمة داخل المعاملة
                return await Order.findAll({
                    include: [
                        {
                            model: Customer,
                            as: 'customer',
                            required: false,
                            attributes: ['id', 'name', 'email']
                        },
                        {
                            model: Store,
                            as: 'store',
                            required: false,
                            attributes: ['id', 'name']
                        },
                        {
                            model: OrderDetail,
                            as: 'orderDetails',
                            required: false,
                            attributes: ['id', 'quantity', 'price'],
                            include: [{
                                model: Product,
                                as: 'product',
                                required: false,
                                attributes: ['id', 'name']
                            }]
                        }
                    ],
                    attributes: ['id', 'totalPrice', 'status', 'createdAt'],
                    limit: 100,
                    order: [['createdAt', 'DESC']],
                    transaction
                });
            });

            res.render('orders/index', { orders });
        } catch (error) {
            console.error('Error in OrdersController.index:', {
                message: error.message,
                stack: error.stack,
                sql: error.sql,
                parameters: error.parameters,
                retriesAttempted: error.retriesAttempted
            });

            res.status(500).render('error', {
                error: {
                    message: 'An error occurred while fetching orders',
                    details: process.env.NODE_ENV === 'development' ? error.message : null
                }
            });
        }
    }

    async create(req, res) {
        try {
            const [customers, stores, products] = await Promise.all([
                executeWithRetry(async () => Customer.findAll({ where: { status: 'active' } }),
                    { useTransaction: false }),
                executeWithRetry(async () => Store.findAll({ where: { status: 'active' } }),
                    { useTransaction: false }),
                executeWithRetry(async () => Product.findAll({
                    where: {
                        status: 'active',
                        quantity: { [sequelize.Op.gt]: 0 }
                    }}),
                    { useTransaction: false })
            ]);

            res.render('orders/create', { customers, stores, products });
        } catch (error) {
            console.error('Error in OrdersController.create:', error);
            res.status(500).render('error', {
                error: {
                    message: 'An error occurred while loading the create form',
                    details: process.env.NODE_ENV === 'development' ? error.message : null
                }
            });
        }
    }

    async store(req, res) {
        try {
            let createdOrder = null;

            await executeWithRetry(async (transaction) => {
                const { orderDetails, ...orderData } = req.body;
                const order = await Order.create(orderData, { transaction });
                createdOrder = order;

                if (Array.isArray(orderDetails)) {
                    const details = orderDetails.map(detail => ({
                        ...detail,
                        orderId: order.id
                    }));
                    await OrderDetail.bulkCreate(details, { transaction });
                }
            });

            // إرسال إشعار للمتجر عن الطلب الجديد
            if (createdOrder) {
                try {
                    await this.sendNewOrderNotifications(createdOrder);
                } catch (notificationError) {
                    logger.error('Error sending order notifications:', notificationError);
                    // لا نوقف العملية إذا فشل الإشعار
                }
            }

            req.flash('success', 'Order created successfully');
            res.redirect('/orders');
        } catch (error) {
            console.error('Error in OrdersController.store:', error);
            res.status(500).render('error', {
                error: {
                    message: 'An error occurred while creating the order',
                    details: process.env.NODE_ENV === 'development' ? error.message : null
                }
            });
        }
    }

    /**
     * إرسال إشعارات الطلب الجديد
     */
    async sendNewOrderNotifications(order) {
        try {
            // جلب تفاصيل الطلب مع العلاقات
            const orderWithDetails = await Order.findByPk(order.id, {
                include: [
                    {
                        model: Customer,
                        as: 'customer',
                        attributes: ['id', 'name', 'email', 'phone']
                    },
                    {
                        model: OrderDetail,
                        as: 'orderDetails',
                        include: [{
                            model: Product,
                            as: 'product',
                            include: [{
                                model: Store,
                                as: 'store',
                                attributes: ['id', 'name', 'userName', 'email']
                            }]
                        }]
                    }
                ]
            });

            if (!orderWithDetails) {
                logger.error('Order not found for notifications:', order.id);
                return;
            }

            // تجميع المتاجر المشاركة في الطلب
            const storesInOrder = new Map();
            let totalAmount = 0;

            orderWithDetails.orderDetails.forEach(detail => {
                const store = detail.product.store;
                const itemTotal = detail.quantity * detail.price;
                totalAmount += itemTotal;

                if (!storesInOrder.has(store.id)) {
                    storesInOrder.set(store.id, {
                        store: store,
                        items: [],
                        totalAmount: 0
                    });
                }

                const storeData = storesInOrder.get(store.id);
                storeData.items.push({
                    product: detail.product.name,
                    quantity: detail.quantity,
                    price: detail.price,
                    total: itemTotal
                });
                storeData.totalAmount += itemTotal;
            });

            // إرسال إشعار لكل متجر
            for (const [storeId, storeData] of storesInOrder) {
                const itemsList = storeData.items.map(item =>
                    `${item.product} (${item.quantity} × ${item.price} ريال)`
                ).join('\n');

                await NotificationService.notifyStore(storeId, {
                    title: 'طلب جديد يحتاج للمعالجة',
                    message: `لديك طلب جديد رقم #${orderWithDetails.id} من العميل ${orderWithDetails.customer.name}\n\nالمنتجات:\n${itemsList}\n\nإجمالي المبلغ: ${storeData.totalAmount} ريال`,
                    type: 'order',
                    priority: 'high',
                    actionUrl: `/store/orders/${orderWithDetails.id}`,
                    actionText: 'معالجة الطلب',
                    data: {
                        orderId: orderWithDetails.id,
                        customerId: orderWithDetails.customer.id,
                        customerName: orderWithDetails.customer.name,
                        totalAmount: storeData.totalAmount,
                        itemsCount: storeData.items.length,
                        type: 'new_order'
                    }
                });
            }

            // إرسال إشعار تأكيد للعميل
            await NotificationService.notifyCustomer(orderWithDetails.customer.id, {
                title: 'تم إنشاء طلبك بنجاح',
                message: `تم إنشاء طلبك رقم #${orderWithDetails.id} بنجاح بإجمالي ${totalAmount} ريال. سيتم معالجته من قبل المتاجر قريباً.`,
                type: 'success',
                priority: 'normal',
                actionUrl: `/customers/orders/${orderWithDetails.id}`,
                actionText: 'عرض الطلب',
                data: {
                    orderId: orderWithDetails.id,
                    totalAmount: totalAmount,
                    storesCount: storesInOrder.size,
                    type: 'order_created'
                }
            });

            logger.info(`Order notifications sent for order ${orderWithDetails.id} to ${storesInOrder.size} stores and customer ${orderWithDetails.customer.id}`);

        } catch (error) {
            logger.error('Error in sendNewOrderNotifications:', error);
            throw error;
        }
    }

    async show(req, res) {
        try {
            const order = await executeWithRetry(async (transaction) => {
                return await Order.findByPk(req.params.id, {
                    include: [
                        {
                            model: Customer,
                            as: 'customer',
                            attributes: ['id', 'name', 'email', 'phone']
                        },
                        {
                            model: Store,
                            as: 'store',
                            attributes: ['id', 'name']
                        },
                        {
                            model: OrderDetail,
                            as: 'orderDetails',
                            include: [{
                                model: Product,
                                as: 'product',
                                attributes: ['id', 'name', 'price']
                            }]
                        }
                    ],
                    transaction
                });
            });

            if (!order) {
                return res.status(404).render('error', {
                    error: { message: 'Order not found' }
                });
            }

            res.render('orders/show', { order });
        } catch (error) {
            console.error('Error in OrdersController.show:', error);
            res.status(500).render('error', {
                error: {
                    message: 'An error occurred while fetching the order',
                    details: process.env.NODE_ENV === 'development' ? error.message : null
                }
            });
        }
    }
}

module.exports = new OrdersController();
