// هذا ملف models/index.js المعدل بشكل نهائي
'use strict';

const fs = require('fs');
const path = require('path');
const Sequelize = require('sequelize');
const process = require('process');

const basename = path.basename(__filename);
const env = process.env.NODE_ENV || 'development';
const configFile = require('../config/config.js');
const config = configFile[env];

if (!config) {
  throw new Error(`❌ Config for environment "${env}" is not defined in config.js`);
}

const db = {};
let sequelize;
let connectionAttempts = 0;
const maxConnectionAttempts = config.retry?.max || 3;

// إعدادات الاتصال
const sequelizeOptions = {
  ...config,
  pool: {
    ...(config.pool || {}),
    handleDisconnects: true,
    idle: 10000,
    acquire: 30000,
    evict: 1000
  }
};

if (config.use_env_variable) {
  sequelize = new Sequelize(process.env[config.use_env_variable], sequelizeOptions);
} else {
  sequelize = new Sequelize(config.database, config.username, config.password, sequelizeOptions);
}

// التحقق من الاتصال
async function initializeDatabase() {
  try {
    await Promise.race([
      sequelize.authenticate(),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Database connection timeout')), 30000)
      )
    ]);
    console.log('✅ Database connection has been established successfully.');
    return true;
  } catch (error) {
    connectionAttempts++;
    console.error(`❌ Attempt ${connectionAttempts} failed:`, error.message);

    if (connectionAttempts < maxConnectionAttempts) {
      const backoffTime = Math.min(1000 * Math.pow(2, connectionAttempts), 10000);
      console.log(`🔁 Retrying in ${backoffTime / 1000} seconds...`);
      await new Promise(resolve => setTimeout(resolve, backoffTime));
      return await initializeDatabase();
    }

    console.error('🚫 Max attempts reached. Could not connect to DB.');
    throw error;
  }
}

// أول محاولة للاتصال
initializeDatabase().catch(err => {
  console.error('❌ Initial DB connection failed:', err.message);
  if (process.env.NODE_ENV === 'production') process.exit(1);
});

// تحميل الموديلات
const modelFiles = fs.readdirSync(__dirname).filter(file =>
  file.indexOf('.') !== 0 &&
  file !== basename &&
  file.slice(-3) === '.js' &&
  file.indexOf('.test.js') === -1
);

for (const file of modelFiles) {
  try {
    const model = require(path.join(__dirname, file))(sequelize, Sequelize.DataTypes);
    db[model.name] = model;
  } catch (error) {
    console.error(`❌ Error loading model ${file}:`, error.message);
  }
}

// العلاقات
Object.keys(db).forEach(modelName => {
  if (db[modelName].associate) {
    try {
      db[modelName].associate(db);
    } catch (error) {
      console.error(`❌ Error setting up associations for ${modelName}:`, error.message);
    }
  }
});

console.log('✅ Models loaded:', Object.keys(db));

db.sequelize = sequelize;
db.Sequelize = Sequelize;

module.exports = db;