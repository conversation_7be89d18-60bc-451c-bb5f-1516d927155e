/**
 * سكريبت اختبار API للتأكد من عمل جميع المسارات
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3001/api';
let authToken = null;

// إعداد axios
const api = axios.create({
    baseURL: BASE_URL,
    timeout: 10000,
    headers: {
        'Content-Type': 'application/json'
    }
});

// إضافة التوكن تلقائياً
api.interceptors.request.use(config => {
    if (authToken) {
        config.headers.Authorization = `Bearer ${authToken}`;
    }
    return config;
});

// معالجة الأخطاء
api.interceptors.response.use(
    response => response,
    error => {
        console.error('❌ خطأ في API:', error.response?.data?.message || error.message);
        return Promise.reject(error);
    }
);

async function testAPI() {
    console.log('🚀 بدء اختبار API نظام إدارة المتاجر الذكي\n');

    try {
        // 1. اختبار معلومات API
        console.log('📋 اختبار معلومات API...');
        const info = await api.get('/');
        console.log('✅ معلومات API:', info.data.data.name);

        // 2. اختبار فحص الصحة
        console.log('\n💓 اختبار فحص الصحة...');
        const health = await api.get('/health');
        console.log('✅ حالة API:', health.data.data.status);

        // 3. اختبار الفئات
        console.log('\n📂 اختبار جلب الفئات...');
        const categories = await api.get('/categories');
        console.log('✅ عدد الفئات:', categories.data.data.categories.length);

        // 4. اختبار المناطق
        console.log('\n🗺️ اختبار جلب المناطق...');
        const areas = await api.get('/areas');
        console.log('✅ عدد المناطق:', areas.data.data.areas.length);

        // 5. اختبار المدن
        console.log('\n🏙️ اختبار جلب المدن...');
        const countries = await api.get('/countries');
        console.log('✅ عدد المدن:', countries.data.data.countries.length);

        // 6. اختبار المتاجر
        console.log('\n🏪 اختبار جلب المتاجر...');
        const stores = await api.get('/stores?limit=5');
        console.log('✅ عدد المتاجر:', stores.data.data.stores.length);

        // 7. اختبار المنتجات
        console.log('\n📦 اختبار جلب المنتجات...');
        const products = await api.get('/products?limit=5');
        console.log('✅ عدد المنتجات:', products.data.data.products.length);

        // 8. اختبار البحث العام
        console.log('\n🔍 اختبار البحث العام...');
        const search = await api.get('/search?q=test&limit=3');
        console.log('✅ نتائج البحث متاحة');

        // 9. اختبار تسجيل عميل جديد
        console.log('\n👤 اختبار تسجيل عميل جديد...');
        const testCustomer = {
            name: 'عميل تجريبي',
            email: `test${Date.now()}@example.com`,
            phone: '01234567890',
            password: 'password123',
            address: 'عنوان تجريبي',
            areaId: 1
        };

        try {
            const register = await api.post('/customers/register', testCustomer);
            authToken = register.data.data.token;
            console.log('✅ تم تسجيل العميل بنجاح');

            // 10. اختبار جلب بيانات العميل
            console.log('\n📋 اختبار جلب بيانات العميل...');
            const profile = await api.get('/customers/profile');
            console.log('✅ تم جلب بيانات العميل:', profile.data.data.customer.name);

            // 11. اختبار الإشعارات
            console.log('\n🔔 اختبار الإشعارات...');
            const notifications = await api.get('/notifications');
            console.log('✅ تم جلب الإشعارات');

            // 12. اختبار عدد الإشعارات غير المقروءة
            const unreadCount = await api.get('/notifications/unread-count');
            console.log('✅ عدد الإشعارات غير المقروءة:', unreadCount.data.data.unreadCount);

            // 13. اختبار إحصائيات الطلبات
            console.log('\n📊 اختبار إحصائيات الطلبات...');
            const orderStats = await api.get('/orders/stats');
            console.log('✅ تم جلب إحصائيات الطلبات');

            // 14. اختبار المسارات الجديدة للموبايل
            console.log('\n📱 اختبار مسارات الموبايل الجديدة...');

            // اختبار الصفحة الرئيسية للموبايل
            const mobileHome = await api.get('/mobile/home');
            console.log('✅ تم جلب الصفحة الرئيسية للموبايل');

            // اختبار السلة (إذا كان هناك توكن)
            if (authToken) {
                try {
                    const cart = await api.get('/mobile/cart');
                    console.log('✅ تم جلب السلة للموبايل');

                    // اختبار إضافة منتج للسلة (إذا كان هناك منتجات)
                    if (products.data.data.products.length > 0) {
                        const firstProduct = products.data.data.products[0];
                        await api.post('/mobile/cart/add', {
                            productId: firstProduct.id,
                            quantity: 1
                        });
                        console.log('✅ تم إضافة منتج للسلة');

                        // اختبار بيانات الدفع
                        try {
                            const checkoutData = await api.get('/mobile/checkout');
                            console.log('✅ تم جلب بيانات الدفع');
                        } catch (checkoutError) {
                            console.log('⚠️ خطأ في بيانات الدفع:', checkoutError.response?.data?.message);
                        }
                    }

                    // اختبار طلبات العميل
                    try {
                        const orders = await api.get('/mobile/orders');
                        console.log('✅ تم جلب طلبات العميل');
                    } catch (ordersError) {
                        console.log('⚠️ خطأ في جلب الطلبات:', ordersError.response?.data?.message);
                    }

                } catch (cartError) {
                    console.log('⚠️ خطأ في اختبار السلة:', cartError.response?.data?.message);
                }
            }

            // 15. اختبار تسجيل الخروج
            console.log('\n🚪 اختبار تسجيل الخروج...');
            await api.post('/customers/logout');
            console.log('✅ تم تسجيل الخروج بنجاح');

        } catch (error) {
            if (error.response?.status === 409) {
                console.log('⚠️ العميل موجود مسبقاً، سيتم اختبار تسجيل الدخول...');

                // اختبار تسجيل الدخول
                const login = await api.post('/customers/login', {
                    email: testCustomer.email,
                    password: testCustomer.password
                });
                authToken = login.data.data.token;
                console.log('✅ تم تسجيل الدخول بنجاح');
            } else {
                throw error;
            }
        }

        console.log('\n🎉 تم اختبار جميع مسارات API بنجاح!');
        console.log('\n📊 ملخص الاختبار:');
        console.log('✅ معلومات API');
        console.log('✅ فحص الصحة');
        console.log('✅ الفئات والمناطق والمدن');
        console.log('✅ المتاجر والمنتجات');
        console.log('✅ البحث العام');
        console.log('✅ تسجيل وإدارة العملاء');
        console.log('✅ الإشعارات');
        console.log('✅ إحصائيات الطلبات');

        console.log('\n🚀 API جاهز للاستخدام مع تطبيقات Flutter!');

    } catch (error) {
        console.error('\n❌ فشل في اختبار API:', error.message);
        if (error.response) {
            console.error('📄 تفاصيل الخطأ:', error.response.data);
        }
        process.exit(1);
    }
}

// تشغيل الاختبار
if (require.main === module) {
    testAPI();
}

module.exports = { testAPI };
