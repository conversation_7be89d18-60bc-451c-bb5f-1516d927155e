<!-- Hero Section -->
<div class="bg-primary text-white py-5 mb-4">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1>Welcome to E-Market</h1>
                <p class="lead">Discover amazing products from trusted stores</p>
                <a href="/products" class="btn btn-light btn-lg">Start Shopping</a>
            </div>
            <div class="col-md-4">
                <img src="/images/shopping.svg" alt="Shopping" class="img-fluid">
            </div>
        </div>
    </div>
</div>

<!-- Featured Products -->
<section class="mb-5">
    <h2 class="mb-4">Featured Products</h2>
    <div class="row">
        <% if (featuredProducts.length > 0) { %>
            <% featuredProducts.forEach(product => { %>
                <div class="col-md-3 mb-4">
                    <div class="card h-100">
                        <% if (product.images && product.images.length > 0) { %>
                            <img src="<%= product.images[0].path %>" class="card-img-top" alt="<%= product.name %>">
                        <% } else { %>
                            <img src="/images/placeholder.jpg" class="card-img-top" alt="No image">
                        <% } %>
                        <div class="card-body">
                            <h5 class="card-title"><%= product.name %></h5>
                            <p class="card-text text-truncate"><%= product.description %></p>
                            <div class="d-flex justify-content-between align-items-center">
                                <% if (product.discountPercentage > 0) { %>
                                    <div>
                                        <span class="text-decoration-line-through text-muted">$<%= product.price %></span>
                                        <span class="text-danger fw-bold">$<%= product.getFinalPrice() %></span>
                                    </div>
                                <% } else { %>
                                    <span class="fw-bold">$<%= product.price %></span>
                                <% } %>
                                <small class="text-muted">By <%= product.store.name %></small>
                            </div>
                        </div>
                        <div class="card-footer bg-white border-top-0">
                            <a href="/products/<%= product.id %>" class="btn btn-primary w-100">View Details</a>
                        </div>
                    </div>
                </div>
            <% }); %>
        <% } else { %>
            <div class="col">
                <p class="text-muted">No featured products available.</p>
            </div>
        <% } %>
    </div>
</section>

<!-- Popular Stores -->
<section class="mb-5">
    <h2 class="mb-4">Popular Stores</h2>
    <div class="row">
        <% if (activeStores.length > 0) { %>
            <% activeStores.forEach(store => { %>
                <div class="col-md-3 mb-4">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <% if (store.logo) { %>
                                <img src="<%= store.logo %>" class="rounded-circle mb-3" style="width: 100px; height: 100px;" alt="<%= store.name %>">
                            <% } else { %>
                                <div class="rounded-circle bg-light d-flex align-items-center justify-content-center mb-3 mx-auto" style="width: 100px; height: 100px;">
                                    <i class="fas fa-store fa-3x text-primary"></i>
                                </div>
                            <% } %>
                            <h5 class="card-title"><%= store.name %></h5>
                            <p class="card-text text-muted"><%= store.description || 'No description available' %></p>
                            <a href="/stores/<%= store.id %>" class="btn btn-outline-primary">Visit Store</a>
                        </div>
                    </div>
                </div>
            <% }); %>
        <% } else { %>
            <div class="col">
                <p class="text-muted">No active stores available.</p>
            </div>
        <% } %>
    </div>
</section>

<!-- Categories Section -->
<section class="mb-5">
    <h2 class="mb-4">Shop by Category</h2>
    <div class="row g-4">
        <div class="col-md-4">
            <a href="/products?category=electronics" class="text-decoration-none">
                <div class="card bg-light">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-laptop fa-3x text-primary mb-3"></i>
                        <h5 class="card-title mb-0">Electronics</h5>
                    </div>
                </div>
            </a>
        </div>
        <div class="col-md-4">
            <a href="/products?category=fashion" class="text-decoration-none">
                <div class="card bg-light">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-tshirt fa-3x text-primary mb-3"></i>
                        <h5 class="card-title mb-0">Fashion</h5>
                    </div>
                </div>
            </a>
        </div>
        <div class="col-md-4">
            <a href="/products?category=home" class="text-decoration-none">
                <div class="card bg-light">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-home fa-3x text-primary mb-3"></i>
                        <h5 class="card-title mb-0">Home & Living</h5>
                    </div>
                </div>
            </a>
        </div>
    </div>
</section> 