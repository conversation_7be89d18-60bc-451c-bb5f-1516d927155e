const { Cart, Product, Store, Customer, Category } = require('../models');
const { Op } = require('sequelize');

class CartService {
    
    /**
     * إضافة منتج للسلة
     */
    async addToCart(customerId, productId, quantity = 1, sessionId = null) {
        try {
            // التحقق من وجود المنتج
            const product = await Product.findOne({
                where: {
                    id: productId,
                    status: "active"
                },
                include: [
                    {
                        model: Store,
                        as: 'store',
                        where: { status: "active" }
                    }
                ]
            });

            if (!product) {
                throw new Error('المنتج غير موجود أو غير متاح');
            }

            // التحقق من الكمية المتاحة
            if (product.quantity < quantity) {
                throw new Error(`الكمية المتاحة: ${product.quantity}`);
            }

            // البحث عن عنصر موجود في السلة
            const whereClause = { productId: productId };
            if (customerId) {
                whereClause.customerId = customerId;
            } else if (sessionId) {
                whereClause.sessionId = sessionId;
            } else {
                throw new Error('معرف العميل أو الجلسة مطلوب');
            }

            let cartItem = await Cart.findOne({ where: whereClause });

            if (cartItem) {
                // تحديث الكمية
                const newQuantity = cartItem.quantity + parseInt(quantity);
                
                if (newQuantity > product.quantity) {
                    throw new Error(`الكمية الإجمالية تتجاوز المتاح. المتاح: ${product.quantity}, في السلة: ${cartItem.quantity}`);
                }

                await cartItem.updateQuantity(newQuantity);
            } else {
                // إنشاء عنصر جديد
                cartItem = await Cart.create({
                    customerId: customerId,
                    productId: productId,
                    quantity: parseInt(quantity),
                    price: product.getFinalPrice(), // استخدام السعر مع الخصم
                    totalPrice: product.getFinalPrice() * parseInt(quantity),
                    sessionId: sessionId
                });
            }

            return cartItem;

        } catch (error) {
            throw new Error(`خطأ في إضافة المنتج للسلة: ${error.message}`);
        }
    }

    /**
     * الحصول على محتويات السلة
     */
    async getCart(customerId, sessionId = null) {
        try {
            const whereClause = { isActive: true };
            
            if (customerId) {
                whereClause.customerId = customerId;
            } else if (sessionId) {
                whereClause.sessionId = sessionId;
            } else {
                throw new Error('معرف العميل أو الجلسة مطلوب');
            }

            const cartItems = await Cart.findAll({
                where: whereClause,
                include: [
                    {
                        model: Product,
                        as: 'product',
                        include: [
                            {
                                model: Store,
                                as: 'store',
                                attributes: ['name']
                            },
                            {
                                model: Category,
                                as: 'category',
                                attributes: ['name']
                            }
                        ]
                    }
                ],
                order: [['createdAt', 'DESC']]
            });

            // تجميع العناصر حسب المتجر
            const storeGroups = new Map();
            let totalPrice = 0;
            let totalItems = 0;

            for (const item of cartItems) {
                const product = item.product;
                
                // التحقق من توفر المنتج
                if (!product || !product.isActive || product.quantity < item.quantity) {
                    // حذف العناصر غير المتاحة
                    await item.destroy();
                    continue;
                }

                // تحديث السعر إذا تغير
                const currentPrice = product.getFinalPrice();
                if (parseFloat(item.price) !== currentPrice) {
                    await item.updatePrice(currentPrice);
                }

                const itemTotal = parseFloat(item.totalPrice);
                totalPrice += itemTotal;
                totalItems += item.quantity;

                // تجميع حسب المتجر
                const storeId = product.store.id;
                if (!storeGroups.has(storeId)) {
                    storeGroups.set(storeId, {
                        store: product.store,
                        items: [],
                        subtotal: 0,
                        itemsCount: 0
                    });
                }

                const storeGroup = storeGroups.get(storeId);
                storeGroup.items.push(item);
                storeGroup.subtotal += itemTotal;
                storeGroup.itemsCount += item.quantity;
            }

            return {
                cartItems,
                storeGroups: Array.from(storeGroups.values()),
                summary: {
                    totalItems,
                    totalPrice,
                    storesCount: storeGroups.size
                }
            };

        } catch (error) {
            throw new Error(`خطأ في جلب السلة: ${error.message}`);
        }
    }

    /**
     * تحديث كمية منتج في السلة
     */
    async updateCartItem(cartItemId, quantity, customerId = null) {
        try {
            const whereClause = { id: cartItemId };
            if (customerId) {
                whereClause.customerId = customerId;
            }

            const cartItem = await Cart.findOne({
                where: whereClause,
                include: [
                    {
                        model: Product,
                        as: 'product'
                    }
                ]
            });

            if (!cartItem) {
                throw new Error('العنصر غير موجود في السلة');
            }

            // التحقق من الكمية المتاحة
            if (quantity > cartItem.product.quantity) {
                throw new Error(`الكمية المتاحة: ${cartItem.product.quantity}`);
            }

            await cartItem.updateQuantity(quantity);
            return cartItem;

        } catch (error) {
            throw new Error(`خطأ في تحديث السلة: ${error.message}`);
        }
    }

    /**
     * حذف منتج من السلة
     */
    async removeFromCart(cartItemId, customerId = null) {
        try {
            const whereClause = { id: cartItemId };
            if (customerId) {
                whereClause.customerId = customerId;
            }

            const cartItem = await Cart.findOne({ where: whereClause });

            if (!cartItem) {
                throw new Error('العنصر غير موجود في السلة');
            }

            await cartItem.destroy();
            return true;

        } catch (error) {
            throw new Error(`خطأ في حذف المنتج: ${error.message}`);
        }
    }

    /**
     * مسح السلة بالكامل
     */
    async clearCart(customerId, sessionId = null) {
        try {
            const whereClause = {};
            
            if (customerId) {
                whereClause.customerId = customerId;
            } else if (sessionId) {
                whereClause.sessionId = sessionId;
            } else {
                throw new Error('معرف العميل أو الجلسة مطلوب');
            }

            await Cart.destroy({ where: whereClause });
            return true;

        } catch (error) {
            throw new Error(`خطأ في مسح السلة: ${error.message}`);
        }
    }

    /**
     * الحصول على عدد العناصر في السلة
     */
    async getCartCount(customerId, sessionId = null) {
        try {
            const whereClause = { isActive: true };
            
            if (customerId) {
                whereClause.customerId = customerId;
            } else if (sessionId) {
                whereClause.sessionId = sessionId;
            } else {
                return 0;
            }

            const count = await Cart.sum('quantity', { where: whereClause });
            return count || 0;

        } catch (error) {
            console.error('خطأ في جلب عدد عناصر السلة:', error);
            return 0;
        }
    }

    /**
     * تنظيف السلات المنتهية الصلاحية
     */
    async cleanExpiredCarts() {
        try {
            const expiredCount = await Cart.destroy({
                where: {
                    expiresAt: {
                        [Op.lt]: new Date()
                    }
                }
            });

            console.log(`تم حذف ${expiredCount} عنصر منتهي الصلاحية من السلة`);
            return expiredCount;

        } catch (error) {
            console.error('خطأ في تنظيف السلات المنتهية الصلاحية:', error);
            return 0;
        }
    }

    /**
     * نقل السلة من الجلسة إلى العميل عند تسجيل الدخول
     */
    async mergeSessionCartToCustomer(sessionId, customerId) {
        try {
            const sessionCartItems = await Cart.findAll({
                where: { sessionId: sessionId }
            });

            for (const sessionItem of sessionCartItems) {
                // البحث عن نفس المنتج في سلة العميل
                const customerCartItem = await Cart.findOne({
                    where: {
                        customerId: customerId,
                        productId: sessionItem.productId
                    }
                });

                if (customerCartItem) {
                    // دمج الكميات
                    const newQuantity = customerCartItem.quantity + sessionItem.quantity;
                    await customerCartItem.updateQuantity(newQuantity);
                    await sessionItem.destroy();
                } else {
                    // نقل العنصر للعميل
                    await sessionItem.update({
                        customerId: customerId,
                        sessionId: null
                    });
                }
            }

            return true;

        } catch (error) {
            console.error('خطأ في دمج السلة:', error);
            return false;
        }
    }
}

module.exports = new CartService();
