<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Edit Store</h1>
        <a href="/admin/stores" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Stores
        </a>
    </div>

    <div class="card">
        <div class="card-body">
            <form action="/admin/stores/<%= store.id %>" method="POST">
                <div class="mb-3">
                    <label for="name" class="form-label">Store Name</label>
                    <input type="text" class="form-control" id="name" name="name" value="<%= store.name %>" required>
                </div>
                
                <div class="mb-3">
                    <label for="userName" class="form-label">Username</label>
                    <input type="text" class="form-control" id="userName" name="userName" value="<%= store.userName %>" required>
                </div>

                <!-- Multi-select Categories -->
                <div class="mb-3">
                    <label for="categoryIds" class="form-label">Categories</label>
                    <select class="form-select" id="categoryIds" name="categoryIds" multiple required>
                        <% categories.forEach(category => { %>
                            <option value="<%= category.id %>"
                                <%= store.categories && store.categories.some(c => c.id === category.id) ? 'selected' : '' %>>
                                <%= category.name %>
                            </option>
                        <% }); %>
                    </select>
                    <small class="form-text text-muted">Hold Ctrl (Cmd on Mac) to select multiple</small>
                </div>

                <div class="mb-3">
                    <label for="areaId" class="form-label">Area</label>
                    <select class="form-select" id="areaId" name="areaId" required>
                        <option value="">Select Area</option>
                        <% areas.forEach(area => { %>
                            <option value="<%= area.id %>" <%= store.areaId === area.id ? 'selected' : '' %>>
                                <%= area.name %>
                            </option>
                        <% }); %>
                    </select>
                </div>
                
                <div class="mb-3">
                    <label for="address" class="form-label">Address</label>
                    <textarea class="form-control" id="address" name="address" rows="3"><%= store.address || '' %></textarea>
                </div>
                
                <div class="mb-3">
                    <label for="phoneNumber" class="form-label">Phone Number</label>
                    <input type="text" class="form-control" id="phoneNumber" name="phoneNumber" value="<%= store.phoneNumber || '' %>">
                </div>
                
                <div class="mb-3">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status" required>
                        <option value="active" <%= store.status === 'active' ? 'selected' : '' %>>Active</option>
                        <option value="inactive" <%= store.status === 'inactive' ? 'selected' : '' %>>Inactive</option>
                        <option value="pending" <%= store.status === 'pending' ? 'selected' : '' %>>Pending</option>
                    </select>
                </div>
                
                <div class="mb-3">
                    <label for="notes" class="form-label">Notes</label>
                    <textarea class="form-control" id="notes" name="notes" rows="3"><%= store.notes || '' %></textarea>
                </div>
                
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">Update Store</button>
                </div>
            </form>
        </div>
    </div>
</div>
