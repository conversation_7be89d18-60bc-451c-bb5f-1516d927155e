const { Store, Product, Category, Area, Country } = require('../../models');
const { Op } = require('sequelize');

class StoreApiController {

    /**
     * الحصول على جميع المتاجر
     * GET /api/stores
     */
    async getAllStores(req, res) {
        try {
            const { 
                page = 1, 
                limit = 10, 
                search = '', 
                areaId = null,
                categoryId = null,
                isActive = true 
            } = req.query;

            const offset = (page - 1) * limit;
            const whereClause = {
                isActive: isActive === 'true'
            };

            // البحث بالاسم
            if (search) {
                whereClause.name = {
                    [Op.like]: `%${search}%`
                };
            }

            // فلتر بالمنطقة
            if (areaId) {
                whereClause.areaId = areaId;
            }

            // إعداد include للفئات إذا كان هناك فلتر
            const includeArray = [
                {
                    model: Area,
                    include: [Country]
                }
            ];

            // إضافة فلتر الفئة إذا كان موجود
            if (categoryId) {
                includeArray.push({
                    model: Product,
                    where: { categoryId: categoryId },
                    required: true,
                    include: [Category]
                });
            } else {
                includeArray.push({
                    model: Product,
                    include: [Category],
                    required: false
                });
            }

            const { count, rows: stores } = await Store.findAndCountAll({
                where: whereClause,
                include: includeArray,
                limit: parseInt(limit),
                offset: parseInt(offset),
                order: [['createdAt', 'DESC']],
                distinct: true
            });

            // تنسيق البيانات للموبايل
            const formattedStores = stores.map(store => ({
                id: store.id,
                name: store.name,
                description: store.description,
                phone: store.phone,
                address: store.address,
                image: store.image ? `/uploads/${store.image}` : null,
                isActive: store.isActive,
                rating: store.rating || 0,
                area: store.Area ? {
                    id: store.Area.id,
                    name: store.Area.name,
                    country: store.Area.Country ? store.Area.Country.name : null
                } : null,
                productsCount: store.Products ? store.Products.length : 0,
                categories: store.Products ? 
                    [...new Set(store.Products.map(p => p.Category?.name).filter(Boolean))] : [],
                createdAt: store.createdAt
            }));

            res.json({
                success: true,
                message: 'تم جلب المتاجر بنجاح',
                data: {
                    stores: formattedStores,
                    pagination: {
                        currentPage: parseInt(page),
                        totalPages: Math.ceil(count / limit),
                        totalItems: count,
                        itemsPerPage: parseInt(limit),
                        hasNextPage: page * limit < count,
                        hasPrevPage: page > 1
                    }
                }
            });

        } catch (error) {
            console.error('خطأ في جلب المتاجر:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    /**
     * الحصول على تفاصيل متجر واحد
     * GET /api/stores/:id
     */
    async getStoreById(req, res) {
        try {
            const { id } = req.params;

            const store = await Store.findByPk(id, {
                include: [
                    {
                        model: Area,
                        include: [Country]
                    },
                    {
                        model: Product,
                        include: [Category],
                        where: { isActive: true },
                        required: false
                    }
                ]
            });

            if (!store) {
                return res.status(404).json({
                    success: false,
                    message: 'المتجر غير موجود',
                    data: null
                });
            }

            // تنسيق البيانات
            const formattedStore = {
                id: store.id,
                name: store.name,
                description: store.description,
                phone: store.phone,
                address: store.address,
                image: store.image ? `/uploads/${store.image}` : null,
                isActive: store.isActive,
                rating: store.rating || 0,
                area: store.Area ? {
                    id: store.Area.id,
                    name: store.Area.name,
                    country: store.Area.Country ? store.Area.Country.name : null
                } : null,
                products: store.Products ? store.Products.map(product => ({
                    id: product.id,
                    name: product.name,
                    description: product.description,
                    price: parseFloat(product.price),
                    image: product.image ? `/uploads/${product.image}` : null,
                    isActive: product.isActive,
                    category: product.Category ? {
                        id: product.Category.id,
                        name: product.Category.name
                    } : null,
                    createdAt: product.createdAt
                })) : [],
                createdAt: store.createdAt
            };

            res.json({
                success: true,
                message: 'تم جلب بيانات المتجر بنجاح',
                data: { store: formattedStore }
            });

        } catch (error) {
            console.error('خطأ في جلب بيانات المتجر:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    /**
     * البحث في المتاجر
     * GET /api/stores/search
     */
    async searchStores(req, res) {
        try {
            const { 
                q = '', 
                areaId = null,
                categoryId = null,
                minRating = 0,
                page = 1,
                limit = 10
            } = req.query;

            const offset = (page - 1) * limit;
            const whereClause = {
                isActive: true,
                [Op.or]: [
                    { name: { [Op.like]: `%${q}%` } },
                    { description: { [Op.like]: `%${q}%` } }
                ]
            };

            // فلتر بالمنطقة
            if (areaId) {
                whereClause.areaId = areaId;
            }

            // فلتر بالتقييم
            if (minRating > 0) {
                whereClause.rating = { [Op.gte]: minRating };
            }

            const includeArray = [
                {
                    model: Area,
                    include: [Country]
                }
            ];

            // فلتر بالفئة
            if (categoryId) {
                includeArray.push({
                    model: Product,
                    where: { categoryId: categoryId, isActive: true },
                    required: true,
                    include: [Category]
                });
            } else {
                includeArray.push({
                    model: Product,
                    where: { isActive: true },
                    include: [Category],
                    required: false
                });
            }

            const { count, rows: stores } = await Store.findAndCountAll({
                where: whereClause,
                include: includeArray,
                limit: parseInt(limit),
                offset: parseInt(offset),
                order: [['rating', 'DESC'], ['createdAt', 'DESC']],
                distinct: true
            });

            // تنسيق البيانات
            const formattedStores = stores.map(store => ({
                id: store.id,
                name: store.name,
                description: store.description,
                phone: store.phone,
                address: store.address,
                image: store.image ? `/uploads/${store.image}` : null,
                rating: store.rating || 0,
                area: store.Area ? {
                    id: store.Area.id,
                    name: store.Area.name,
                    country: store.Area.Country ? store.Area.Country.name : null
                } : null,
                productsCount: store.Products ? store.Products.length : 0,
                categories: store.Products ? 
                    [...new Set(store.Products.map(p => p.Category?.name).filter(Boolean))] : []
            }));

            res.json({
                success: true,
                message: 'تم البحث بنجاح',
                data: {
                    stores: formattedStores,
                    searchQuery: q,
                    pagination: {
                        currentPage: parseInt(page),
                        totalPages: Math.ceil(count / limit),
                        totalItems: count,
                        itemsPerPage: parseInt(limit)
                    }
                }
            });

        } catch (error) {
            console.error('خطأ في البحث:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    /**
     * الحصول على المتاجر المميزة
     * GET /api/stores/featured
     */
    async getFeaturedStores(req, res) {
        try {
            const { limit = 5 } = req.query;

            const stores = await Store.findAll({
                where: {
                    isActive: true,
                    rating: { [Op.gte]: 4.0 } // المتاجر بتقييم 4 نجوم أو أكثر
                },
                include: [
                    {
                        model: Area,
                        include: [Country]
                    },
                    {
                        model: Product,
                        where: { isActive: true },
                        include: [Category],
                        required: false
                    }
                ],
                order: [['rating', 'DESC'], ['createdAt', 'DESC']],
                limit: parseInt(limit)
            });

            const formattedStores = stores.map(store => ({
                id: store.id,
                name: store.name,
                description: store.description,
                image: store.image ? `/uploads/${store.image}` : null,
                rating: store.rating || 0,
                area: store.Area ? store.Area.name : null,
                productsCount: store.Products ? store.Products.length : 0
            }));

            res.json({
                success: true,
                message: 'تم جلب المتاجر المميزة بنجاح',
                data: { stores: formattedStores }
            });

        } catch (error) {
            console.error('خطأ في جلب المتاجر المميزة:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }
}

module.exports = new StoreApiController();
