/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

main {
    flex: 1;
}

/* Loading Indicator */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-overlay.show {
    display: flex;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error Page */
.error-container {
    max-width: 800px;
    margin: 0 auto;
}

.error-card {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    overflow: hidden;
}

.error-header {
    background-color: #dc3545;
    color: white;
    padding: 15px;
}

.error-body {
    padding: 20px;
}

.error-footer {
    background-color: #f8f9fa;
    padding: 10px;
    border-top: 1px solid #e9ecef;
}

/* Tables */
.table-responsive {
    overflow-x: auto;
}

.table th {
    background-color: #f8f9fa;
}

.action-buttons .btn {
    margin-right: 5px;
}

/* Cards */
.card {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    margin-bottom: 20px;
}

/* Form Styling */
.form-control:focus {
    border-color: #4a6fdc;
    box-shadow: 0 0 0 0.25rem rgba(74, 111, 220, 0.25);
}

/* Alerts */
.alert {
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Pagination */
.pagination .page-item.active .page-link {
    background-color: #4a6fdc;
    border-color: #4a6fdc;
}

.pagination .page-link {
    color: #4a6fdc;
}

/* Navbar styling */
.navbar {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Footer */
footer {
    margin-top: auto;
    box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.05);
}

/* Responsive fixes */
@media (max-width: 768px) {
    .action-buttons {
        display: flex;
        flex-direction: column;
    }
    
    .action-buttons .btn {
        margin-bottom: 5px;
        width: 100%;
    }
} 