const { Admin } = require('../models');

class AdminAuthController {
    // Show login form
    async showLogin(req, res) {
        res.render('admin/auth/login');
    }

    // Handle login
    async login(req, res) {
        try {
            const { username, password } = req.body;
            const admin = await Admin.findOne({ where: { username } });

            if (!admin) {
                return res.render('admin/auth/login', {
                    error: 'Invalid username or password'
                });
            }

           const isValidPassword = await admin.validatePassword(password);
            if (!isValidPassword) {
                return res.render('admin/auth/login', {
                    error: 'Invalid username or password'
                });
            }

            if (admin.status !== 'active') {
                return res.render('admin/auth/login', {
                    error: 'Your account has been deactivated'
                });
            }

            // Update last login
            admin.lastLogin = new Date();
            await admin.save();

            // Set session
            req.session.adminId = admin.id;
            req.session.adminRole = admin.role;
            req.session.adminUsername = admin.username;
            
            res.redirect('/admin/dashboard');
        } catch (error) {
            res.render('admin/auth/login', {
                error: 'An error occurred during login'
            });
        }
    }

    // Handle logout
    async logout(req, res) {
        req.session.destroy();
        res.redirect('/admin/auth/login');
    }

    // Change Password
    async showChangePassword(req, res) {
        res.render('admin/auth/change-password');
    }

    async changePassword(req, res) {
        try {
            const { currentPassword, newPassword, confirmPassword } = req.body;
            const admin = await Admin.findByPk(req.session.adminId);

            if (!admin) {
                return res.render('admin/auth/change-password', {
                    error: 'Admin not found'
                });
            }

            const isValidPassword = await admin.validatePassword(currentPassword);
            if (!isValidPassword) {
                return res.render('admin/auth/change-password', {
                    error: 'Current password is incorrect'
                });
            }

            if (newPassword !== confirmPassword) {
                return res.render('admin/auth/change-password', {
                    error: 'New passwords do not match'
                });
            }

            admin.password = newPassword;
            await admin.save();

            res.render('admin/auth/change-password', {
                success: 'Password changed successfully'
            });
        } catch (error) {
            res.render('admin/auth/change-password', {
                error: 'An error occurred while changing password'
            });
        }
    }
}

module.exports = new AdminAuthController(); 