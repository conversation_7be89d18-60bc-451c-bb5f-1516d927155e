const jwt = require('jsonwebtoken');

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '24h';
const JWT_REFRESH_EXPIRES_IN = process.env.JWT_REFRESH_EXPIRES_IN || '7d';

/**
 * Generate JWT token
 * @param {Object} payload - User data to encode in token
 * @param {string} expiresIn - Token expiration time
 * @returns {string} JWT token
 */
const generateToken = (payload, expiresIn = JWT_EXPIRES_IN) => {
    return jwt.sign(payload, JWT_SECRET, { expiresIn });
};

/**
 * Generate refresh token
 * @param {Object} payload - User data to encode in token
 * @returns {string} Refresh JWT token
 */
const generateRefreshToken = (payload) => {
    return jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_REFRESH_EXPIRES_IN });
};

/**
 * Verify JWT token
 * @param {string} token - JWT token to verify
 * @returns {Object} Decoded token payload
 */
const verifyToken = (token) => {
    try {
        return jwt.verify(token, JWT_SECRET);
    } catch (error) {
        throw new Error('Invalid or expired token');
    }
};

/**
 * Generate tokens for user authentication
 * @param {Object} user - User object
 * @param {string} userType - Type of user (customer, store, admin)
 * @returns {Object} Object containing access and refresh tokens
 */
const generateAuthTokens = (user, userType) => {
    const payload = {
        id: user.id,
        userType: userType,
        name: user.name || user.userName,
        email: user.email,
        status: user.status
    };

    const accessToken = generateToken(payload);
    const refreshToken = generateRefreshToken({ id: user.id, userType });

    return {
        accessToken,
        refreshToken,
        expiresIn: JWT_EXPIRES_IN
    };
};

/**
 * Extract token from request headers or cookies
 * @param {Object} req - Express request object
 * @returns {string|null} JWT token or null
 */
const extractToken = (req) => {
    // Check Authorization header
    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer ')) {
        return req.headers.authorization.substring(7);
    }
    
    // Check cookies
    if (req.cookies && req.cookies.token) {
        return req.cookies.token;
    }
    
    // Check query parameter (for development/testing)
    if (req.query && req.query.token) {
        return req.query.token;
    }
    
    return null;
};

/**
 * Decode token without verification (for expired token handling)
 * @param {string} token - JWT token
 * @returns {Object|null} Decoded payload or null
 */
const decodeToken = (token) => {
    try {
        return jwt.decode(token);
    } catch (error) {
        return null;
    }
};

module.exports = {
    generateToken,
    generateRefreshToken,
    verifyToken,
    generateAuthTokens,
    extractToken,
    decodeToken,
    JWT_SECRET,
    JWT_EXPIRES_IN
};
