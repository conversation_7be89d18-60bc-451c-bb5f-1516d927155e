<div class="container mt-5">
    <div class="d-flex justify-content-between align-items-center mb-5 flex-wrap">
        <h2 class="fw-bold text-primary mb-2">Welcome <%= customerName %></h2>          
    </div>

    <% if (categories.length === 0) { %>
        <div class="alert alert-info text-center p-4 rounded shadow-sm">
            No categories available in this area.
        </div>
    <% } else { 
        const bgColors = ['bg-primary', 'bg-success', 'bg-warning', 'bg-danger', 'bg-info', 'bg-dark'];
    %>
        <div class="row">
            <% categories.forEach((category, index) => {
                const colorClass = bgColors[category.id % bgColors.length]; // لون مخصص حسب ID
                const icon = category.icon || 'box'; // افتراضي: أيقونة صندوق
            %>
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card shadow-sm h-100 border-0 transition-hover">
                        <div class="card-body text-center d-flex flex-column justify-content-between">
                            <div>
                                <div class="mb-3">
                                    <i class="bi bi-<%= icon %> <%= colorClass %> text-white p-3 rounded-circle fs-3"></i>
                                </div>
                                <h5 class="card-title text-dark fw-semibold"><%= category.name %></h5>
                                <p class="card-text text-muted mb-3"><%= category.stores.length %> Store(s)</p>
                            </div>
                            <a href="/customers/categories/<%= category.id %>" class="btn btn-outline-primary btn-sm">
                                View Stores
                            </a>
                        </div>
                    </div>
                </div>
            <% }) %>
        </div>
    <% } %>
</div>

<!-- أيقونات Bootstrap -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">

<style>
.transition-hover {
    transition: transform 0.2s, box-shadow 0.2s;
}
.transition-hover:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}
</style>
