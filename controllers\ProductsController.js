const BaseController = require('./BaseController');
const { Product, Store, Image } = require('../models');
const { Op } = require('sequelize');
const checkDatabaseHealth = require('../utils/dbHealthCheck');
const path = require('path');

class ProductsController extends BaseController {
    constructor() {
        super(Product, 'products');
    }

    async index(req, res) {
        try {
            const isHealthy = await checkDatabaseHealth();
            if (!isHealthy) {
                throw new Error('Database schema validation failed');
            }

            const products = await Product.sequelize.transaction(async (t) => {
                return await Product.findAll({
                    include: [
                        {
                            model: Store,
                            as: 'store',
                            required: false,
                            attributes: ['id', 'name', 'status'],
                            where: { status: 'active' }
                        },
                        {
                            model: Image,
                            as: 'images',
                            required: false,
                            attributes: ['id', 'path']
                        }
                    ],
                    where: { status: 'active' },
                    attributes: [
                        'id', 'name', 'description', 'price',
                        'quantity', 'category', 'discountPercentage',
                        'discountStartDate', 'discountEndDate', 'status'
                    ],
                    order: [['createdAt', 'DESC']],
                    transaction: t
                });
            });

            if (!products.length) {
                return res.render('products/index', {
                    products: [],
                    message: 'No products available at the moment.'
                });
            }

            res.render('products/index', { products });
        } catch (error) {
            console.error('Error in ProductsController.index:', error);
            res.status(500).render('error', {
                error: {
                    message: 'An error occurred while fetching products',
                    details: process.env.NODE_ENV === 'development' ? error.message : null
                }
            });
        }
    }

    async create(req, res) {
        try {
            const stores = await Store.findAll({
                where: { status: 'active' },
                attributes: ['id', 'name']
            });

            if (!stores.length) {
                return res.status(404).render('error', {
                    error: { message: 'No active stores found' }
                });
            }

            res.render('products/create', { stores });
        } catch (error) {
            console.error('Error in ProductsController.create:', error);
            res.status(500).render('error', {
                error: {
                    message: 'An error occurred while loading the create form',
                    details: process.env.NODE_ENV === 'development' ? error.message : null
                }
            });
        }
    }

    async store(req, res) {
        try {
            const { name, price, storeId } = req.body;
            if (!name || !price || !storeId) {
                return res.status(400).render('error', {
                    error: { message: 'Missing required fields' }
                });
            }

            // إنشاء المنتج
            const product = await Product.create({
                ...req.body,
                status: 'active',
                quantity: req.body.quantity || 0
            });

            // رفع الصور إن وجدت
            if (req.files && req.files.images) {
                const images = Array.isArray(req.files.images) ? req.files.images : [req.files.images];
                for (const image of images) {
                    try {
                        const fileName = `${Date.now()}-${image.name}`;
                        // مسار الحفظ داخل مجلد public/uploads/products
                        const filePath = path.join(__dirname, '..', 'public', 'uploads', 'products', fileName);
                        await image.mv(filePath);
                        await Image.create({
                            productId: product.id,
                            path: `/uploads/products/${fileName}`
                        });
                    } catch (uploadError) {
                        console.error('Error uploading image:', uploadError);
                        // الاستمرار في رفع بقية الصور حتى لو فشل واحد منهم
                    }
                }
            }

            req.flash('success', 'Product created successfully');
            res.redirect('/products');
        } catch (error) {
            console.error('Error in ProductsController.store:', error);
            res.status(500).render('error', {
                error: {
                    message: 'An error occurred while creating the product',
                    details: process.env.NODE_ENV === 'development' ? error.message : null
                }
            });
        }
    }

    async edit(req, res) {
        try {
            const product = await Product.findByPk(req.params.id, {
                include: [{ model: Image, as: 'images' }]
            });
            if (!product) {
                return res.status(404).render('error', { error: { message: 'Product not found' } });
            }
            res.render('products/edit', { product });
        } catch (error) {
            console.error('Error in ProductsController.edit:', error);
            res.status(500).render('error', { error: { message: 'Error loading product edit form' } });
        }
    }
    
    async update(req, res) {
        try {
            const product = await Product.findByPk(req.params.id);
            if (!product) {
                return res.status(404).render('error', { error: { message: 'Product not found' } });
            }
            await product.update(req.body);
            req.flash('success', 'Product updated successfully');
            res.redirect('/products');
        } catch (error) {
            console.error('Error in ProductsController.update:', error);
            res.status(500).render('error', { error: { message: 'Error updating product' } });
        }
    }
    
    async delete(req, res) {
        try {
            const product = await Product.findByPk(req.params.id);
            if (!product) {
                return res.status(404).render('error', { error: { message: 'Product not found' } });
            }
            await product.destroy();
            req.flash('success', 'Product deleted successfully');
            res.redirect('/products');
        } catch (error) {
            console.error('Error in ProductsController.delete:', error);
            res.status(500).render('error', { error: { message: 'Error deleting product' } });
        }
    }
    

}

module.exports = new ProductsController();
