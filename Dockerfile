# استخدام Node.js الرسمي كصورة أساسية
FROM node:18-alpine

# تعيين مجلد العمل
WORKDIR /app

# نسخ ملفات package.json و package-lock.json
COPY package*.json ./

# تثبيت التبعيات
RUN npm ci --only=production && npm cache clean --force

# إنشاء مستخدم غير جذر لتشغيل التطبيق
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001

# نسخ ملفات التطبيق
COPY --chown=nextjs:nodejs . .

# إنشاء المجلدات المطلوبة
RUN mkdir -p uploads logs && \
    chown -R nextjs:nodejs uploads logs

# تعيين المتغيرات البيئية
ENV NODE_ENV=production
ENV PORT=3000

# كشف المنفذ
EXPOSE 3000

# التبديل إلى المستخدم غير الجذر
USER nextjs

# تشغيل التطبيق
CMD ["npm", "start"]

# إضافة تسميات للصورة
LABEL maintainer="Smart Store System Team"
LABEL version="1.0.0"
LABEL description="نظام إدارة المتاجر الذكي"
