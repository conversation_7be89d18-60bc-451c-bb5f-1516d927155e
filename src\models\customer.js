'use strict';
const { Model } = require('sequelize');
const bcrypt = require('bcryptjs');

module.exports = (sequelize, DataTypes) => {
  class Customer extends Model {
    static associate(models) {
      Customer.hasMany(models.Order, {
        foreignKey: 'customerId',
        as: 'orders'
      });

      Customer.belongsToMany(models.Area, {
        through: models.CustomerArea,
        foreignKey: 'customer_id',
        otherKey: 'area_id',
        as: 'areas'
      });

      // العلاقة مع السلة
      Customer.hasMany(models.Cart, {
        foreignKey: 'customerId',
        as: 'cartItems',
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE'
      });
    }

    async validatePassword(password) {
      return bcrypt.compare(password, this.password);
    }
  }

  Customer.init({
    name: {
      type: DataTypes.STRING,
      allowNull: false
    },
    barcode: DataTypes.STRING,
    password: {
      type: DataTypes.STRING,
      allowNull: false,
      set(value) {
        // Hash password before saving
        const hashedPassword = bcrypt.hashSync(value, 10);
        this.setDataValue('password', hashedPassword);
      }
    },
    phoneNumber: DataTypes.STRING,
    discountRate: DataTypes.DECIMAL(10, 2),
    notes: DataTypes.TEXT,
    status: {
      type: DataTypes.ENUM('active', 'inactive', 'pending'),
      defaultValue: 'pending',
      allowNull: false
    }
  }, {
    sequelize,
    modelName: 'Customer',
  });

  return Customer;
};