const BaseController = require('./BaseController');
const { Customer, Area, Order, Country, Category, Store, CustomerArea, Product , Cart ,Image, OrderDetail} = require('../models');

class CustomersController extends BaseController {
    constructor() {
        super(Customer, 'customers');
    }

    async showall(req, res) {
      try {
          const customers = await Customer.findAll({
              include: [
                  { model: Area, as: 'areas', through: 'CustomerAreas' },
                  { model: Order, as: 'orders' }
              ]
          });
          res.render('admin/customers/index', { customers });
      } catch (error) {
          res.status(500).render('error', { error });
      }
  }

    async show(req, res) {
        const customerId = req.params.id;

        try {
            const customer = await Customer.findByPk(customerId, {
                include: [{
                  model: Area,
                  as: 'areas',
                  include: [{ model: Country, as: 'country' }]
                }]
              });

          if (!customer) {
            return res.status(404).render('error', { error: { message: 'الزبون غير موجود' } });
          }

          res.render('stores/customers', { customer });
        } catch (error) {
          console.error("Error fetching customer:", error);
          res.status(500).render('error', { error: { message: 'حدث خطأ أثناء جلب بيانات الزبون' } });
        }
      }


      async index(req, res) {
        try {
          const customerId = req.user.id;
          const customer = await Customer.findByPk(customerId, {
            include: {
                model: Area,
                as: 'areas'
            }
            });
            if (!customer) {
                return res.redirect('/customers/login');
            }
          const categories = await Category.findAll({
            include: [{
              model: Store,
              as: 'stores',
              required: false
            }]
          });

          res.render('customers/index', {
            customerName: customer.name,
            categories
          });

        } catch (error) {
          console.error('Error loading customer home:', error);
          res.status(500).send('Internal Server Error');
        }
      }

      async storesByCategory(req, res) {
        const categoryId = req.params.id;

        try {
          const category = await Category.findByPk(categoryId);

          if (!category) {
            return res.status(404).render('error', { error: { message: 'Category not found' } });
          }

          const stores = await Store.findAll({
            where: {
              status: 'active'
            },
            include: [
              {
                model: Category,
                as: 'categories',
                where: { id: categoryId }
              }
            ],
            Store: [['createdAt', 'DESC']]
          });

          res.render('customers/stores', {
            stores,
            category });

        } catch (error) {
          console.error('Error loading stores by category:', error);
          res.status(500).render('error', { error: { message: 'Failed to load stores' } });
        }
      }

      // عرض منتجات متجر معين
    async showStoreProducts(req, res) {
        const storeId = parseInt(req.params.id);
        try {
        const store = await Store.findByPk(storeId, {
            include: [{
                model: Product,
                as: 'products',
                include: [{
                    model: Image,
                    as: 'images'
                }]
            }] // تأكد من أن العلاقة مع المنتجات معرفة
        });

        if (!store) {
            return res.status(404).send("Store not found");
        }

        res.render('customers/products', {
            store,
            products: store.products
        });
        } catch (err) {
        console.error('Error loading store products:', err);
        res.status(500).send("Error loading products");
        }
    }

    async productDetails(req, res) {
        const id = req.params.id;

        try {
          const product = await Product.findOne({
            where: { id: id },
            include: [{ model: Store, as: 'store' }, { model: Category, as: 'category' }, { model: Image, as: 'images' } ]
          });

          if (!product) return res.status(404).send('Product not found');

          res.render('customers/product-details', { product });
        } catch (error) {
          console.error('Product details error:', error);
          res.status(500).send('Error loading product details');
        }
      };

       async addToCart(req, res) {
        const { productId, quantity } = req.body;
        try {
          // تأكد من وجود cart في cookies أو أنشئه
          let cart = [];
          if (req.cookies.cart) {
            try {
              cart = JSON.parse(req.cookies.cart);
            } catch (e) {
              cart = [];
            }
          }

          // تحقق إذا المنتج موجود بالسلة مسبقاً
          const existingItemIndex = cart.findIndex(item => item.productId == productId);
          if (existingItemIndex > -1) {
            // تحديث الكمية
            cart[existingItemIndex].quantity += parseInt(quantity);
          } else {
            cart.push({ productId: parseInt(productId), quantity: parseInt(quantity) });
          }

          // حفظ السلة في cookies
          res.cookie('cart', JSON.stringify(cart), {
            maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
            httpOnly: false // يمكن الوصول إليها من JavaScript
          });

          // إعادة تحميل صفحة تفاصيل المنتج مع رسالة نجاح
          const product = await Product.findByPk(productId, {
            include: [
                {
                  model: Store,
                  as: 'store' // <-- مهم جداً
                },
                {
                  model: Image,
                  as: 'images'
                }
              ]
           });

          res.render('customers/product-details', {
            product,
            successMessage: 'Product added to cart successfully!'
          });
        } catch (error) {
          console.error('Add to cart error:', error);
          res.status(500).send('Error adding product to cart');
        }
      };


       async showCart(req, res) {
        try {
          let cart = [];
          if (req.cookies.cart) {
            try {
              cart = JSON.parse(req.cookies.cart);
            } catch (e) {
              cart = [];
            }
          }

          // جلب تفاصيل المنتجات في السلة من قاعدة البيانات
          const productIds = cart.map(item => item.productId);
          const products = await Product.findAll({
            where: { id: productIds },
            include: [ {
                model: Store,
                as: 'store' // <-- مهم جداً
              },
              {
                model: Category,
                as: 'category'
              },
              {
                model: Image,
                as: 'images'
              }
            ]
          });

          // دمج بيانات المنتجات مع الكمية من الجلسة
          const cartItems = products.map(product => {
            const cartItem = cart.find(item => item.productId === product.id);
            return {
              product,
              quantity: cartItem ? cartItem.quantity : 0
            };
          });

          // حساب إجمالي السعر
          const totalPrice = cartItems.reduce((sum, item) => sum + (item.product.price * item.quantity), 0);

          res.render('customers/cart/index', { cartItems, totalPrice });
        } catch (error) {
          console.error('Error loading cart:', error);
          res.status(500).send('Error loading cart');
        }
      };

      async removeFromCart(req, res) {
        try {
            const { productId } = req.body;
            let cart = [];
            if (req.cookies.cart) {
              try {
                cart = JSON.parse(req.cookies.cart);
              } catch (e) {
                cart = [];
              }
            }

            cart = cart.filter(item => item.productId != productId);

            // حفظ السلة المحدثة في cookies
            res.cookie('cart', JSON.stringify(cart), {
              maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
              httpOnly: false
            });

            res.redirect('/customers/cart');
        } catch (error) {
            console.error('Error removing from cart:', error);
            res.status(500).send('Error removing from cart');
        }
      };

      // عرض صفحة الدفع
      async showCheckout(req, res) {
        try {
            const customerId = req.user.id;

            let cart = [];
            if (req.cookies.cart) {
              try {
                cart = JSON.parse(req.cookies.cart);
              } catch (e) {
                cart = [];
              }
            }
            if (cart.length === 0) {
                req.flash('error', 'سلة التسوق فارغة');
                return res.redirect('/customers/cart');
            }

            // جلب تفاصيل المنتجات من قاعدة البيانات
            const cartItems = [];
            let totalPrice = 0;
            const storeGroups = new Map();

            for (const item of cart) {
                const product = await Product.findByPk(item.productId, {
                    include: [
                        { model: Store, as: 'store' },
                        { model: Image, as: 'images' }
                    ]
                });

                if (product && product.quantity >= item.quantity) {
                    const itemTotal = product.price * item.quantity;
                    totalPrice += itemTotal;

                    const cartItem = {
                        product,
                        quantity: item.quantity,
                        total: itemTotal
                    };

                    cartItems.push(cartItem);

                    // تجميع المنتجات حسب المتجر
                    const storeId = product.store.id;
                    if (!storeGroups.has(storeId)) {
                        storeGroups.set(storeId, {
                            store: product.store,
                            items: [],
                            subtotal: 0
                        });
                    }

                    const storeGroup = storeGroups.get(storeId);
                    storeGroup.items.push(cartItem);
                    storeGroup.subtotal += itemTotal;
                }
            }

            // جلب بيانات العميل
            const customer = await Customer.findByPk(customerId, {
                include: [{ model: Area, as: 'areas' }]
            });

            res.render('customers/checkout/index', {
                customer,
                cartItems,
                storeGroups: Array.from(storeGroups.values()),
                totalPrice
            });

        } catch (error) {
            console.error('Error showing checkout:', error);
            res.status(500).render('error', {
                error: { message: 'حدث خطأ في عرض صفحة الدفع' }
            });
        }
      }

      // معالجة إنشاء الطلب
      async processCheckout(req, res) {
        try {
            const customerId = req.user.id;

            let cart = [];
            if (req.cookies.cart) {
              try {
                cart = JSON.parse(req.cookies.cart);
              } catch (e) {
                cart = [];
              }
            }
            if (cart.length === 0) {
                req.flash('error', 'سلة التسوق فارغة');
                return res.redirect('/customers/cart');
            }

            const { deliveryAddress, notes } = req.body;

            // تجميع المنتجات حسب المتجر
            const storeGroups = new Map();
            let totalAmount = 0;

            for (const item of cart) {
                const product = await Product.findByPk(item.productId, {
                    include: [{ model: Store, as: 'store' }]
                });

                if (!product || product.quantity < item.quantity) {
                    req.flash('error', `المنتج ${product ? product.name : 'غير موجود'} غير متوفر بالكمية المطلوبة`);
                    return res.redirect('/customers/cart');
                }

                const storeId = product.store.id;
                const itemTotal = product.price * item.quantity;
                totalAmount += itemTotal;

                if (!storeGroups.has(storeId)) {
                    storeGroups.set(storeId, {
                        store: product.store,
                        items: [],
                        subtotal: 0
                    });
                }

                const storeGroup = storeGroups.get(storeId);
                storeGroup.items.push({
                    product,
                    quantity: item.quantity,
                    price: product.price,
                    total: itemTotal
                });
                storeGroup.subtotal += itemTotal;
            }

            // إنشاء طلب منفصل لكل متجر
            const createdOrders = [];

            for (const [storeId, storeGroup] of storeGroups) {
                const orderData = {
                    customerId: customerId,
                    storeId: storeId,
                    totalAmount: storeGroup.subtotal,
                    status: 'pending',
                    deliveryAddress: deliveryAddress || 'العنوان الافتراضي',
                    notes: notes || null
                };

                // إنشاء الطلب
                const order = await Order.create(orderData);

                // إنشاء تفاصيل الطلب
                const orderDetails = storeGroup.items.map(item => ({
                    orderId: order.id,
                    productId: item.product.id,
                    quantity: item.quantity,
                    totalPrice: item.total
                }));

                await OrderDetail.bulkCreate(orderDetails);

                // تحديث كمية المنتجات
                for (const item of storeGroup.items) {
                    await Product.update(
                        { quantity: item.product.quantity - item.quantity },
                        { where: { id: item.product.id } }
                    );
                }

                createdOrders.push({
                    order,
                    storeGroup
                });

                // إرسال إشعار للمتجر
                try {
                    await this.sendNewOrderNotification(order, storeGroup);
                } catch (notificationError) {
                    console.error('Error sending order notification:', notificationError);
                }
            }

            // إرسال إشعار للعميل
            try {
                await this.sendOrderConfirmationNotification(customerId, createdOrders, totalAmount);
            } catch (notificationError) {
                console.error('Error sending customer notification:', notificationError);
            }

            // مسح السلة
            res.clearCookie('cart');

            res.redirect('/customers/orders?success=' + encodeURIComponent(`تم إنشاء ${createdOrders.length} طلب بنجاح! سيتم التواصل معك قريباً.`));

        } catch (error) {
            console.error('Error processing checkout:', error);
            req.flash('error', 'حدث خطأ في معالجة الطلب. يرجى المحاولة مرة أخرى.');
            res.redirect('/customers/checkout');
        }
      }

      // إرسال إشعار للمتجر عن الطلب الجديد
      async sendNewOrderNotification(order, storeGroup) {
        try {
            const NotificationService = require('../services/NotificationService');

            const itemsList = storeGroup.items.map(item =>
                `${item.product.name} (${item.quantity} × ${item.price} ريال)`
            ).join('\n');

            await NotificationService.notifyStore(order.storeId, {
                title: 'طلب جديد يحتاج للمعالجة',
                message: `لديك طلب جديد رقم #${order.id}\n\nالمنتجات:\n${itemsList}\n\nإجمالي المبلغ: ${storeGroup.subtotal} ريال\n\nعنوان التوصيل: ${order.deliveryAddress}`,
                type: 'order',
                priority: 'high',
                actionUrl: `/store/orders/${order.id}`,
                actionText: 'معالجة الطلب',
                data: {
                    orderId: order.id,
                    customerId: order.customerId,
                    totalAmount: storeGroup.subtotal,
                    itemsCount: storeGroup.items.length,
                    type: 'new_order'
                }
            });

            console.log(`Order notification sent to store ${order.storeId} for order ${order.id}`);

        } catch (error) {
            console.error('Error in sendNewOrderNotification:', error);
            throw error;
        }
      }

      // إرسال إشعار تأكيد للعميل
      async sendOrderConfirmationNotification(customerId, createdOrders, totalAmount) {
        try {
            const NotificationService = require('../services/NotificationService');

            const ordersList = createdOrders.map(({ order, storeGroup }) =>
                `طلب #${order.id} من ${storeGroup.store.name} (${storeGroup.subtotal} ريال)`
            ).join('\n');

            await NotificationService.notifyCustomer(customerId, {
                title: 'تم إنشاء طلباتك بنجاح',
                message: `تم إنشاء طلباتك بنجاح:\n\n${ordersList}\n\nإجمالي المبلغ: ${totalAmount} ريال\n\nسيتم معالجة طلباتك من قبل المتاجر قريباً.`,
                type: 'success',
                priority: 'normal',
                actionUrl: '/customers/orders',
                actionText: 'عرض طلباتي',
                data: {
                    ordersCount: createdOrders.length,
                    totalAmount: totalAmount,
                    type: 'orders_created'
                }
            });

            console.log(`Order confirmation notification sent to customer ${customerId}`);

        } catch (error) {
            console.error('Error in sendOrderConfirmationNotification:', error);
            throw error;
        }
      }



      async getOrders(req, res) {
        const customerId = req.user.id;

        try {
          const orders = await Order.findAll({
            where: { customerId },
            include: [{
              model: OrderDetail,
              as: 'orderDetails',
              include: [{
                model: Product,
                as: 'product',
                include: [{ model: Store, as: 'store' }]
              }]
            }],
            order: [['createdAt', 'DESC']]
          });

          res.render('customers/orders', { orders });
        } catch (err) {
          console.error('Order fetch error:', err);
          res.status(500).send('Internal Server Error');
        }
      };

      // عرض صفحة البروفايل
      async profile(req, res) {
        try {
          const customerId = req.user.id;

          const customer = await Customer.findByPk(customerId, {
            include: {
              model: Area,
              as: 'areas'
             }
          });

          const areas = await Area.findAll(); // جلب كل المناطق عشان خيارات الـ select

          res.render('customers/profile', { customer, areas });
        } catch (error) {
          console.error('Profile fetch error:', err);
          res.status(500).send('Error loading profile');
        }
      };

      // تحديث العنوان فقط
      async addAddress(req, res) {
        try {
          const customerId = req.user.id;
          const { areaId, address } = req.body;

          try {
            await CustomerArea.create({ customer_id: customerId, area_id: areaId, address });
            res.redirect('/customers/profile');
          } catch (err) {
            console.error(err);
            res.status(500).send('Error adding address');
          }
        } catch (err) {
          console.error('Address update error:', err);
          res.status(500).send('Error adding address');
        }
      };

      async deleteAddress(req, res) {
        const addressId = req.params.id;
        const customerId = req.user.id;
        try {
          await CustomerArea.destroy({ where: { area_id: addressId , customer_id: customerId } });
          res.redirect('/customers/profile');
        } catch (err) {
          console.error(err);
          res.status(500).send('Error deleting address');
        }
      };
    async create(req, res) {
        try {
            const areas = await Area.findAll();
            res.render('admin/customers/create', { areas });
        } catch (error) {
            res.status(500).render('error', { error });
        }
    }

    async store(req, res) {
        try {
            const { areas, ...customerData } = req.body;
            const customer = await Customer.create(customerData);
            if (areas) {
                await customer.setAreas(Array.isArray(areas) ? areas : [areas]);
            }
            res.redirect('/admin/customers');
        } catch (error) {
            res.status(500).render('error', { error });
        }
    }

    async edit(req, res) {
        try {
            const [customer, areas] = await Promise.all([
                Customer.findByPk(req.params.id, {
                    include: [{ model: Area, as: 'areas', through: 'CustomerAreas' }]
                }),
                Area.findAll()
            ]);
            if (!customer) {
                return res.status(404).render('error', { error: 'Customer not found' });
            }
            res.render('admin/customers/edit', { customer, areas });
        } catch (error) {
            res.status(500).render('error', { error });
        }
    }

    async update(req, res) {
        try {
            const { areas, ...customerData } = req.body;
            const customer = await Customer.findByPk(req.params.id);
            if (!customer) {
                return res.status(404).render('error', { error: 'Customer not found' });
            }
            await customer.update(customerData);
            if (areas) {
                await customer.setAreas(Array.isArray(areas) ? areas : [areas]);
            }
            res.redirect('/admin/customers');
        } catch (error) {
            res.status(500).render('error', { error });
        }
    }

    // دالة الحذف
    async delete(req, res) {
        try {
            const customer = await Customer.findByPk(req.params.id);
            if (!customer) {
                return res.status(404).render('error', { error: 'Customer not found' });
            }
            await customer.destroy();
            req.flash('success', 'Customer deleted successfully');
            res.redirect('/admin/customers');
        } catch (error) {
            res.status(500).render('error', { error });
        }
    }

    async updateStatus(req, res) {
        try {
            const { id } = req.params;
            const { status } = req.body;
            console.log(status);
            if (!['active', 'inactive', 'pending'].includes(status)) {
                req.flash('error', 'Invalid status value');
                return res.redirect('/admin/customers');
            }

            const customer = await Customer.findByPk(id);
            if (!customer) {
                return res.status(404).render('error', {
                    error: { status: 404, message: 'Store not found' }
                });
            }

            await Customer.update({ status }, { where: { id } });
            req.flash('success', `Customer status updated to ${status}`);
            res.redirect('/admin/customers');
        } catch (error) {
            console.error('Error updating customer status:', error);
            res.status(500).render('error', { error: { message: 'Unable to update customer status' } });
        }
    }
}

module.exports = new CustomersController();
