const BaseController = require('./BaseController');
const { Customer, Area, Order, Country, Category, Store, CustomerArea, Product, Cart, Image, OrderDetail, Notification } = require('../models');
const { buildSearchAndFilter, buildSortOptions, buildPaginationOptions, calculatePaginationInfo, sanitizeFilters } = require('../utils/searchFilter');
const { Op } = require('sequelize');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const CartService = require('../services/CartService');
const { Console } = require('winston/lib/winston/transports');

class CustomersController extends BaseController {
    constructor() {
        super(Customer, 'customers');
    }

    /**
     * API: الحصول على الصفحة الرئيسية للعميل مع الفئات والمتاجر
     * GET /api/customers/home
     */
    async getHome(req, res) {
        try {
            const customerId = req.customer?.id;
            console.log('Customer ID:', customerId);

            // جلب الفئات مع المتاجر
            const categories = await Category.findAll({
              attributes: ['id', 'name'],
                order: [['name', 'ASC']]
            });

            res.json({
                success: true,
                message: 'تم جلب البيانات بنجاح',
                data:  categories
            });

        } catch (error) {
            console.error('خطأ في جلب الصفحة الرئيسية:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    async showall(req, res) {
      try {
          // إعداد خيارات البحث والفلتر
          const searchFields = {
              text: ['name', 'barcode', 'phoneNumber'],
              numeric: ['id']
          };

          const filterFields = {
              status: { type: 'exact' },
              createdAt: { type: 'date' }
          };

          // بناء شروط البحث والفلتر
          const whereClause = buildSearchAndFilter(req.query, searchFields, filterFields);

          // خيارات الترتيب
          const sortOptions = buildSortOptions(req.query.sortBy, req.query.sortOrder, 'createdAt');

          // خيارات الـ pagination
          const paginationOptions = buildPaginationOptions(req.query.page, req.query.limit);

          // جلب البيانات
          const { count, rows: customers } = await Customer.findAndCountAll({
              where: whereClause,
              include: [
                  { model: Area, as: 'areas', through: 'CustomerAreas' },
                  { model: Order, as: 'orders' }
              ],
              order: sortOptions,
              limit: paginationOptions.limit,
              offset: paginationOptions.offset,
              distinct: true
          });

          // حساب معلومات الـ pagination
          const pagination = calculatePaginationInfo(count, paginationOptions.page, paginationOptions.limit);

          // تنظيف الفلاتر للعرض
          const filters = sanitizeFilters(req.query);

          // خيارات الترتيب للعرض
          const sortOptionsForView = [
              { value: 'createdAt', label: 'تاريخ الإنشاء' },
              { value: 'name', label: 'اسم العميل' },
              { value: 'barcode', label: 'الباركود' },
              { value: 'status', label: 'الحالة' }
          ];

          res.render('admin/customers/index', {
              customers,
              pagination,
              filters,
              sortOptions: sortOptionsForView,
              currentUrl: req.originalUrl,
              originalUrl: req.originalUrl,
              activeFiltersCount: Object.keys(filters).length
          });
      } catch (error) {
          console.error('Error fetching customers:', error);
          res.status(500).render('error', { error });
      }
  }

    async show(req, res) {
        const customerId = req.params.id;

        try {
            const customer = await Customer.findByPk(customerId, {
                include: [{
                  model: Area,
                  as: 'areas',
                  include: [{ model: Country, as: 'country' }]
                }]
              });

          if (!customer) {
            return res.status(404).render('error', { error: { message: 'الزبون غير موجود' } });
          }

          res.render('stores/customers', { customer });
        } catch (error) {
          console.error("Error fetching customer:", error);
          res.status(500).render('error', { error: { message: 'حدث خطأ أثناء جلب بيانات الزبون' } });
        }
      }


    /**
     * API: الحصول على متاجر حسب الفئة
     * GET /api/customers/categories/:id
     */
    async getStoresByCategory(req, res) {
        try {
            const categoryId = req.params.id;
            const { page = 1, limit = 2, search = '', areaId = null } = req.query;
            console.log("getStoresByCategory");
            const category = await Category.findByPk(categoryId);
            if (!category) {
                return res.status(404).json({
                    success: false,
                    message: 'الفئة غير موجودة',
                    data: null
                });
            }

            const offset = (page - 1) * limit;
            const whereClause = { status: "active" };

            // فلتر البحث
            if (search) {
                whereClause[Op.or] = [
                    { name: { [Op.like]: `%${search}%` } },
                    { description: { [Op.like]: `%${search}%` } }
                ];
            }

            // فلتر المنطقة
            if (areaId) {
                whereClause.areaId = areaId;
            }

            // بناء include بشكل مرن
            const includeClause = [
                {
                    model: Area,
                    as: 'area',
                    attributes: ['name']
                }
            ];

            // تنفيذ الاستعلام
            const { count, rows: stores } = await Store.findAndCountAll({
                where: whereClause,
                include: includeClause,
                attributes: ['id', 'name', 'address', 'phoneNumber'],
                limit: parseInt(limit) || 10,
                offset: parseInt(offset) || 0,
                order: [['createdAt', 'DESC']],
                distinct: true
            });



            res.json({
                success: true,
                message: 'تم جلب المتاجر بنجاح',
                data: {
                   /* category: {
                        id: category.id,
                        name: category.name,
                        description: category.description
                    },*/
                    stores,
                  /*  pagination: {
                        currentPage: parseInt(page),
                        totalPages: Math.ceil(count / limit),
                        totalItems: count,
                        itemsPerPage: parseInt(limit)
                    }*/
                }
            });

        } catch (error) {
            console.error('خطأ في جلب متاجر الفئة:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    /**
     * API: الحصول على منتجات متجر معين
     * GET /api/customers/stores/:id
     */
    async getStoreProducts(req, res) {
        try {
            console.log("getStoreProducts");
            const storeId = parseInt(req.params.id);
            const { page = 1, limit = 10, search = '', categoryId = null, minPrice = null, maxPrice = null } = req.query;

            const store = await Store.findByPk(storeId, {
                where: { status: "active" },
                attributes: ['id', 'name', 'address', 'phoneNumber']
            });

            if (!store) {
                return res.status(404).json({
                    success: false,
                    message: 'المتجر غير موجود',
                    data: null
                });
            }

            const offset = (page - 1) * limit;
            const whereClause = {
                storeId: storeId,
                status: "active"
            };

            // فلتر البحث
            if (search) {
                whereClause[Op.or] = [
                    { name: { [Op.like]: `%${search}%` } },
                    { description: { [Op.like]: `%${search}%` } }
                ];
            }

            // فلتر الفئة
            if (categoryId) {
                whereClause.categoryId = categoryId;
            }

            // فلتر السعر
            if (minPrice || maxPrice) {
                whereClause.price = {};
                if (minPrice) whereClause.price[Op.gte] = parseFloat(minPrice);
                if (maxPrice) whereClause.price[Op.lte] = parseFloat(maxPrice);
            }

            const { count, rows: products } = await Product.findAndCountAll({
                where: whereClause,
                include: [
                    {
                        model: Image,
                        as: 'images',
                        attributes: ['image'],
                        required: false
                    }
                ],
                attributes: ['id', 'name', 'description', 'price', 'quantity','createdAt'],
                limit: parseInt(limit),
                offset: parseInt(offset),
                order: [['createdAt', 'DESC']]
            });


            res.json({
                success: true,
                message: 'تم جلب منتجات المتجر بنجاح',
                data: {
                   /* store: {
                        id: store.id,
                        name: store.name,
                        description: store.description,
                        image: store.image ? `/uploads/${store.image}` : null,
                        rating: store.rating || 0,
                        phone: store.phone,
                        address: store.address
                    },*/
                    products: products,
                 /*  pagination: {
                        currentPage: parseInt(page),
                        totalPages: Math.ceil(count / limit),
                        totalItems: count,
                        itemsPerPage: parseInt(limit)
                    }*/
                }
            });

        } catch (error) {
            console.error('خطأ في جلب منتجات المتجر:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    /**
     * API: الحصول على تفاصيل منتج
     * GET /api/customers/products/:id
     */
    async getProductDetails(req, res) {
        try {
            const productId = req.params.id;

            const product = await Product.findOne({
                where: {
                    id: productId,
                   status: "active"
                },
                attributes: ['id', 'name', 'description', 'price', 'quantity'],
                include: [
                    {
                        model: Store,
                        as: 'store',
                        attributes: ['name'],
                        include: [
                            {
                                model: Area,
                                as: 'area',
                                attributes: ['name']
                            }
                        ]
                    },
                    {
                        model: Category,
                        as: 'category',
                        attributes: ['name']
                    },
                    {
                        model: Image,
                        as: 'images',
                        attributes: ['image']
                    }
                ]
            });

            if (!product) {
                return res.status(404).json({
                    success: false,
                    message: 'المنتج غير موجود',
                    data: null
                });
            }

            res.json({
                success: true,
                message: 'تم جلب تفاصيل المنتج بنجاح',
                data: { product: [product] }
            });

        } catch (error) {
            console.error('خطأ في جلب تفاصيل المنتج:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    /**
     * API: إضافة منتج للسلة
     * POST /api/customers/cart/add
     */
    async addToCart(req, res) {
        try {
            const customerId = 1;//req.customer.id;
            const { productId, quantity = 1 ,sessionId = null} = req.body;

            const cartItem = await CartService.addToCart(customerId, productId, quantity,sessionId);

            const cartCount = await CartService.getCartCount(customerId, sessionId);

            res.json({
                success: true,
                message: 'تم إضافة المنتج للسلة بنجاح',
                data: {
                    cartItem: cartItem,
                    cartCount: cartCount || 0
                }
            });

        } catch (error) {
            console.error('خطأ في إضافة المنتج للسلة:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }


    /**
     * API: عرض محتويات السلة
     * GET /api/customers/cart
     */
    async getCart(req, res) {
        try {
            const customerId = req.customer.id;

            // استخدام CartService
            const cartData = await CartService.getCart(customerId);

            // تنسيق البيانات للإرجاع
            const formattedCartItems = cartData.cartItems.map(item => ({
                id: item.id,
                quantity: item.quantity,
                price: parseFloat(item.price),
                totalPrice: parseFloat(item.totalPrice),
                notes: item.notes,
                product: {
                    id: item.product.id,
                    name: item.product.name,
                    description: item.product.description,
                    price: parseFloat(item.product.price),
                    quantity: item.product.quantity,
                    category: item.product.category ? {
                        id: item.product.category.id,
                        name: item.product.category.name
                    } : null
                },
                store: {
                    name: item.product.store.name,
                }
            }));

            const formattedStoreGroups = cartData.storeGroups.map(group => ({
                store: {
                    name: group.store.name,
                },
                items: group.items.map(item => ({
                    id: item.id,
                    quantity: item.quantity,
                    price: parseFloat(item.price),
                    totalPrice: parseFloat(item.totalPrice),
                    notes: item.notes,
                    product: {
                        id: item.product.id,
                        name: item.product.name,
                        description: item.product.description,
                        price: parseFloat(item.product.price),
                        quantity: item.product.quantity,
                        category: item.product.category ? {
                            id: item.product.category.id,
                            name: item.product.category.name
                        } : null
                    }
                })),
                subtotal: group.subtotal,
                itemsCount: group.itemsCount
            }));

            res.json({
                success: true,
                message: 'تم جلب محتويات السلة بنجاح',
                data: {
                    cartItems: formattedCartItems,
                    storeGroups: formattedStoreGroups,
                    summary: cartData.summary
                }
            });

        } catch (error) {
            console.error('خطأ في جلب السلة:', error);
            res.status(500).json({
                success: false,
                message: error.message || 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    /**
     * API: حذف منتج من السلة
     * DELETE /api/customers/cart/:id
     */
    async removeFromCart(req, res) {
        try {
            const customerId = req.customer.id;
            const cartItemId = req.params.id;

            // استخدام CartService
            await CartService.removeFromCart(cartItemId, customerId);

            // جلب عدد العناصر المتبقية في السلة
            const cartCount = await CartService.getCartCount(customerId);

            res.json({
                success: true,
                message: 'تم حذف المنتج من السلة بنجاح',
                data: {
                    cartCount: cartCount
                }
            });

        } catch (error) {
            console.error('خطأ في حذف المنتج من السلة:', error);

            let statusCode = 500;
            if (error.message.includes('غير موجود')) {
                statusCode = 404;
            }

            res.status(statusCode).json({
                success: false,
                message: error.message,
                data: null
            });
        }
    }

    /**
     * API: تحديث كمية منتج في السلة
     * PUT /api/customers/cart/:id
     */
    async updateCartItem(req, res) {
        try {
            const customerId = 1;//req.customer.id;
            const cartItemId = req.params.id;
            const { quantity } = req.body;

            if (!quantity || quantity < 1) {
                return res.status(400).json({
                    success: false,
                    message: 'الكمية يجب أن تكون أكبر من صفر',
                    data: null
                });
            }

            // استخدام CartService
            const updatedCartItem = await CartService.updateCartItem(cartItemId, quantity, customerId);

            res.json({
                success: true,
                message: 'تم تحديث الكمية بنجاح',
                data: {
                    cartItem: {
                        id: updatedCartItem.id,
                        quantity: updatedCartItem.quantity,
                        price: parseFloat(updatedCartItem.price),
                        totalPrice: parseFloat(updatedCartItem.totalPrice)
                    }
                }
            });

        } catch (error) {
            console.error('خطأ في تحديث السلة:', error);

            let statusCode = 500;
            if (error.message.includes('غير موجود')) {
                statusCode = 404;
            } else if (error.message.includes('الكمية') || error.message.includes('متاحة')) {
                statusCode = 400;
            }

            res.status(statusCode).json({
                success: false,
                message: error.message,
                data: null
            });
        }
    }

    /**
     * API: مسح السلة بالكامل
     * DELETE /api/customers/cart
     */
    async clearCart(req, res) {
        try {
            const customerId = req.customer.id;

            // استخدام CartService
            await CartService.clearCart(customerId);

            res.json({
                success: true,
                message: 'تم مسح السلة بنجاح',
                data: null
            });

        } catch (error) {
            console.error('خطأ في مسح السلة:', error);
            res.status(500).json({
                success: false,
                message: error.message || 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

      // عرض صفحة الدفع
      async showCheckout(req, res) {
        try {
            const customerId = req.user.id;

            let cart = [];
            if (req.cookies.cart) {
              try {
                cart = JSON.parse(req.cookies.cart);
              } catch (e) {
                cart = [];
              }
            }
            if (cart.length === 0) {
                req.flash('error', 'سلة التسوق فارغة');
                return res.redirect('/customers/cart');
            }

            // جلب تفاصيل المنتجات من قاعدة البيانات
            const cartItems = [];
            let totalPrice = 0;
            const storeGroups = new Map();

            for (const item of cart) {
                const product = await Product.findByPk(item.productId, {
                    include: [
                        { model: Store, as: 'store' },
                        { model: Image, as: 'images' }
                    ]
                });

                if (product && product.quantity >= item.quantity) {
                    const itemTotal = product.price * item.quantity;
                    totalPrice += itemTotal;

                    const cartItem = {
                        product,
                        quantity: item.quantity,
                        total: itemTotal
                    };

                    cartItems.push(cartItem);

                    // تجميع المنتجات حسب المتجر
                    const storeId = product.store.id;
                    if (!storeGroups.has(storeId)) {
                        storeGroups.set(storeId, {
                            store: product.store,
                            items: [],
                            subtotal: 0
                        });
                    }

                    const storeGroup = storeGroups.get(storeId);
                    storeGroup.items.push(cartItem);
                    storeGroup.subtotal += itemTotal;
                }
            }

            // جلب بيانات العميل
            const customer = await Customer.findByPk(customerId, {
                include: [{ model: Area, as: 'areas' }]
            });

            res.render('customers/checkout/index', {
                customer,
                cartItems,
                storeGroups: Array.from(storeGroups.values()),
                totalPrice
            });

        } catch (error) {
            console.error('Error showing checkout:', error);
            res.status(500).render('error', {
                error: { message: 'حدث خطأ في عرض صفحة الدفع' }
            });
        }
      }

      // معالجة إنشاء الطلب
      async processCheckout(req, res) {
        try {
            const customerId = req.user.id;

            let cart = [];
            if (req.cookies.cart) {
              try {
                cart = JSON.parse(req.cookies.cart);
              } catch (e) {
                cart = [];
              }
            }
            if (cart.length === 0) {
                req.flash('error', 'سلة التسوق فارغة');
                return res.redirect('/customers/cart');
            }

            const { deliveryAddress, notes } = req.body;

            // تجميع المنتجات حسب المتجر
            const storeGroups = new Map();
            let totalAmount = 0;

            for (const item of cart) {
                const product = await Product.findByPk(item.productId, {
                    include: [{ model: Store, as: 'store' }]
                });

                if (!product || product.quantity < item.quantity) {
                    req.flash('error', `المنتج ${product ? product.name : 'غير موجود'} غير متوفر بالكمية المطلوبة`);
                    return res.redirect('/customers/cart');
                }

                const storeId = product.store.id;
                const itemTotal = product.price * item.quantity;
                totalAmount += itemTotal;

                if (!storeGroups.has(storeId)) {
                    storeGroups.set(storeId, {
                        store: product.store,
                        items: [],
                        subtotal: 0
                    });
                }

                const storeGroup = storeGroups.get(storeId);
                storeGroup.items.push({
                    product,
                    quantity: item.quantity,
                    price: product.price,
                    total: itemTotal
                });
                storeGroup.subtotal += itemTotal;
            }

            // إنشاء طلب منفصل لكل متجر
            const createdOrders = [];

            for (const [storeId, storeGroup] of storeGroups) {
                const orderData = {
                    customerId: customerId,
                    storeId: storeId,
                    totalAmount: storeGroup.subtotal,
                    status: 'pending',
                    deliveryAddress: deliveryAddress || 'العنوان الافتراضي',
                    notes: notes || null
                };

                // إنشاء الطلب
                const order = await Order.create(orderData);

                // إنشاء تفاصيل الطلب
                const orderDetails = storeGroup.items.map(item => ({
                    orderId: order.id,
                    productId: item.product.id,
                    quantity: item.quantity,
                    totalPrice: item.total
                }));

                await OrderDetail.bulkCreate(orderDetails);

                // تحديث كمية المنتجات
                for (const item of storeGroup.items) {
                    await Product.update(
                        { quantity: item.product.quantity - item.quantity },
                        { where: { id: item.product.id } }
                    );
                }

                createdOrders.push({
                    order,
                    storeGroup
                });

                // إرسال إشعار للمتجر
                try {
                    await this.sendNewOrderNotification(order, storeGroup);
                } catch (notificationError) {
                    console.error('Error sending order notification:', notificationError);
                }
            }

            // إرسال إشعار للعميل
            try {
                await this.sendOrderConfirmationNotification(customerId, createdOrders, totalAmount);
            } catch (notificationError) {
                console.error('Error sending customer notification:', notificationError);
            }

            // مسح السلة
            res.clearCookie('cart');

            res.redirect('/customers/orders?success=' + encodeURIComponent(`تم إنشاء ${createdOrders.length} طلب بنجاح! سيتم التواصل معك قريباً.`));

        } catch (error) {
            console.error('Error processing checkout:', error);
            req.flash('error', 'حدث خطأ في معالجة الطلب. يرجى المحاولة مرة أخرى.');
            res.redirect('/customers/checkout');
        }
      }

      // إرسال إشعار للمتجر عن الطلب الجديد
      async sendNewOrderNotification(order, storeGroup) {
        try {
            const NotificationService = require('../services/NotificationService');

            const itemsList = storeGroup.items.map(item =>
                `${item.product.name} (${item.quantity} × ${item.price} ريال)`
            ).join('\n');

            await NotificationService.notifyStore(order.storeId, {
                title: 'طلب جديد يحتاج للمعالجة',
                message: `لديك طلب جديد رقم #${order.id}\n\nالمنتجات:\n${itemsList}\n\nإجمالي المبلغ: ${storeGroup.subtotal} ريال\n\nعنوان التوصيل: ${order.deliveryAddress}`,
                type: 'order',
                priority: 'high',
                actionUrl: `/store/orders/${order.id}`,
                actionText: 'معالجة الطلب',
                data: {
                    orderId: order.id,
                    customerId: order.customerId,
                    totalAmount: storeGroup.subtotal,
                    itemsCount: storeGroup.items.length,
                    type: 'new_order'
                }
            });

            console.log(`Order notification sent to store ${order.storeId} for order ${order.id}`);

        } catch (error) {
            console.error('Error in sendNewOrderNotification:', error);
            throw error;
        }
      }

      // إرسال إشعار تأكيد للعميل
      async sendOrderConfirmationNotification(customerId, createdOrders, totalAmount) {
        try {
            const NotificationService = require('../services/NotificationService');

            const ordersList = createdOrders.map(({ order, storeGroup }) =>
                `طلب #${order.id} من ${storeGroup.store.name} (${storeGroup.subtotal} ريال)`
            ).join('\n');

            await NotificationService.notifyCustomer(customerId, {
                title: 'تم إنشاء طلباتك بنجاح',
                message: `تم إنشاء طلباتك بنجاح:\n\n${ordersList}\n\nإجمالي المبلغ: ${totalAmount} ريال\n\nسيتم معالجة طلباتك من قبل المتاجر قريباً.`,
                type: 'success',
                priority: 'normal',
                actionUrl: '/customers/orders',
                actionText: 'عرض طلباتي',
                data: {
                    ordersCount: createdOrders.length,
                    totalAmount: totalAmount,
                    type: 'orders_created'
                }
            });

            console.log(`Order confirmation notification sent to customer ${customerId}`);

        } catch (error) {
            console.error('Error in sendOrderConfirmationNotification:', error);
            throw error;
        }
      }



      async getOrders(req, res) {
        const customerId = req.user.id;

        try {
          const orders = await Order.findAll({
            where: { customerId },
            include: [{
              model: OrderDetail,
              as: 'orderDetails',
              include: [{
                model: Product,
                as: 'product',
                include: [{ model: Store, as: 'store' }]
              }]
            }],
            order: [['createdAt', 'DESC']]
          });

          res.render('customers/orders', { orders });
        } catch (err) {
          console.error('Order fetch error:', err);
          res.status(500).send('Internal Server Error');
        }
      };

      // عرض صفحة البروفايل
      async profile(req, res) {
        try {
          const customerId = req.user.id;

          const customer = await Customer.findByPk(customerId, {
            include: {
              model: Area,
              as: 'areas'
             }
          });

          const areas = await Area.findAll(); // جلب كل المناطق عشان خيارات الـ select

          res.render('customers/profile', { customer, areas });
        } catch (error) {
          console.error('Profile fetch error:', err);
          res.status(500).send('Error loading profile');
        }
      };

      // تحديث العنوان فقط
      async addAddress(req, res) {
        try {
          const customerId = req.user.id;
          const { areaId, address } = req.body;

          try {
            await CustomerArea.create({ customer_id: customerId, area_id: areaId, address });
            res.redirect('/customers/profile');
          } catch (err) {
            console.error(err);
            res.status(500).send('Error adding address');
          }
        } catch (err) {
          console.error('Address update error:', err);
          res.status(500).send('Error adding address');
        }
      };

      async deleteAddress(req, res) {
        const addressId = req.params.id;
        const customerId = req.user.id;
        try {
          await CustomerArea.destroy({ where: { area_id: addressId , customer_id: customerId } });
          res.redirect('/customers/profile');
        } catch (err) {
          console.error(err);
          res.status(500).send('Error deleting address');
        }
      };
      
    async create(req, res) {
        try {
            const areas = await Area.findAll();
            res.render('admin/customers/create', { areas });
        } catch (error) {
            res.status(500).render('error', { error });
        }
    }

    async store(req, res) {
        try {
            const { areas, ...customerData } = req.body;
            const customer = await Customer.create(customerData);
            if (areas) {
                await customer.setAreas(Array.isArray(areas) ? areas : [areas]);
            }
            res.redirect('/admin/customers');
        } catch (error) {
            res.status(500).render('error', { error });
        }
    }

    async edit(req, res) {
        try {
            const [customer, areas] = await Promise.all([
                Customer.findByPk(req.params.id, {
                    include: [{ model: Area, as: 'areas', through: 'CustomerAreas' }]
                }),
                Area.findAll()
            ]);
            if (!customer) {
                return res.status(404).render('error', { error: 'Customer not found' });
            }
            res.render('admin/customers/edit', { customer, areas });
        } catch (error) {
            res.status(500).render('error', { error });
        }
    }

    async update(req, res) {
        try {
            const { areas, ...customerData } = req.body;
            const customer = await Customer.findByPk(req.params.id);
            if (!customer) {
                return res.status(404).render('error', { error: 'Customer not found' });
            }
            await customer.update(customerData);
            if (areas) {
                await customer.setAreas(Array.isArray(areas) ? areas : [areas]);
            }
            res.redirect('/admin/customers');
        } catch (error) {
            res.status(500).render('error', { error });
        }
    }

    // دالة الحذف
    async delete(req, res) {
        try {
            const customer = await Customer.findByPk(req.params.id);
            if (!customer) {
                return res.status(404).render('error', { error: 'Customer not found' });
            }
            await customer.destroy();
            req.flash('success', 'Customer deleted successfully');
            res.redirect('/admin/customers');
        } catch (error) {
            res.status(500).render('error', { error });
        }
    }

    async updateStatus(req, res) {
        try {
            const { id } = req.params;
            const { status } = req.body;
            console.log(status);
            if (!['active', 'inactive', 'pending'].includes(status)) {
                req.flash('error', 'Invalid status value');
                return res.redirect('/admin/customers');
            }

            const customer = await Customer.findByPk(id);
            if (!customer) {
                return res.status(404).render('error', {
                    error: { status: 404, message: 'Store not found' }
                });
            }

            await Customer.update({ status }, { where: { id } });
            req.flash('success', `Customer status updated to ${status}`);
            res.redirect('/admin/customers');
        } catch (error) {
            console.error('Error updating customer status:', error);
            res.status(500).render('error', { error: { message: 'Unable to update customer status' } });
        }
    }
}

module.exports = new CustomersController();
