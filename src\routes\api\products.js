const express = require('express');
const router = express.Router();
const ProductApiController = require('../../controllers/api/ProductApiController');
const { optionalAuth } = require('../../middleware/apiAuth');

/**
 * مسارات المنتجات (عامة - لا تحتاج مصادقة)
 */

// الحصول على جميع المنتجات
router.get('/', optionalAuth, ProductApiController.getAllProducts);

// البحث في المنتجات
router.get('/search', optionalAuth, ProductApiController.searchProducts);

// الحصول على المنتجات المميزة
router.get('/featured', optionalAuth, ProductApiController.getFeaturedProducts);

// الحصول على منتجات حسب الفئة
router.get('/category/:categoryId', optionalAuth, ProductApiController.getProductsByCategory);

// الحصول على تفاصيل منتج واحد
router.get('/:id', optionalAuth, ProductApiController.getProductById);

module.exports = router;
