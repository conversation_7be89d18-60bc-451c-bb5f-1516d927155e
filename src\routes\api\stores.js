const express = require('express');
const router = express.Router();
const StoreApiController = require('../../controllers/api/StoreApiController');
const { optionalAuth } = require('../../middleware/apiAuth');

/**
 * مسارات المتاجر (عامة - لا تحتاج مصادقة)
 */

// الحصول على جميع المتاجر
router.get('/', optionalAuth, StoreApiController.getAllStores);

// البحث في المتاجر
router.get('/search', optionalAuth, StoreApiController.searchStores);

// الحصول على المتاجر المميزة
router.get('/featured', optionalAuth, StoreApiController.getFeaturedStores);

// الحصول على تفاصيل متجر واحد
router.get('/:id', optionalAuth, StoreApiController.getStoreById);

module.exports = router;
