'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class Category extends Model {
    static associate(models) {
      // A category can belong to many stores (many-to-many)
      Category.belongsToMany(models.Store, {
        through: 'StoreCategories',
        foreignKey: 'categoryId',
        otherKey: 'storeId',
        as: 'stores'
      });

      // A category can have many products (one-to-many)
      Category.hasMany(models.Product, {
        foreignKey: 'categoryId',
        as: 'products'
      });
    }
  }
  
  Category.init({
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    status: {
      type: DataTypes.ENUM('active', 'inactive'),
      defaultValue: 'active',
      allowNull: false
    }
  }, {
    sequelize,
    modelName: 'Category',
  });
  
  return Category;
};
