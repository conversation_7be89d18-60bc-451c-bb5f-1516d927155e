const { Product, Store, Category, Area, Country } = require('../../models');
const { Op } = require('sequelize');

class ProductApiController {

    /**
     * الحصول على جميع المنتجات
     * GET /api/products
     */
    async getAllProducts(req, res) {
        try {
            const { 
                page = 1, 
                limit = 10, 
                search = '', 
                categoryId = null,
                storeId = null,
                minPrice = null,
                maxPrice = null,
                sortBy = 'createdAt',
                sortOrder = 'DESC'
            } = req.query;

            const offset = (page - 1) * limit;
            const whereClause = {
                isActive: true
            };

            // البحث بالاسم أو الوصف
            if (search) {
                whereClause[Op.or] = [
                    { name: { [Op.like]: `%${search}%` } },
                    { description: { [Op.like]: `%${search}%` } }
                ];
            }

            // فلتر بالفئة
            if (categoryId) {
                whereClause.categoryId = categoryId;
            }

            // فلتر بالمتجر
            if (storeId) {
                whereClause.storeId = storeId;
            }

            // فلتر بالسعر
            if (minPrice || maxPrice) {
                whereClause.price = {};
                if (minPrice) whereClause.price[Op.gte] = parseFloat(minPrice);
                if (maxPrice) whereClause.price[Op.lte] = parseFloat(maxPrice);
            }

            const { count, rows: products } = await Product.findAndCountAll({
                where: whereClause,
                include: [
                    {
                        model: Category,
                        attributes: ['id', 'name']
                    },
                    {
                        model: Store,
                        attributes: ['id', 'name', 'rating'],
                        include: [
                            {
                                model: Area,
                                attributes: ['id', 'name'],
                                include: [
                                    {
                                        model: Country,
                                        attributes: ['id', 'name']
                                    }
                                ]
                            }
                        ]
                    }
                ],
                limit: parseInt(limit),
                offset: parseInt(offset),
                order: [[sortBy, sortOrder.toUpperCase()]]
            });

            // تنسيق البيانات للموبايل
            const formattedProducts = products.map(product => ({
                id: product.id,
                name: product.name,
                description: product.description,
                price: parseFloat(product.price),
                image: product.image ? `/uploads/${product.image}` : null,
                isActive: product.isActive,
                category: product.Category ? {
                    id: product.Category.id,
                    name: product.Category.name
                } : null,
                store: product.Store ? {
                    id: product.Store.id,
                    name: product.Store.name,
                    rating: product.Store.rating || 0,
                    area: product.Store.Area ? {
                        id: product.Store.Area.id,
                        name: product.Store.Area.name,
                        country: product.Store.Area.Country ? product.Store.Area.Country.name : null
                    } : null
                } : null,
                createdAt: product.createdAt
            }));

            res.json({
                success: true,
                message: 'تم جلب المنتجات بنجاح',
                data: {
                    products: formattedProducts,
                    pagination: {
                        currentPage: parseInt(page),
                        totalPages: Math.ceil(count / limit),
                        totalItems: count,
                        itemsPerPage: parseInt(limit),
                        hasNextPage: page * limit < count,
                        hasPrevPage: page > 1
                    }
                }
            });

        } catch (error) {
            console.error('خطأ في جلب المنتجات:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    /**
     * الحصول على تفاصيل منتج واحد
     * GET /api/products/:id
     */
    async getProductById(req, res) {
        try {
            const { id } = req.params;

            const product = await Product.findByPk(id, {
                include: [
                    {
                        model: Category,
                        attributes: ['id', 'name']
                    },
                    {
                        model: Store,
                        attributes: ['id', 'name', 'description', 'phone', 'address', 'rating'],
                        include: [
                            {
                                model: Area,
                                attributes: ['id', 'name'],
                                include: [
                                    {
                                        model: Country,
                                        attributes: ['id', 'name']
                                    }
                                ]
                            }
                        ]
                    }
                ]
            });

            if (!product || !product.isActive) {
                return res.status(404).json({
                    success: false,
                    message: 'المنتج غير موجود',
                    data: null
                });
            }

            // الحصول على منتجات مشابهة
            const relatedProducts = await Product.findAll({
                where: {
                    categoryId: product.categoryId,
                    id: { [Op.ne]: product.id },
                    isActive: true
                },
                include: [
                    {
                        model: Store,
                        attributes: ['id', 'name', 'rating']
                    }
                ],
                limit: 5,
                order: [['createdAt', 'DESC']]
            });

            // تنسيق البيانات
            const formattedProduct = {
                id: product.id,
                name: product.name,
                description: product.description,
                price: parseFloat(product.price),
                image: product.image ? `/uploads/${product.image}` : null,
                isActive: product.isActive,
                category: product.Category ? {
                    id: product.Category.id,
                    name: product.Category.name
                } : null,
                store: product.Store ? {
                    id: product.Store.id,
                    name: product.Store.name,
                    description: product.Store.description,
                    phone: product.Store.phone,
                    address: product.Store.address,
                    rating: product.Store.rating || 0,
                    area: product.Store.Area ? {
                        id: product.Store.Area.id,
                        name: product.Store.Area.name,
                        country: product.Store.Area.Country ? product.Store.Area.Country.name : null
                    } : null
                } : null,
                relatedProducts: relatedProducts.map(rp => ({
                    id: rp.id,
                    name: rp.name,
                    price: parseFloat(rp.price),
                    image: rp.image ? `/uploads/${rp.image}` : null,
                    store: rp.Store ? {
                        id: rp.Store.id,
                        name: rp.Store.name,
                        rating: rp.Store.rating || 0
                    } : null
                })),
                createdAt: product.createdAt
            };

            res.json({
                success: true,
                message: 'تم جلب بيانات المنتج بنجاح',
                data: { product: formattedProduct }
            });

        } catch (error) {
            console.error('خطأ في جلب بيانات المنتج:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    /**
     * البحث في المنتجات
     * GET /api/products/search
     */
    async searchProducts(req, res) {
        try {
            const { 
                q = '', 
                categoryId = null,
                minPrice = null,
                maxPrice = null,
                areaId = null,
                page = 1,
                limit = 10
            } = req.query;

            const offset = (page - 1) * limit;
            const whereClause = {
                isActive: true,
                [Op.or]: [
                    { name: { [Op.like]: `%${q}%` } },
                    { description: { [Op.like]: `%${q}%` } }
                ]
            };

            // فلتر بالفئة
            if (categoryId) {
                whereClause.categoryId = categoryId;
            }

            // فلتر بالسعر
            if (minPrice || maxPrice) {
                whereClause.price = {};
                if (minPrice) whereClause.price[Op.gte] = parseFloat(minPrice);
                if (maxPrice) whereClause.price[Op.lte] = parseFloat(maxPrice);
            }

            // إعداد include للمتجر والمنطقة
            const includeArray = [
                {
                    model: Category,
                    attributes: ['id', 'name']
                },
                {
                    model: Store,
                    attributes: ['id', 'name', 'rating'],
                    include: [
                        {
                            model: Area,
                            attributes: ['id', 'name']
                        }
                    ]
                }
            ];

            // فلتر بالمنطقة
            if (areaId) {
                includeArray[1].include[0].where = { id: areaId };
                includeArray[1].required = true;
            }

            const { count, rows: products } = await Product.findAndCountAll({
                where: whereClause,
                include: includeArray,
                limit: parseInt(limit),
                offset: parseInt(offset),
                order: [['createdAt', 'DESC']]
            });

            const formattedProducts = products.map(product => ({
                id: product.id,
                name: product.name,
                description: product.description,
                price: parseFloat(product.price),
                image: product.image ? `/uploads/${product.image}` : null,
                category: product.Category ? {
                    id: product.Category.id,
                    name: product.Category.name
                } : null,
                store: product.Store ? {
                    id: product.Store.id,
                    name: product.Store.name,
                    rating: product.Store.rating || 0,
                    area: product.Store.Area ? product.Store.Area.name : null
                } : null
            }));

            res.json({
                success: true,
                message: 'تم البحث بنجاح',
                data: {
                    products: formattedProducts,
                    searchQuery: q,
                    pagination: {
                        currentPage: parseInt(page),
                        totalPages: Math.ceil(count / limit),
                        totalItems: count,
                        itemsPerPage: parseInt(limit)
                    }
                }
            });

        } catch (error) {
            console.error('خطأ في البحث:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    /**
     * الحصول على المنتجات المميزة
     * GET /api/products/featured
     */
    async getFeaturedProducts(req, res) {
        try {
            const { limit = 10 } = req.query;

            const products = await Product.findAll({
                where: {
                    isActive: true
                },
                include: [
                    {
                        model: Category,
                        attributes: ['id', 'name']
                    },
                    {
                        model: Store,
                        attributes: ['id', 'name', 'rating'],
                        where: { rating: { [Op.gte]: 4.0 } }, // من متاجر مميزة
                        required: true
                    }
                ],
                order: [['createdAt', 'DESC']],
                limit: parseInt(limit)
            });

            const formattedProducts = products.map(product => ({
                id: product.id,
                name: product.name,
                price: parseFloat(product.price),
                image: product.image ? `/uploads/${product.image}` : null,
                category: product.Category ? product.Category.name : null,
                store: product.Store ? {
                    id: product.Store.id,
                    name: product.Store.name,
                    rating: product.Store.rating || 0
                } : null
            }));

            res.json({
                success: true,
                message: 'تم جلب المنتجات المميزة بنجاح',
                data: { products: formattedProducts }
            });

        } catch (error) {
            console.error('خطأ في جلب المنتجات المميزة:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    /**
     * الحصول على منتجات حسب الفئة
     * GET /api/products/category/:categoryId
     */
    async getProductsByCategory(req, res) {
        try {
            const { categoryId } = req.params;
            const { page = 1, limit = 10 } = req.query;

            const offset = (page - 1) * limit;

            const { count, rows: products } = await Product.findAndCountAll({
                where: {
                    categoryId: categoryId,
                    isActive: true
                },
                include: [
                    {
                        model: Category,
                        attributes: ['id', 'name']
                    },
                    {
                        model: Store,
                        attributes: ['id', 'name', 'rating']
                    }
                ],
                limit: parseInt(limit),
                offset: parseInt(offset),
                order: [['createdAt', 'DESC']]
            });

            const formattedProducts = products.map(product => ({
                id: product.id,
                name: product.name,
                description: product.description,
                price: parseFloat(product.price),
                image: product.image ? `/uploads/${product.image}` : null,
                store: product.Store ? {
                    id: product.Store.id,
                    name: product.Store.name,
                    rating: product.Store.rating || 0
                } : null
            }));

            res.json({
                success: true,
                message: 'تم جلب منتجات الفئة بنجاح',
                data: {
                    products: formattedProducts,
                    category: products.length > 0 ? products[0].Category : null,
                    pagination: {
                        currentPage: parseInt(page),
                        totalPages: Math.ceil(count / limit),
                        totalItems: count,
                        itemsPerPage: parseInt(limit)
                    }
                }
            });

        } catch (error) {
            console.error('خطأ في جلب منتجات الفئة:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }
}

module.exports = new ProductApiController();
