'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // حذف address من Customer
    await queryInterface.removeColumn('Customers', 'address');

    // إضافة address إلى CustomerAreas
    await queryInterface.addColumn('CustomerAreas', 'address', {
      type: Sequelize.STRING,
      allowNull: true
    });
  },

  async down(queryInterface, Sequelize) {
    // إعادة address إلى Customer
    await queryInterface.addColumn('Customers', 'address', {
      type: Sequelize.STRING,
      allowNull: true
    });

    // حذف address من CustomerAreas
    await queryInterface.removeColumn('CustomerAreas', 'address');
  }
};
