<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Orders for <%= store.name %></h1>
        <a href="/admin/stores" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Stores
        </a>
    </div>

    <div class="table-responsive">
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>Order ID</th>
                    <th>Customer</th>
                    <th>Status</th>
                    <th>Total</th>
                    <th>Created At</th>
                </tr>
            </thead>
            <tbody>
                <% orders.forEach(order => { %>
                    <tr>
                        <td>
                            <a href="/admin/orders/<%= order.id %>">
                              <%= order.id %>
                            </a>
                        </td>                          
                        <td><%= order.customer ? order.customer.name : 'N/A' %></td>
                        <td><%= order.status %></td>
                        <td><%= order.totalPrice %></td>
                        <td><%= order.createdAt.toLocaleString() %></td>
                    </tr>
                <% }) %>
            </tbody>
        </table>
    </div>
    <% if (totalPages > 1) { %>
        <nav aria-label="Page navigation" class="d-flex justify-content-center mt-4">
          <ul class="pagination">
            <% if (currentPage > 1) { %>
              <li class="page-item">
                <a class="page-link" href="?page=<%= currentPage - 1 %>">السابق</a>
              </li>
            <% } else { %>
              <li class="page-item disabled">
                <span class="page-link">السابق</span>
              </li>
            <% } %>
        
            <% for(let i = 1; i <= totalPages; i++) { %>
              <li class="page-item <%= currentPage === i ? 'active' : '' %>">
                <a class="page-link" href="?page=<%= i %>"><%= i %></a>
              </li>
            <% } %>
        
            <% if (currentPage < totalPages) { %>
              <li class="page-item">
                <a class="page-link" href="?page=<%= currentPage + 1 %>">التالي</a>
              </li>
            <% } else { %>
              <li class="page-item disabled">
                <span class="page-link">التالي</span>
              </li>
            <% } %>
          </ul>
        </nav>
        <% } %>
    <% if (orders.length === 0) { %>
        <div class="alert alert-info">No orders found for this store.</div>
    <% } %>
</div>
