require('dotenv').config({ path: '../.env' });
const express = require('express');
const session = require('express-session');
const path = require('path');
const flash = require('connect-flash');
const expressLayouts = require('express-ejs-layouts');
const helmet = require('helmet');
const cors = require('cors');
const compression = require('compression');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
const { sequelize } = require('./models');
const logger = require('./utils/logger');
const errorHandler = require('./middleware/errorHandler');
const ensureTablesExist = require('./utils/dbTablesCheck');
const checkDatabaseHealth = require('./utils/dbHealthCheck');
const swaggerUi = require('swagger-ui-express');
const swaggerDocument = require('./config/swagger');
const routes = require('./routes');
const NotificationScheduler = require('./utils/notificationScheduler');

const app = express();

/** ======== Middlewares ======== **/

// Security headers
app.use(helmet());

// CORS setup
app.use(cors({
    origin: process.env.CORS_ORIGIN || '*',
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
    allowedHeaders: ['Content-Type', 'Authorization']
}));

// Rate limiting for /api
const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 mins
    max: 100,
    message: 'Too many requests from this IP, please try again later.'
});
app.use('/api/', limiter);

// Request timeout (30 seconds)
app.use((req, res, next) => {
    req.setTimeout(30000, () => {
        const err = new Error('Request timeout');
        err.status = 408;
        next(err);
    });
    next();
});

// Response compression
app.use(compression());

// HTTP request logger
app.use(morgan('combined', { stream: { write: msg => logger.info(msg.trim()) } }));

// Parse JSON and URL-encoded payloads
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Static files (adjust path if needed)
app.use(express.static(path.join(__dirname, '../public')));
app.use('/uploads', express.static(path.join(__dirname, '../public/uploads')));
/*
// File upload config
app.use(fileUpload({
    createParentPath: true,
    limits: { fileSize: 2 * 1024 * 1024 }, // 2MB
    abortOnLimit: true,
    useTempFiles: true,
    tempFileDir: '/tmp/'
}));
*/
// View engine setup (EJS + layouts)
app.use(expressLayouts);
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));
app.set('layout', 'layouts/main');

// Session config
app.use(session({
    secret: process.env.SESSION_SECRET || 'defaultSecret',
    resave: false,
    saveUninitialized: false,
    cookie: {
        secure: process.env.NODE_ENV === 'production',
        httpOnly: true,
        maxAge: 24 * 60 * 60 * 1000, // 24 hours
        sameSite: 'strict',
    },
    name: 'sessionId',
}));

// Flash messages
app.use(flash());

// Make session & flash available in all views
app.use((req, res, next) => {
    res.locals.session = req.session;
    res.locals.success = req.flash('success');
    res.locals.error = req.flash('error');
    next();
});

/** ======== Routes ======== **/

// API docs
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerDocument));

// Main routes loader
app.use('/', routes);

// Health check endpoint
app.get('/api/health', async (req, res) => {
    try {
        const dbHealth = await checkDatabaseHealth();

        const systemHealth = {
            status: dbHealth.healthy ? 'ok' : 'error',
            message: dbHealth.message,
            components: {
                database: {
                    status: dbHealth.healthy ? 'ok' : 'error',
                    message: dbHealth.message
                },
                server: {
                    status: 'ok',
                    uptime: process.uptime()
                }
            },
            timestamp: new Date().toISOString(),
            version: process.env.npm_package_version || 'unknown',
        };

        const statusCode = dbHealth.healthy ? 200 : 500;
        res.status(statusCode).json(systemHealth);
    } catch (error) {
        logger.error('Health check failed:', error);
        res.status(500).json({
            status: 'error',
            message: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

// 404 handler (should be after all routes)
app.use((req, res) => {
    res.status(404).render('error', {
        error: {
            status: 404,
            message: 'Page not found'
        }
    });
});

// Centralized error handler (last middleware)
app.use(errorHandler);

/** ======== Server startup ======== **/

const PORT = process.env.PORT || 3000;

const startServer = async () => {
    try {
        console.log('DB_USER:', process.env.DB_USER);
console.log('DB_PASSWORD:', process.env.DB_PASSWORD);
        await sequelize.authenticate();
        logger.info('Database connection established');

        await ensureTablesExist();
        logger.info('Database tables verified');

        // تفعيل مجدول الإشعارات
        NotificationScheduler.init();
        logger.info('Notification scheduler initialized');

        const server = app.listen(PORT, () => {
            logger.info(`Server running on port ${PORT} in ${process.env.NODE_ENV || 'development'} mode`);
        });

        // Graceful shutdown
        const shutdown = async () => {
            logger.info('Shutting down server...');
            server.close(async () => {
                try {
                    await sequelize.close();
                    logger.info('Database connections closed');
                    process.exit(0);
                } catch (err) {
                    logger.error('Error during shutdown:', err);
                    process.exit(1);
                }
            });
        };

        process.on('SIGTERM', shutdown);
        process.on('SIGINT', shutdown);

    } catch (error) {
        logger.error('Failed to start server:', error);
        process.exit(1);
    }
};

startServer();
