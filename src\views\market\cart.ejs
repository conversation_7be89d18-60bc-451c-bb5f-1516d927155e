<div class="row">
    <div class="col-md-8">
        <h2 class="mb-4">Shopping Cart</h2>
        
        <% if (Object.keys(storeGroups).length > 0) { %>
            <% Object.entries(storeGroups).forEach(([storeId, group]) => { %>
                <div class="card mb-4">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">
                            <i class="fas fa-store text-primary me-2"></i>
                            <%= group.storeName %>
                        </h5>
                    </div>
                    <div class="card-body">
                        <% group.items.forEach(item => { %>
                            <div class="row mb-4">
                                <div class="col-md-2">
                                    <% if (item.image) { %>
                                        <img src="<%= item.image %>" class="img-fluid rounded" alt="<%= item.name %>">
                                    <% } else { %>
                                        <div class="bg-light rounded d-flex align-items-center justify-content-center" style="height: 100px;">
                                            <i class="fas fa-box fa-2x text-muted"></i>
                                        </div>
                                    <% } %>
                                </div>
                                <div class="col-md-4">
                                    <h6 class="text-truncate"><%= item.name %></h6>
                                    <small class="text-muted">Unit Price: $<%= item.price %></small>
                                </div>
                                <div class="col-md-3">
                                    <div class="input-group">
                                        <button class="btn btn-outline-secondary" type="button" onclick="updateQuantity('<%= item.productId %>', -1)">-</button>
                                        <input type="number" class="form-control text-center" value="<%= item.quantity %>" min="1" onchange="updateQuantity('<%= item.productId %>', this.value)">
                                        <button class="btn btn-outline-secondary" type="button" onclick="updateQuantity('<%= item.productId %>', 1)">+</button>
                                    </div>
                                </div>
                                <div class="col-md-2 text-end">
                                    <strong>$<%= item.total.toFixed(2) %></strong>
                                </div>
                                <div class="col-md-1 text-end">
                                    <button class="btn btn-link text-danger" onclick="removeItem('<%= item.productId %>')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        <% }); %>
                        <div class="text-end border-top pt-3">
                            <strong>Subtotal: $<%= group.subtotal.toFixed(2) %></strong>
                        </div>
                    </div>
                </div>
            <% }); %>
        <% } else { %>
            <div class="alert alert-info">
                <i class="fas fa-shopping-cart me-2"></i>
                Your cart is empty. <a href="/products">Continue shopping</a>
            </div>
        <% } %>
    </div>

    <div class="col-md-4">
        <% if (Object.keys(storeGroups).length > 0) { %>
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Order Summary</h5>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between mb-2">
                            <span>Subtotal</span>
                            <strong>$<%= total.toFixed(2) %></strong>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Shipping</span>
                            <span class="text-success">Free</span>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between">
                            <span>Total</span>
                            <strong class="text-primary">$<%= total.toFixed(2) %></strong>
                        </div>
                    </div>
                    <form action="/checkout" method="POST">
                        <button type="submit" class="btn btn-primary w-100">
                            Proceed to Checkout
                        </button>
                    </form>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-body">
                    <h6 class="card-title">Have a coupon?</h6>
                    <form class="d-flex">
                        <input type="text" class="form-control me-2" placeholder="Enter code">
                        <button type="submit" class="btn btn-outline-primary">Apply</button>
                    </form>
                </div>
            </div>
        <% } %>
    </div>
</div>

<script>
function updateQuantity(productId, change) {
    fetch('/cart/update', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            productId,
            change
        })
    }).then(() => window.location.reload());
}

function removeItem(productId) {
    if (confirm('Are you sure you want to remove this item?')) {
        fetch('/cart/remove', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ productId })
        }).then(() => window.location.reload());
    }
}
</script> 