const express = require('express');
const router = express.Router();
const CustomersController = require('../../controllers/CustomersController');
const { authenticateCustomer, optionalAuth } = require('../../middleware/apiAuth');

/**
 * مسارات العملاء المحولة للموبايل (Flutter)
 */

// الصفحة الرئيسية (عامة - لا تحتاج مصادقة)
router.get('/home', optionalAuth, CustomersController.getHome);

// متاجر حسب الفئة (عامة)
router.get('/categories/:id/stores', optionalAuth, CustomersController.getStoresByCategory);

// منتجات متجر معين (عامة)
router.get('/stores/:id/products', optionalAuth, CustomersController.getStoreProducts);

// تفاصيل منتج (عامة)
router.get('/products/:id', optionalAuth, CustomersController.getProductDetails);

/**
 * مسارات السلة (تحتاج مصادقة)
 */

// عرض السلة
router.get('/cart', authenticateCustomer, CustomersController.getCart);

// إضافة للسلة
router.post('/cart/add', authenticateCustomer, CustomersController.addToCart);

// تحديث كمية في السلة
router.put('/cart/:id', authenticateCustomer, CustomersController.updateCartItem);

// حذف من السلة
router.delete('/cart/:id', authenticateCustomer, CustomersController.removeFromCart);

// مسح السلة بالكامل
router.delete('/cart', authenticateCustomer, CustomersController.clearCart);

/**
 * مسارات الدفع والطلبات (تحتاج مصادقة)
 */

// بيانات الدفع
router.get('/checkout', authenticateCustomer, CustomersController.getCheckoutData);

// معالجة الدفع وإنشاء الطلب
router.post('/checkout', authenticateCustomer, CustomersController.processCheckout);

// الحصول على طلبات العميل
router.get('/orders', authenticateCustomer, CustomersController.getOrders);

module.exports = router;
