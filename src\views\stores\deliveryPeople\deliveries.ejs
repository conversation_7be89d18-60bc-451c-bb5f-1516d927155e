<h3>Deliveries for Driver</h3>
<table class="table table-striped">
  <thead>
    <tr>
      <th>#</th>
      <th>Status</th>
      <th>Pickup Time</th>
      <th>Delivery Time</th>
      <th>Notes</th>
    </tr>
  </thead>
  <tbody>
    <% deliveries.forEach((d, i) => { %>
      <tr>
        <td><%= i + 1 %></td>
        <td><%= d.status %></td>
        <td><%= d.pickupTime ? new Date(d.pickupTime).toLocaleString() : '-' %></td>
        <td><%= d.deliveryTime ? new Date(d.deliveryTime).toLocaleString() : '-' %></td>
        <td><%= d.notes || '-' %></td>
      </tr>
    <% }) %>
  </tbody>
</table>
