document.addEventListener('DOMContentLoaded', function() {
    // Create loading overlay element
    const loadingOverlay = document.createElement('div');
    loadingOverlay.className = 'loading-overlay';
    loadingOverlay.innerHTML = '<div class="spinner"></div>';
    document.body.appendChild(loadingOverlay);
    
    // Show loading indicator for form submissions and links
    setupLoadingIndicators();
    
    // Setup automatic table row highlighting
    setupTableRowHighlighting();
    
    // Auto-dismiss alerts after 5 seconds
    setupAutoDismissAlerts();
    
    // Add current year to footer copyright if exists
    updateCopyrightYear();
});

// Show loading indicator for form submissions and navigations
function setupLoadingIndicators() {
    const loadingOverlay = document.querySelector('.loading-overlay');
    
    // For forms
    document.querySelectorAll('form').forEach(form => {
        // Skip forms with "no-loader" class
        if (form.classList.contains('no-loader')) return;
        
        form.addEventListener('submit', function() {
            // Don't show for forms that have a "method" attribute of "get"
            if (this.method && this.method.toLowerCase() === 'get') return;
            
            // Don't show loading for forms with data validation errors
            if (!this.checkValidity()) return;
            
            loadingOverlay.classList.add('show');
        });
    });
    
    // For links that aren't just anchors, download links, or external links
    document.querySelectorAll('a').forEach(link => {
        // Skip links with "no-loader" class
        if (link.classList.contains('no-loader')) return;
        
        // Skip links that open in a new tab/window
        if (link.target === '_blank') return;
        
        // Skip anchor links (that start with #)
        if (link.getAttribute('href')?.startsWith('#')) return;
        
        // Skip download links
        if (link.hasAttribute('download')) return;
        
        // Skip external links
        if (link.hostname !== window.location.hostname) return;
        
        link.addEventListener('click', function(e) {
            // Don't show loading for links with modifier keys pressed (ctrl, shift, etc.)
            if (e.ctrlKey || e.shiftKey || e.metaKey || e.altKey) return;
            
            loadingOverlay.classList.add('show');
        });
    });
    
    // Always hide loading indicator when navigating away
    window.addEventListener('beforeunload', function() {
        loadingOverlay.classList.remove('show');
    });
}

// Highlight table rows on hover
function setupTableRowHighlighting() {
    document.querySelectorAll('table tbody tr').forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.backgroundColor = '#f5f5f5';
        });
        
        row.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
        });
    });
}

// Auto-dismiss alerts after 5 seconds
function setupAutoDismissAlerts() {
    document.querySelectorAll('.alert').forEach(alert => {
        // Skip alerts with "no-auto-dismiss" class
        if (alert.classList.contains('no-auto-dismiss')) return;
        
        setTimeout(() => {
            const closeButton = alert.querySelector('.btn-close');
            if (closeButton) {
                closeButton.click();
            } else {
                alert.style.opacity = '0';
                setTimeout(() => {
                    alert.style.display = 'none';
                }, 300);
            }
        }, 5000);
    });
}

// Update footer copyright year
function updateCopyrightYear() {
    const copyrightElement = document.querySelector('.copyright-year');
    if (copyrightElement) {
        copyrightElement.textContent = new Date().getFullYear();
    }
} 