<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Products</h1>
        <a href="/products/create" class="btn btn-primary">Add New Product</a>
    </div>

    <% if (locals.message) { %>
        <div class="alert alert-info"><%= message %></div>
    <% } %>

    <% if (products && products.length > 0) { %>
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Name</th>
                        <th>Price</th>
                        <th>Store</th>
                        <th>Images</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <% products.forEach(product => { %>
                        <tr>
                            <td><%= product.id %></td>
                            <td><%= product.name || 'Unnamed Product' %></td>
                            <td>$<%= (product.price || 0).toFixed(2) %></td>
                            <td><%= product.store ? product.store.name : 'N/A' %></td>
                            <td>
                                <% if (product.images && product.images.length > 0 && product.images[0].path) { %>
                                    <img src="<%= product.images[0].path %>" alt="<%= product.name %>" style="width: 50px; height: 50px; object-fit: cover;">
                                <% } else { %>
                                    No Image
                                <% } %>
                            </td>
                            <td class="action-buttons">
                                <a href="/products/<%= product.id %>/edit" class="btn btn-sm btn-warning">
                                    <i class="fas fa-edit"></i> Edit
                                </a>
                                <form action="/products/<%= product.id %>/delete" method="POST" class="d-inline">
                                    <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure?')">
                                        <i class="fas fa-trash"></i> Delete
                                    </button>
                                </form>
                            </td>
                        </tr>
                    <% }); %>
                </tbody>
            </table>
        </div>
    <% } else { %>
        <div class="alert alert-info">No products found.</div>
    <% } %>
</div> 