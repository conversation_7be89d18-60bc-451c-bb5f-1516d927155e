<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Orders</h1>
        <a href="/orders/create" class="btn btn-primary">Create New Order</a>
    </div>

    <% if (locals.message) { %>
        <div class="alert alert-info"><%= message %></div>
    <% } %>

    <% if (orders && orders.length > 0) { %>
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Customer</th>
                        <th>Store</th>
                        <th>Total Price</th>
                        <th>Status</th>
                        <th>Created At</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <% orders.forEach(order => { %>
                        <tr>
                            <td><%= order.id %></td>
                            <td><%= order.customer ? order.customer.name : 'N/A' %></td>
                            <td><%= order.store ? order.store.name : 'N/A' %></td>
                            <td>$<%= (order.totalPrice || 0).toFixed(2) %></td>
                            <td>
                                <span class="badge bg-<%= !order.status ? 'secondary' :
                                                        order.status === 'pending' ? 'warning' : 
                                                        order.status === 'completed' ? 'success' : 
                                                        order.status === 'cancelled' ? 'danger' : 'secondary' %>">
                                    <%= order.status ? (order.status.charAt(0).toUpperCase() + order.status.slice(1)) : 'Unknown' %>
                                </span>
                            </td>
                            <td><%= order.createdAt ? new Date(order.createdAt).toLocaleDateString() : 'N/A' %></td>
                            <td class="action-buttons">
                                <a href="/orders/<%= order.id %>" class="btn btn-sm btn-info">
                                    <i class="fas fa-eye"></i> View
                                </a>
                                <form action="/orders/<%= order.id %>/delete" method="POST" class="d-inline">
                                    <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this order?')">
                                        <i class="fas fa-trash"></i> Delete
                                    </button>
                                </form>
                            </td>
                        </tr>
                    <% }); %>
                </tbody>
            </table>
        </div>
    <% } else { %>
        <div class="alert alert-info">No orders found.</div>
    <% } %>
</div> 