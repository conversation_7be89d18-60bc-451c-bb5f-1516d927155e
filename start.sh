#!/bin/bash

# نظام إدارة المتاجر الذكي - سكريبت التشغيل السريع

echo "🏪 مرحباً بك في نظام إدارة المتاجر الذكي"
echo "================================================"

# التحقق من وجود Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js غير مثبت. يرجى تثبيت Node.js أولاً"
    exit 1
fi

# التحقق من وجود npm
if ! command -v npm &> /dev/null; then
    echo "❌ npm غير مثبت. يرجى تثبيت npm أولاً"
    exit 1
fi

# التحقق من إصدار Node.js
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 16 ]; then
    echo "❌ يتطلب Node.js الإصدار 16 أو أحدث. الإصدار الحالي: $(node -v)"
    exit 1
fi

echo "✅ Node.js $(node -v) مثبت بنجاح"

# التحقق من وجود ملف .env
if [ ! -f .env ]; then
    echo "⚠️  ملف .env غير موجود. سيتم إنشاؤه من .env.example"
    if [ -f .env.example ]; then
        cp .env.example .env
        echo "✅ تم إنشاء ملف .env من .env.example"
        echo "📝 يرجى تعديل ملف .env بالإعدادات المناسبة"
    else
        echo "❌ ملف .env.example غير موجود"
        exit 1
    fi
fi

# تثبيت التبعيات
echo "📦 جاري تثبيت التبعيات..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ فشل في تثبيت التبعيات"
    exit 1
fi

echo "✅ تم تثبيت التبعيات بنجاح"

# التحقق من قاعدة البيانات
echo "🗄️  التحقق من قاعدة البيانات..."

# قراءة إعدادات قاعدة البيانات من .env
source .env

# التحقق من وجود MySQL
if command -v mysql &> /dev/null; then
    echo "✅ MySQL مثبت"
    
    # محاولة الاتصال بقاعدة البيانات
    mysql -h"$DB_HOST" -u"$DB_USER" -p"$DB_PASS" -e "USE $DB_NAME;" 2>/dev/null
    
    if [ $? -ne 0 ]; then
        echo "⚠️  قاعدة البيانات غير موجودة. سيتم إنشاؤها..."
        mysql -h"$DB_HOST" -u"$DB_USER" -p"$DB_PASS" -e "CREATE DATABASE IF NOT EXISTS $DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
        
        if [ $? -eq 0 ]; then
            echo "✅ تم إنشاء قاعدة البيانات بنجاح"
        else
            echo "❌ فشل في إنشاء قاعدة البيانات"
            echo "يرجى التحقق من إعدادات قاعدة البيانات في ملف .env"
        fi
    else
        echo "✅ قاعدة البيانات متصلة بنجاح"
    fi
else
    echo "⚠️  MySQL غير مثبت أو غير متاح في PATH"
    echo "يرجى التأكد من تثبيت MySQL وتشغيله"
fi

# إنشاء المجلدات المطلوبة
echo "📁 إنشاء المجلدات المطلوبة..."

mkdir -p uploads
mkdir -p logs
mkdir -p backups
mkdir -p temp

echo "✅ تم إنشاء المجلدات بنجاح"

# تشغيل التطبيق
echo "🚀 جاري تشغيل التطبيق..."
echo "================================================"
echo "🌐 سيتم تشغيل التطبيق على: http://localhost:${PORT:-3001}"
echo "📊 لوحة تحكم الإدارة: http://localhost:${PORT:-3001}/admin"
echo "🏪 واجهة المتاجر: http://localhost:${PORT:-3001}/store"
echo "👤 واجهة العملاء: http://localhost:${PORT:-3001}/customers"
echo "================================================"
echo "للإيقاف اضغط Ctrl+C"
echo ""

# تشغيل التطبيق في وضع التطوير
if [ "$NODE_ENV" = "production" ]; then
    echo "🔧 تشغيل في وضع الإنتاج..."
    npm start
else
    echo "🔧 تشغيل في وضع التطوير..."
    npm run dev
fi
