const { Customer } = require('../models');

class AuthController {
    // عرض نموذج تسجيل الدخول
    async showLogin(req, res) {
        res.render('customers/auth/login');
    }

    // عرض نموذج التسجيل
    async showRegister(req, res) {
        res.render('customers/auth/register');
    }

    // معالجة تسجيل الدخول
    async login(req, res) {
        try {
            const { barcode, password } = req.body;
            const customer = await Customer.findOne({ where: { barcode } });

            if (!customer) {
                return res.render('customers/auth/login', {
                    error: 'Invalid barcode or password'
                });
            }

            const isValidPassword = await customer.validatePassword(password);
            if (!isValidPassword) {
                return res.render('customers/auth/login', {
                    error: 'Invalid barcode or password'
                });
            }

            if (customer.status !== 'active') {
                return res.render('customers/auth/login', {
                    error: 'Your account is not active. Please contact support.'
                });
            }

            // تعيين بيانات الجلسة
            req.session.customerId = customer.id;
            req.session.customerName = customer.name;

            req.flash('success', 'Welcome back, ' + customer.name);
            res.redirect('/customers/home');
        } catch (error) {
            console.error('Login error:', error);
            res.render('customers/auth/login', {
                error: 'An error occurred during login'
            });
        }
    }

    // معالجة التسجيل
    async register(req, res) {
        try {
            const { name, barcode, password, phoneNumber } = req.body;

            // التحقق من وجود باركود مسبقاً
            const existingCustomer = await Customer.findOne({ where: { barcode } });
            if (existingCustomer) {
                return res.render('customers/auth/register', {
                    error: 'Barcode already exists'
                });
            }

            // إنشاء عميل جديد
            await Customer.create({
                name,
                barcode,
                password,
                phoneNumber,
                status: 'pending'
            });

            req.flash('success', 'Registration successful! Please wait for account activation.');
            res.redirect('/customers/login');
        } catch (error) {
            console.error('Registration error:', error);
            res.render('customers/auth/register', {
                error: 'An error occurred during registration'
            });
        }
    }

    // تسجيل الخروج
    async logout(req, res) {
        req.session.destroy(err => {
            if (err) {
                console.error('Logout error:', err);
            }
            res.redirect('/customers/login');
        });
    }
}

module.exports = new AuthController();
