const { Customer } = require('../models');
const { generateAuthTokens } = require('../utils/jwt');

class AuthController {
    // عرض نموذج تسجيل الدخول
    async showLogin(req, res) {
        res.render('customers/auth/login');
    }

    // عرض نموذج التسجيل
    async showRegister(req, res) {
        res.render('customers/auth/register');
    }

    // معالجة تسجيل الدخول
    async login(req, res) {
        try {
            const { barcode, password } = req.body;
            const customer = await Customer.findOne({ where: { barcode } });

            if (!customer) {
                return res.render('customers/auth/login', {
                    error: 'Invalid barcode or password'
                });
            }

            const isValidPassword = await customer.validatePassword(password);
            if (!isValidPassword) {
                return res.render('customers/auth/login', {
                    error: 'Invalid barcode or password'
                });
            }

            if (customer.status !== 'active') {
                return res.render('customers/auth/login', {
                    error: 'Your account is not active. Please contact support.'
                });
            }

            // إنشاء JWT tokens
            const tokens = generateAuthTokens(customer, 'customer');

            // تعيين الـ token في الـ cookies
            res.cookie('token', tokens.accessToken, {
                httpOnly: true,
                secure: process.env.NODE_ENV === 'production',
                maxAge: 24 * 60 * 60 * 1000, // 24 hours
                sameSite: 'strict'
            });

            res.cookie('refreshToken', tokens.refreshToken, {
                httpOnly: true,
                secure: process.env.NODE_ENV === 'production',
                maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
                sameSite: 'strict'
            });

            res.redirect('/customers/home?success=' + encodeURIComponent('Welcome back, ' + customer.name));
        } catch (error) {
            console.error('Login error:', error);
            res.render('customers/auth/login', {
                error: 'An error occurred during login'
            });
        }
    }

    // معالجة التسجيل
    async register(req, res) {
        try {
            const { name, barcode, password, phoneNumber } = req.body;

            // التحقق من وجود باركود مسبقاً
            const existingCustomer = await Customer.findOne({ where: { barcode } });
            if (existingCustomer) {
                return res.render('customers/auth/register', {
                    error: 'Barcode already exists'
                });
            }

            // إنشاء عميل جديد
            await Customer.create({
                name,
                barcode,
                password,
                phoneNumber,
                status: 'pending'
            });

            res.redirect('/customers/login?success=' + encodeURIComponent('Registration successful! Please wait for account activation.'));
        } catch (error) {
            console.error('Registration error:', error);
            res.render('customers/auth/register', {
                error: 'An error occurred during registration'
            });
        }
    }

    // تسجيل الخروج
    async logout(req, res) {
        // مسح الـ cookies
        res.clearCookie('token');
        res.clearCookie('refreshToken');
        res.clearCookie('cart');

        res.redirect('/customers/login?success=' + encodeURIComponent('Logged out successfully'));
    }
}

module.exports = new AuthController();
