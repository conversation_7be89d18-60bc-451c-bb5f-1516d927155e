<div class="row">
    <!-- Filters Sidebar -->
    <div class="col-md-3">
        <div class="card mb-4">
            <div class="card-body">
                <h5 class="card-title">Filters</h5>
                <form action="/products" method="GET">
                    <!-- Category Filter -->
                    <div class="mb-3">
                        <label class="form-label">Category</label>
                        <select name="category" class="form-select">
                            <option value="">All Categories</option>
                            <option value="electronics" <%= filters.category === 'electronics' ? 'selected' : '' %>>Electronics</option>
                            <option value="fashion" <%= filters.category === 'fashion' ? 'selected' : '' %>>Fashion</option>
                            <option value="home" <%= filters.category === 'home' ? 'selected' : '' %>>Home & Living</option>
                            <option value="books" <%= filters.category === 'books' ? 'selected' : '' %>>Books</option>
                            <option value="sports" <%= filters.category === 'sports' ? 'selected' : '' %>>Sports</option>
                        </select>
                    </div>

                    <!-- Price Range Filter -->
                    <div class="mb-3">
                        <label class="form-label">Price Range</label>
                        <div class="row g-2">
                            <div class="col">
                                <input type="number" name="minPrice" class="form-control" placeholder="Min" value="<%= filters.minPrice || '' %>">
                            </div>
                            <div class="col">
                                <input type="number" name="maxPrice" class="form-control" placeholder="Max" value="<%= filters.maxPrice || '' %>">
                            </div>
                        </div>
                    </div>

                    <!-- Store Filter -->
                    <div class="mb-3">
                        <label class="form-label">Store</label>
                        <select name="store" class="form-select">
                            <option value="">All Stores</option>
                            <% if (typeof stores !== 'undefined') { %>
                                <% stores.forEach(store => { %>
                                    <option value="<%= store.id %>" <%= filters.store == store.id ? 'selected' : '' %>><%= store.name %></option>
                                <% }); %>
                            <% } %>
                        </select>
                    </div>

                    <button type="submit" class="btn btn-primary w-100">Apply Filters</button>
                </form>
            </div>
        </div>
    </div>

    <!-- Products Grid -->
    <div class="col-md-9">
        <!-- Sort Options -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h4 class="mb-0">Products</h4>
                <small class="text-muted"><%= count %> products found</small>
            </div>
            <div class="d-flex align-items-center">
                <label class="me-2">Sort by:</label>
                <select class="form-select form-select-sm" style="width: auto;" onchange="window.location.href=this.value">
                    <option value="/products?sort=newest" <%= filters.sort === 'newest' ? 'selected' : '' %>>Newest</option>
                    <option value="/products?sort=price-asc" <%= filters.sort === 'price-asc' ? 'selected' : '' %>>Price: Low to High</option>
                    <option value="/products?sort=price-desc" <%= filters.sort === 'price-desc' ? 'selected' : '' %>>Price: High to Low</option>
                    <option value="/products?sort=name" <%= filters.sort === 'name' ? 'selected' : '' %>>Name</option>
                </select>
            </div>
        </div>

        <!-- Products Grid -->
        <div class="row">
            <% if (products.length > 0) { %>
                <% products.forEach(product => { %>
                    <div class="col-md-4 mb-4">
                        <div class="card h-100">
                            <% if (product.images && product.images.length > 0) { %>
                                <img src="<%= product.images[0].path %>" class="card-img-top" alt="<%= product.name %>">
                            <% } else { %>
                                <img src="/images/placeholder.jpg" class="card-img-top" alt="No image">
                            <% } %>
                            <div class="card-body">
                                <h5 class="card-title text-truncate"><%= product.name %></h5>
                                <p class="card-text text-truncate"><%= product.description %></p>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <% if (product.discountPercentage > 0) { %>
                                        <div>
                                            <span class="text-decoration-line-through text-muted">$<%= product.price %></span>
                                            <span class="text-danger fw-bold">$<%= product.getFinalPrice() %></span>
                                        </div>
                                        <span class="badge bg-danger">-<%= product.discountPercentage %>%</span>
                                    <% } else { %>
                                        <span class="fw-bold">$<%= product.price %></span>
                                    <% } %>
                                </div>
                                <small class="text-muted d-block mb-2">By <%= product.store.name %></small>
                                <div class="d-grid gap-2">
                                    <a href="/products/<%= product.id %>" class="btn btn-primary">View Details</a>
                                    <form action="/cart/add" method="POST">
                                        <input type="hidden" name="productId" value="<%= product.id %>">
                                        <input type="hidden" name="quantity" value="1">
                                        <button type="submit" class="btn btn-outline-primary w-100">Add to Cart</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                <% }); %>
            <% } else { %>
                <div class="col">
                    <div class="alert alert-info">
                        No products found matching your criteria.
                    </div>
                </div>
            <% } %>
        </div>

        <!-- Pagination -->
        <% if (totalPages > 1) { %>
            <nav aria-label="Page navigation" class="mt-4">
                <ul class="pagination justify-content-center">
                    <li class="page-item <%= currentPage == 1 ? 'disabled' : '' %>">
                        <a class="page-link" href="/products?page=<%= currentPage - 1 %>&<%= new URLSearchParams(filters).toString() %>">Previous</a>
                    </li>
                    <% for(let i = 1; i <= totalPages; i++) { %>
                        <li class="page-item <%= currentPage == i ? 'active' : '' %>">
                            <a class="page-link" href="/products?page=<%= i %>&<%= new URLSearchParams(filters).toString() %>"><%= i %></a>
                        </li>
                    <% } %>
                    <li class="page-item <%= currentPage == totalPages ? 'disabled' : '' %>">
                        <a class="page-link" href="/products?page=<%= parseInt(currentPage) + 1 %>&<%= new URLSearchParams(filters).toString() %>">Next</a>
                    </li>
                </ul>
            </nav>
        <% } %>
    </div>
</div> 