<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="text-center">Register Store</h3>
                </div>
                <div class="card-body">
                    <% if (typeof error !== 'undefined') { %>
                        <div class="alert alert-danger"><%= error %></div>
                    <% } %>
                    <form action="/store/register" method="POST">
                        <div class="mb-3">
                            <label for="userName" class="form-label">Username</label>
                            <input type="text" class="form-control" id="userName" name="userName" required>
                        </div>
                        <div class="mb-3">
                            <label for="password" class="form-label">Password</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>
                        <div class="mb-3">
                            <label for="areaId" class="form-label">Area</label>
                            <select class="form-control" id="areaId" name="areaId" required>
                                <option value="">Select an Area</option>
                                <% areas.forEach(area => { %>
                                    <option value="<%= area.id %>"><%= area.name %></option>
                                <% }); %>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="phoneNumber" class="form-label">Phone Number</label>
                            <input type="tel" class="form-control" id="phoneNumber" name="phoneNumber">
                        </div>
                        <div class="mb-3">
                            <label for="address" class="form-label">Address</label>
                            <textarea class="form-control" id="address" name="address" rows="3"></textarea>
                        </div>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">Register Store</button>
                        </div>
                    </form>
                    <div class="text-center mt-3">
                        <p>Already have a store account? <a href="/store/login">Login here</a></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div> 