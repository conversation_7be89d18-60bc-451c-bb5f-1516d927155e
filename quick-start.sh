#!/bin/bash

echo "🚀 تشغيل سريع لنظام إدارة المتاجر الذكي"
echo "========================================"

echo "📦 تثبيت التبعيات..."
npm install

echo "🗄️ إعداد قاعدة البيانات..."
if [ ! -f .env ]; then
    echo "⚠️ إنشاء ملف .env..."
    cp .env.example .env 2>/dev/null || echo "ملف .env.example غير موجود"
fi

echo "📁 إنشاء المجلدات..."
mkdir -p uploads logs

echo "🌐 تشغيل التطبيق..."
echo "========================================"
echo "🔗 الرابط: http://localhost:3001"
echo "📊 لوحة الإدارة: http://localhost:3001/admin/auth/login"
echo "🏪 المتاجر: http://localhost:3001/store/auth/login"
echo "👤 العملاء: http://localhost:3001/customers/auth/login"
echo "========================================"

cd src
node app.js
