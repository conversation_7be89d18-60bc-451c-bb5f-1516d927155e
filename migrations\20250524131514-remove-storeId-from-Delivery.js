'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('Deliveries', 'storeId');
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('Deliveries', 'storeId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'Stores',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL'
    });
  }
};
