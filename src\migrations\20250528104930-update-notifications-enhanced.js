'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    // التحقق من وجود الأعمدة قبل إضافتها
    const tableDescription = await queryInterface.describeTable('Notifications');

    // إضافة عمود title إذا لم يكن موجود
    if (!tableDescription.title) {
      await queryInterface.addColumn('Notifications', 'title', {
        type: Sequelize.STRING,
        allowNull: true
      });

      // تحديث البيانات الموجودة
      await queryInterface.sequelize.query(`
        UPDATE Notifications
        SET title = CASE
          WHEN type = 'order' THEN 'طلب جديد'
          WHEN type = 'promotion' THEN 'عرض خاص'
          WHEN type = 'system' THEN 'إشعار النظام'
          ELSE 'إشعار'
        END
        WHERE title IS NULL
      `);

      // جعل العمود مطلوب
      await queryInterface.changeColumn('Notifications', 'title', {
        type: Sequelize.STRING,
        allowNull: false
      });
    }

    // إضافة عمود priority إذا لم يكن موجود
    if (!tableDescription.priority) {
      await queryInterface.addColumn('Notifications', 'priority', {
        type: Sequelize.STRING,
        allowNull: false,
        defaultValue: 'normal'
      });
    }

    // إضافة عمود adminId إذا لم يكن موجود
    if (!tableDescription.adminId) {
      await queryInterface.addColumn('Notifications', 'adminId', {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'Admins',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      });
    }

    // إضافة عمود expiresAt إذا لم يكن موجود
    if (!tableDescription.expiresAt) {
      await queryInterface.addColumn('Notifications', 'expiresAt', {
        type: Sequelize.DATE,
        allowNull: true
      });
    }

    // إضافة عمود actionUrl إذا لم يكن موجود
    if (!tableDescription.actionUrl) {
      await queryInterface.addColumn('Notifications', 'actionUrl', {
        type: Sequelize.STRING,
        allowNull: true
      });
    }

    // إضافة عمود actionText إذا لم يكن موجود
    if (!tableDescription.actionText) {
      await queryInterface.addColumn('Notifications', 'actionText', {
        type: Sequelize.STRING,
        allowNull: true
      });
    }

    // تحديث المراجع الخارجية لتكون nullable
    await queryInterface.changeColumn('Notifications', 'customerId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'Customers',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL'
    });

    await queryInterface.changeColumn('Notifications', 'storeId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'Stores',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL'
    });

    // إضافة الفهارس
    try {
      await queryInterface.addIndex('Notifications', ['customerId', 'readAt'], {
        name: 'idx_notifications_customer_read'
      });
    } catch (error) {
      // تجاهل الخطأ إذا كان الفهرس موجود
    }

    try {
      await queryInterface.addIndex('Notifications', ['storeId', 'readAt'], {
        name: 'idx_notifications_store_read'
      });
    } catch (error) {
      // تجاهل الخطأ إذا كان الفهرس موجود
    }

    try {
      await queryInterface.addIndex('Notifications', ['adminId', 'readAt'], {
        name: 'idx_notifications_admin_read'
      });
    } catch (error) {
      // تجاهل الخطأ إذا كان الفهرس موجود
    }

    try {
      await queryInterface.addIndex('Notifications', ['type'], {
        name: 'idx_notifications_type'
      });
    } catch (error) {
      // تجاهل الخطأ إذا كان الفهرس موجود
    }

    try {
      await queryInterface.addIndex('Notifications', ['priority'], {
        name: 'idx_notifications_priority'
      });
    } catch (error) {
      // تجاهل الخطأ إذا كان الفهرس موجود
    }

    try {
      await queryInterface.addIndex('Notifications', ['createdAt'], {
        name: 'idx_notifications_created'
      });
    } catch (error) {
      // تجاهل الخطأ إذا كان الفهرس موجود
    }
  },

  async down (queryInterface, Sequelize) {
    // إزالة الفهارس
    try {
      await queryInterface.removeIndex('Notifications', 'idx_notifications_customer_read');
    } catch (error) {}

    try {
      await queryInterface.removeIndex('Notifications', 'idx_notifications_store_read');
    } catch (error) {}

    try {
      await queryInterface.removeIndex('Notifications', 'idx_notifications_admin_read');
    } catch (error) {}

    try {
      await queryInterface.removeIndex('Notifications', 'idx_notifications_type');
    } catch (error) {}

    try {
      await queryInterface.removeIndex('Notifications', 'idx_notifications_priority');
    } catch (error) {}

    try {
      await queryInterface.removeIndex('Notifications', 'idx_notifications_created');
    } catch (error) {}

    // إزالة الأعمدة الجديدة
    const tableDescription = await queryInterface.describeTable('Notifications');

    if (tableDescription.title) {
      await queryInterface.removeColumn('Notifications', 'title');
    }

    if (tableDescription.priority) {
      await queryInterface.removeColumn('Notifications', 'priority');
    }

    if (tableDescription.adminId) {
      await queryInterface.removeColumn('Notifications', 'adminId');
    }

    if (tableDescription.expiresAt) {
      await queryInterface.removeColumn('Notifications', 'expiresAt');
    }

    if (tableDescription.actionUrl) {
      await queryInterface.removeColumn('Notifications', 'actionUrl');
    }

    if (tableDescription.actionText) {
      await queryInterface.removeColumn('Notifications', 'actionText');
    }

    // إعادة المراجع الخارجية إلى مطلوبة
    await queryInterface.changeColumn('Notifications', 'customerId', {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: 'Customers',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE'
    });

    await queryInterface.changeColumn('Notifications', 'storeId', {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: 'Stores',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE'
    });
  }
};
