// models/StoreCategories.js
'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class StoreCategories extends Model {
    static associate(models) {
      // لا نحتاج لتعريف علاقات هنا إذا عرفناها على الموديلين الرئيسيين
    }
  }

  StoreCategories.init({
    storeId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Stores',
        key: 'id'
      },
      onDelete: 'CASCADE'
    },
    categoryId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Categories',
        key: 'id'
      },
      onDelete: 'CASCADE'
    }
  }, {
    sequelize,
    modelName: 'StoreCategories',
    tableName: 'StoreCategories',
    timestamps: true
  });

  return StoreCategories;
};
