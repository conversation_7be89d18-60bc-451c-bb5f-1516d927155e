version: '3.8'

services:
  # تطبيق Node.js
  app:
    build: .
    container_name: smart-store-app
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DB_HOST=mysql
      - DB_NAME=smart_store_system
      - DB_USER=root
      - DB_PASS=your_secure_password
      - DB_PORT=3306
      - REDIS_URL=redis://redis:6379
      - SESSION_SECRET=your-super-secret-session-key-change-this
      - JWT_SECRET=your-super-secret-jwt-key-change-this
    depends_on:
      - mysql
      - redis
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    networks:
      - smart-store-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # قاعدة بيانات MySQL
  mysql:
    image: mysql:8.0
    container_name: smart-store-mysql
    restart: unless-stopped
    environment:
      - MYSQL_ROOT_PASSWORD=your_secure_password
      - MYSQL_DATABASE=smart_store_system
      - MYSQL_USER=app_user
      - MYSQL_PASSWORD=app_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - smart-store-network
    command: --default-authentication-plugin=mysql_native_password

  # Redis للتخزين المؤقت والجلسات
  redis:
    image: redis:7-alpine
    container_name: smart-store-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - smart-store-network
    command: redis-server --appendonly yes

  # Nginx كخادم ويب عكسي
  nginx:
    image: nginx:alpine
    container_name: smart-store-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./uploads:/var/www/uploads
    depends_on:
      - app
    networks:
      - smart-store-network

  # phpMyAdmin لإدارة قاعدة البيانات
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: smart-store-phpmyadmin
    restart: unless-stopped
    environment:
      - PMA_HOST=mysql
      - PMA_PORT=3306
      - PMA_USER=root
      - PMA_PASSWORD=your_secure_password
    ports:
      - "8080:80"
    depends_on:
      - mysql
    networks:
      - smart-store-network

  # Redis Commander لإدارة Redis
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: smart-store-redis-commander
    restart: unless-stopped
    environment:
      - REDIS_HOSTS=local:redis:6379
    ports:
      - "8081:8081"
    depends_on:
      - redis
    networks:
      - smart-store-network

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local

networks:
  smart-store-network:
    driver: bridge
