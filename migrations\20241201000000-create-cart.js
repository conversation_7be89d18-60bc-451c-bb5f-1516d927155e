'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('Carts', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      customerId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'Customers',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      productId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'Products',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      quantity: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 1,
        validate: {
          min: 1
        }
      },
      price: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false,
        comment: 'سعر المنتج وقت الإضافة للسلة'
      },
      totalPrice: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false,
        comment: 'السعر الإجمالي (الكمية × السعر)'
      },
      sessionId: {
        type: Sequelize.STRING,
        allowNull: true,
        comment: 'معرف الجلسة للزوار غير المسجلين'
      },
      isActive: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true
      },
      expiresAt: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: 'تاريخ انتهاء صلاحية عنصر السلة'
      },
      notes: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: 'ملاحظات خاصة بالمنتج'
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // إضافة فهارس لتحسين الأداء
    await queryInterface.addIndex('Carts', ['customerId'], {
      name: 'idx_carts_customer_id'
    });

    await queryInterface.addIndex('Carts', ['productId'], {
      name: 'idx_carts_product_id'
    });

    await queryInterface.addIndex('Carts', ['sessionId'], {
      name: 'idx_carts_session_id'
    });

    await queryInterface.addIndex('Carts', ['customerId', 'productId'], {
      name: 'idx_carts_customer_product',
      unique: true
    });

    await queryInterface.addIndex('Carts', ['isActive'], {
      name: 'idx_carts_is_active'
    });

    await queryInterface.addIndex('Carts', ['expiresAt'], {
      name: 'idx_carts_expires_at'
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('Carts');
  }
};
