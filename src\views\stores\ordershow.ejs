<h2>Order Details (Order #<%= order.id %>)</h2>
<p><strong>Status:</strong> <%= order.status %></p>
<p><strong>Total:</strong> <%= order.totalPrice %></p>
<p><strong>Customer:</strong> <%= order.customer ? order.customer.name : 'N/A' %></p>

<h3>Products:</h3>
<table class="table">
  <thead>
    <tr>
      <th>Product</th>
      <th>Quantity</th>
      <th>Total Price</th>
    </tr>
  </thead>
  <tbody>
    <% order.orderDetails.forEach(detail => { %>
      <tr>
        <td><%= detail.product ? detail.product.name : 'N/A' %></td>
        <td><%= detail.quantity %></td>
        <td><%= detail.totalPrice %></td>
      </tr>
    <% }); %>
  </tbody>
</table>
