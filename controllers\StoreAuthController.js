const { Store, Area, Product, Order, DeliveryPerson , OrderDetail, Customer, Image, Delivery} = require('../models');
const bcrypt = require('bcryptjs');
const Joi = require('joi');
const { generateAuthTokens } = require('../utils/jwt');
const { buildSearchAndFilter, buildSortOptions, buildPaginationOptions, calculatePaginationInfo, sanitizeFilters } = require('../utils/searchFilter');

class StoreAuthController {
    // عرض نموذج تسجيل الدخول
    async showLogin(req, res) {
        res.render('stores/auth/login');
    }

    // عرض نموذج التسجيل مع جلب المناطق
    async showRegister(req, res) {
        try {
            const areas = await Area.findAll();
            res.render('stores/auth/register', { areas });
        } catch (error) {
            res.status(500).render('error', { error });
        }
    }

    // التحقق من صحة بيانات تسجيل الدخول باستخدام Joi
    async validateLogin(data) {
        const schema = Joi.object({
            userName: Joi.string().required(),
            password: Joi.string().required()
        });
        return schema.validateAsync(data);
    }

    // تسجيل الدخول
    async login(req, res) {
        try {
            console.log(req.body);
            await this.validateLogin(req.body);

            const { userName, password } = req.body;
            const store = await Store.findOne({ where: { userName } });

            if (!store) {
                return res.render('stores/auth/login', {
                    error: 'Invalid username or password'
                });
            }

            const isValidPassword = await bcrypt.compare(password, store.password);
            if (!isValidPassword) {
                return res.render('stores/auth/login', {
                    error: 'Invalid username or password'
                });
            }

            if (store.status !== 'active') {
                return res.render('stores/auth/login', {
                    error: 'Your store account is not active. Please contact support.'
                });
            }

            // إنشاء JWT tokens
            const tokens = generateAuthTokens(store, 'store');

            // تعيين الـ token في الـ cookies
            res.cookie('token', tokens.accessToken, {
                httpOnly: true,
                secure: process.env.NODE_ENV === 'production',
                maxAge: 24 * 60 * 60 * 1000, // 24 hours
                sameSite: 'strict'
            });

            res.cookie('refreshToken', tokens.refreshToken, {
                httpOnly: true,
                secure: process.env.NODE_ENV === 'production',
                maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
                sameSite: 'strict'
            });

            res.redirect('/store/dashboard');
        } catch (error) {
            console.error('Login error:', error);
            res.render('stores/auth/login', {
                error: error.isJoi ? 'Invalid input data' : 'An error occurred during login'
            });
        }
    }

    // التحقق من صحة بيانات التسجيل باستخدام Joi
    async validateRegister(data) {
        const schema = Joi.object({
            userName: Joi.string().min(3).max(30).required(),
            password: Joi.string().min(6).required(),
            areaId: Joi.number().integer().required(),
            phoneNumber: Joi.string().pattern(/^\+?\d{7,15}$/).required(),
            address: Joi.string().min(5).required()
        });
        return schema.validateAsync(data);
    }

    // التسجيل
    async register(req, res) {
        try {
            await this.validateRegister(req.body);

            const { userName, password, areaId, phoneNumber, address } = req.body;

            // التحقق من وجود اسم المستخدم مسبقًا
            const existingStore = await Store.findOne({ where: { userName } });
            if (existingStore) {
                const areas = await Area.findAll();
                return res.render('stores/auth/register', {
                    error: 'Username already exists',
                    areas
                });
            }

            // تشفير كلمة المرور قبل الحفظ
            const hashedPassword = await bcrypt.hash(password, 10);

            // إنشاء سجل المتجر
            await Store.create({
                userName,
                password: hashedPassword,
                areaId,
                phoneNumber,
                address,
                status: 'pending'
            });

            res.render('stores/auth/login', {
                success: 'Registration successful! Please wait for account activation.'
            });
        } catch (error) {
            console.error('Register error:', error);
            const areas = await Area.findAll();
            res.render('stores/auth/register', {
                error: error.isJoi ? 'Invalid input data' : 'An error occurred during registration',
                areas
            });
        }
    }

    // تسجيل الخروج
    async logout(req, res) {
        // مسح الـ cookies
        res.clearCookie('token');
        res.clearCookie('refreshToken');

        res.redirect('/store/login?success=' + encodeURIComponent('Logged out successfully'));
    }

}

// Middleware للتحقق من تسجيل دخول المتجر (deprecated - use auth.store instead)
function ensureStoreLoggedIn(req, res, next) {
    const { auth } = require('../middleware/auth');
    return auth.store(req, res, next);
}

module.exports = {
    controller: new StoreAuthController(),
    ensureStoreLoggedIn
};
