<h1>الطلبات الغير مكتملة</h1>
<table class="table">
  <thead>
    <tr>
      <th>رقم الطلب</th>
      <th>العميل</th>
      <th>الحالة</th>
      <th>المجموع</th>
      <th>خيارات</th>
    </tr>
  </thead>
  <tbody>
    <% orders.forEach(order => { %>
      <tr>
        <!-- رابط لعرض تفاصيل الطلب -->
        <td>
          <a href="/store/orders/<%= order.id %>">#<%= order.id %></a>
        </td>

        <!-- رابط لعرض تفاصيل العميل -->
        <td>
          <a href="/store/customers/<%= order.customer.id %>">
            <%= order.customer.name %>
          </a>
        </td>

        <!-- حالة الطلب -->
        <td><%= order.status %></td>

        <!-- المجموع -->
        <td><%= order.totalPrice %></td>

        <!-- زر إضافة توصيل -->
        <td>
          <form action="/store/orders/<%= order.id %>/accept" method="POST" style="display:inline;">
            <button type="submit" class="btn btn-sm btn-primary">
              قبول الطلب
            </button>
          </form>
          <a class="btn btn-sm btn-success" href="/store/<%= order.storeId %>/deliveries/create?orderId=<%= order.id %>">
            إضافة توصيل
          </a>
        </td>
      </tr>
    <% }); %>
  </tbody>
</table>


<% if (totalPages > 1) { %>
  <nav aria-label="Page navigation" class="d-flex justify-content-center mt-4">
    <ul class="pagination">
      <% if (currentPage > 1) { %>
        <li class="page-item">
          <a class="page-link" href="?page=<%= currentPage - 1 %>">السابق</a>
        </li>
      <% } else { %>
        <li class="page-item disabled">
          <span class="page-link">السابق</span>
        </li>
      <% } %>
  
      <% for(let i = 1; i <= totalPages; i++) { %>
        <li class="page-item <%= currentPage === i ? 'active' : '' %>">
          <a class="page-link" href="?page=<%= i %>"><%= i %></a>
        </li>
      <% } %>
  
      <% if (currentPage < totalPages) { %>
        <li class="page-item">
          <a class="page-link" href="?page=<%= currentPage + 1 %>">التالي</a>
        </li>
      <% } else { %>
        <li class="page-item disabled">
          <span class="page-link">التالي</span>
        </li>
      <% } %>
    </ul>
  </nav>
  <% } %>