document.addEventListener('DOMContentLoaded', function() {
    // Create loading overlay element
    const loadingOverlay = document.createElement('div');
    loadingOverlay.className = 'loading-overlay';
    loadingOverlay.innerHTML = '<div class="spinner"></div>';
    document.body.appendChild(loadingOverlay);

    // Show loading indicator for form submissions and links
    setupLoadingIndicators();

    // Setup automatic table row highlighting
    setupTableRowHighlighting();

    // Auto-dismiss alerts after 5 seconds
    setupAutoDismissAlerts();

    // Add current year to footer copyright if exists
    updateCopyrightYear();
});

// Show loading indicator for form submissions and navigations
function setupLoadingIndicators() {
    const loadingOverlay = document.querySelector('.loading-overlay');

    // For forms
    document.querySelectorAll('form').forEach(form => {
        // Skip forms with "no-loader" class
        if (form.classList.contains('no-loader')) return;

        form.addEventListener('submit', function() {
            // Don't show for forms that have a "method" attribute of "get"
            if (this.method && this.method.toLowerCase() === 'get') return;

            // Don't show loading for forms with data validation errors
            if (!this.checkValidity()) return;

            loadingOverlay.classList.add('show');
        });
    });

    // For links that aren't just anchors, download links, or external links
    document.querySelectorAll('a').forEach(link => {
        // Skip links with "no-loader" class
        if (link.classList.contains('no-loader')) return;

        // Skip links that open in a new tab/window
        if (link.target === '_blank') return;

        // Skip anchor links (that start with #)
        if (link.getAttribute('href')?.startsWith('#')) return;

        // Skip download links
        if (link.hasAttribute('download')) return;

        // Skip external links
        if (link.hostname !== window.location.hostname) return;

        link.addEventListener('click', function(e) {
            // Don't show loading for links with modifier keys pressed (ctrl, shift, etc.)
            if (e.ctrlKey || e.shiftKey || e.metaKey || e.altKey) return;

            loadingOverlay.classList.add('show');
        });
    });

    // Always hide loading indicator when navigating away
    window.addEventListener('beforeunload', function() {
        loadingOverlay.classList.remove('show');
    });
}

// Highlight table rows on hover
function setupTableRowHighlighting() {
    document.querySelectorAll('table tbody tr').forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.backgroundColor = '#f5f5f5';
        });

        row.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
        });
    });
}

// Auto-dismiss alerts after 5 seconds
function setupAutoDismissAlerts() {
    document.querySelectorAll('.alert').forEach(alert => {
        // Skip alerts with "no-auto-dismiss" class
        if (alert.classList.contains('no-auto-dismiss')) return;

        setTimeout(() => {
            const closeButton = alert.querySelector('.btn-close');
            if (closeButton) {
                closeButton.click();
            } else {
                alert.style.opacity = '0';
                setTimeout(() => {
                    alert.style.display = 'none';
                }, 300);
            }
        }, 5000);
    });
}

// Update footer copyright year
function updateCopyrightYear() {
    const copyrightElement = document.querySelector('.copyright-year');
    if (copyrightElement) {
        copyrightElement.textContent = new Date().getFullYear();
    }
}

// Notification System
class NotificationManager {
    constructor() {
        this.notificationCount = 0;
        this.notifications = [];
        this.init();
    }

    init() {
        this.loadNotifications();
        this.setupEventListeners();
        // تحديث الإشعارات كل 30 ثانية
        setInterval(() => this.loadNotifications(), 30000);
    }

    setupEventListeners() {
        // عند فتح قائمة الإشعارات
        const notificationDropdown = document.getElementById('notificationDropdown');
        if (notificationDropdown) {
            notificationDropdown.addEventListener('click', () => {
                this.loadNotifications();
            });
        }
    }

    async loadNotifications() {
        try {
            // تحديد نوع المستخدم ومعرفه (يمكن تحسين هذا)
            const userType = this.getUserType();
            const userId = this.getUserId();

            if (!userType || !userId) return;

            // جلب عدد الإشعارات غير المقروءة
            const countResponse = await fetch(`/notifications/api/unread-count?userType=${userType}&userId=${userId}`);
            const countData = await countResponse.json();

            if (countData.success) {
                this.updateNotificationCount(countData.count);
            }

            // جلب الإشعارات الحديثة
            const notificationsResponse = await fetch(`/notifications/api/user-notifications?userType=${userType}&userId=${userId}&limit=5`);
            const notificationsData = await notificationsResponse.json();

            if (notificationsData.success) {
                this.updateNotificationsList(notificationsData.notifications);
            }
        } catch (error) {
            console.error('Error loading notifications:', error);
        }
    }

    updateNotificationCount(count) {
        this.notificationCount = count;
        const countElement = document.getElementById('notificationCount');
        const bellElement = document.querySelector('.notification-bell');

        if (countElement && bellElement) {
            if (count > 0) {
                countElement.textContent = count > 99 ? '99+' : count;
                countElement.style.display = 'flex';
                bellElement.classList.add('has-notifications');
            } else {
                countElement.style.display = 'none';
                bellElement.classList.remove('has-notifications');
            }
        }
    }

    updateNotificationsList(notifications) {
        const listElement = document.getElementById('notificationsList');
        if (!listElement) return;

        if (notifications.length === 0) {
            listElement.innerHTML = `
                <li class="dropdown-item text-center text-muted">
                    <i class="fas fa-bell-slash"></i> لا توجد إشعارات
                </li>
            `;
            return;
        }

        listElement.innerHTML = notifications.map(notification => `
            <li class="dropdown-item ${!notification.readAt ? 'unread' : ''}" onclick="markNotificationAsRead(${notification.id})">
                <div class="notification-title">${notification.title}</div>
                <div class="notification-text">${this.truncateText(notification.message, 60)}</div>
                <div class="notification-time">${this.formatTime(notification.createdAt)}</div>
            </li>
        `).join('');
    }

    truncateText(text, maxLength) {
        return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
    }

    formatTime(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffInMinutes = Math.floor((now - date) / (1000 * 60));

        if (diffInMinutes < 1) return 'الآن';
        if (diffInMinutes < 60) return `منذ ${diffInMinutes} دقيقة`;
        if (diffInMinutes < 1440) return `منذ ${Math.floor(diffInMinutes / 60)} ساعة`;
        return `منذ ${Math.floor(diffInMinutes / 1440)} يوم`;
    }

    getUserType() {
        // تحديد نوع المستخدم من الجلسة أو DOM
        if (document.querySelector('[data-user-type]')) {
            return document.querySelector('[data-user-type]').dataset.userType;
        }
        // افتراضي للمشرف إذا كان في صفحة الإدارة
        if (window.location.pathname.includes('/admin') || window.location.pathname.includes('/notifications')) {
            return 'admin';
        }
        return null;
    }

    getUserId() {
        // تحديد معرف المستخدم من الجلسة أو DOM
        if (document.querySelector('[data-user-id]')) {
            return document.querySelector('[data-user-id]').dataset.userId;
        }
        // افتراضي للمشرف
        return 1;
    }

    showToast(notification) {
        // إنشاء toast notification
        const toastContainer = this.getOrCreateToastContainer();
        const toast = document.createElement('div');
        toast.className = `toast notification-toast type-${notification.type}`;
        toast.setAttribute('role', 'alert');
        toast.innerHTML = `
            <div class="toast-header">
                <i class="fas fa-${this.getTypeIcon(notification.type)} me-2"></i>
                <strong class="me-auto">${notification.title}</strong>
                <small>${this.formatTime(notification.createdAt)}</small>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body">
                ${notification.message}
                ${notification.actionUrl ? `<br><a href="${notification.actionUrl}" class="btn btn-sm btn-primary mt-2">${notification.actionText || 'عرض'}</a>` : ''}
            </div>
        `;

        toastContainer.appendChild(toast);

        // تفعيل Toast
        const bsToast = new bootstrap.Toast(toast, {
            autohide: true,
            delay: 5000
        });
        bsToast.show();

        // إزالة Toast بعد الإخفاء
        toast.addEventListener('hidden.bs.toast', () => {
            toast.remove();
        });
    }

    getOrCreateToastContainer() {
        let container = document.querySelector('.toast-container');
        if (!container) {
            container = document.createElement('div');
            container.className = 'toast-container';
            document.body.appendChild(container);
        }
        return container;
    }

    getTypeIcon(type) {
        const icons = {
            'info': 'info-circle',
            'success': 'check-circle',
            'warning': 'exclamation-triangle',
            'error': 'times-circle',
            'order': 'shopping-cart',
            'promotion': 'tag',
            'system': 'cog'
        };
        return icons[type] || 'bell';
    }
}

// تهيئة مدير الإشعارات
let notificationManager;
document.addEventListener('DOMContentLoaded', function() {
    notificationManager = new NotificationManager();
});

// وظائف عامة للإشعارات
async function markNotificationAsRead(notificationId) {
    try {
        const response = await fetch(`/notifications/${notificationId}/read`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
        });

        const data = await response.json();
        if (data.success && notificationManager) {
            notificationManager.loadNotifications();
        }
    } catch (error) {
        console.error('Error marking notification as read:', error);
    }
}

async function markAllNotificationsAsRead() {
    try {
        const userType = notificationManager.getUserType();
        const userId = notificationManager.getUserId();

        const response = await fetch('/notifications/mark-all-read', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify({ userType, userId })
        });

        const data = await response.json();
        if (data.success && notificationManager) {
            notificationManager.loadNotifications();
        }
    } catch (error) {
        console.error('Error marking all notifications as read:', error);
    }
}

// وظائف خاصة بالعملاء
async function markAllCustomerNotificationsAsRead() {
    try {
        const response = await fetch('/customers/notifications/mark-all-read', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
        });

        const data = await response.json();
        if (data.success) {
            // تحديث العداد والقائمة
            updateCustomerNotificationCount(0);
            loadCustomerNotifications();
        } else {
            alert('حدث خطأ: ' + data.message);
        }
    } catch (error) {
        console.error('Error marking all customer notifications as read:', error);
        alert('حدث خطأ في الاتصال');
    }
}

// وظائف خاصة بالمتاجر
async function markAllStoreNotificationsAsRead() {
    try {
        const response = await fetch('/store/notifications/mark-all-read', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
        });

        const data = await response.json();
        if (data.success) {
            // تحديث العداد والقائمة
            updateStoreNotificationCount(0);
            loadStoreNotifications();
        } else {
            alert('حدث خطأ: ' + data.message);
        }
    } catch (error) {
        console.error('Error marking all store notifications as read:', error);
        alert('حدث خطأ في الاتصال');
    }
}

// تحديث عداد الإشعارات للعملاء
function updateCustomerNotificationCount(count) {
    const countElement = document.getElementById('customerNotificationCount');
    const bellElement = document.querySelector('#customerNotificationDropdown');

    if (countElement && bellElement) {
        if (count > 0) {
            countElement.textContent = count > 99 ? '99+' : count;
            countElement.style.display = 'flex';
            bellElement.classList.add('has-notifications');
        } else {
            countElement.style.display = 'none';
            bellElement.classList.remove('has-notifications');
        }
    }
}

// تحديث عداد الإشعارات للمتاجر
function updateStoreNotificationCount(count) {
    const countElement = document.getElementById('storeNotificationCount');
    const bellElement = document.querySelector('#storeNotificationDropdown');

    if (countElement && bellElement) {
        if (count > 0) {
            countElement.textContent = count > 99 ? '99+' : count;
            countElement.style.display = 'flex';
            bellElement.classList.add('has-notifications');
        } else {
            countElement.style.display = 'none';
            bellElement.classList.remove('has-notifications');
        }
    }
}

// تحميل إشعارات العملاء
async function loadCustomerNotifications() {
    try {
        // جلب عدد الإشعارات غير المقروءة
        const countResponse = await fetch('/customers/notifications/api/unread-count');
        const countData = await countResponse.json();

        if (countData.success) {
            updateCustomerNotificationCount(countData.count);
        }

        // جلب الإشعارات الحديثة
        const notificationsResponse = await fetch('/customers/notifications/api/recent?limit=5');
        const notificationsData = await notificationsResponse.json();

        if (notificationsData.success) {
            updateCustomerNotificationsList(notificationsData.notifications);
        }
    } catch (error) {
        console.error('Error loading customer notifications:', error);
    }
}

// تحميل إشعارات المتاجر
async function loadStoreNotifications() {
    try {
        // جلب عدد الإشعارات غير المقروءة
        const countResponse = await fetch('/store/notifications/api/unread-count');
        const countData = await countResponse.json();

        if (countData.success) {
            updateStoreNotificationCount(countData.count);
        }

        // جلب الإشعارات الحديثة
        const notificationsResponse = await fetch('/store/notifications/api/recent?limit=5');
        const notificationsData = await notificationsResponse.json();

        if (notificationsData.success) {
            updateStoreNotificationsList(notificationsData.notifications);
        }
    } catch (error) {
        console.error('Error loading store notifications:', error);
    }
}

// تحديث قائمة إشعارات العملاء
function updateCustomerNotificationsList(notifications) {
    const listElement = document.getElementById('customerNotificationsList');
    if (!listElement) return;

    if (notifications.length === 0) {
        listElement.innerHTML = `
            <li class="dropdown-item text-center text-muted">
                <i class="fas fa-bell-slash"></i> لا توجد إشعارات
            </li>
        `;
        return;
    }

    listElement.innerHTML = notifications.map(notification => `
        <li class="dropdown-item ${!notification.readAt ? 'unread' : ''}" onclick="markCustomerNotificationAsRead(${notification.id})">
            <div class="notification-title">${notification.title}</div>
            <div class="notification-text">${truncateText(notification.message, 60)}</div>
            <div class="notification-time">${formatTime(notification.createdAt)}</div>
        </li>
    `).join('');
}

// تحديث قائمة إشعارات المتاجر
function updateStoreNotificationsList(notifications) {
    const listElement = document.getElementById('storeNotificationsList');
    if (!listElement) return;

    if (notifications.length === 0) {
        listElement.innerHTML = `
            <li class="dropdown-item text-center text-muted">
                <i class="fas fa-bell-slash"></i> لا توجد إشعارات
            </li>
        `;
        return;
    }

    listElement.innerHTML = notifications.map(notification => `
        <li class="dropdown-item ${!notification.readAt ? 'unread' : ''}" onclick="markStoreNotificationAsRead(${notification.id})">
            <div class="notification-title">${notification.title}</div>
            <div class="notification-text">${truncateText(notification.message, 60)}</div>
            <div class="notification-time">${formatTime(notification.createdAt)}</div>
        </li>
    `).join('');
}

// وضع إشعار العميل كمقروء
async function markCustomerNotificationAsRead(notificationId) {
    try {
        const response = await fetch(`/customers/notifications/${notificationId}/read`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
        });

        const data = await response.json();
        if (data.success) {
            loadCustomerNotifications();
        }
    } catch (error) {
        console.error('Error marking customer notification as read:', error);
    }
}

// وضع إشعار المتجر كمقروء
async function markStoreNotificationAsRead(notificationId) {
    try {
        const response = await fetch(`/store/notifications/${notificationId}/read`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
        });

        const data = await response.json();
        if (data.success) {
            loadStoreNotifications();
        }
    } catch (error) {
        console.error('Error marking store notification as read:', error);
    }
}

// وظائف مساعدة
function truncateText(text, maxLength) {
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
}

function formatTime(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now - date) / (1000 * 60));

    if (diffInMinutes < 1) return 'الآن';
    if (diffInMinutes < 60) return `منذ ${diffInMinutes} دقيقة`;
    if (diffInMinutes < 1440) return `منذ ${Math.floor(diffInMinutes / 60)} ساعة`;
    return `منذ ${Math.floor(diffInMinutes / 1440)} يوم`;
}

// تهيئة الإشعارات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تحميل إشعارات العملاء إذا كان مسجل دخول
    if (document.getElementById('customerNotificationDropdown')) {
        loadCustomerNotifications();
        // تحديث كل 30 ثانية
        setInterval(loadCustomerNotifications, 30000);
    }

    // تحميل إشعارات المتاجر إذا كان مسجل دخول
    if (document.getElementById('storeNotificationDropdown')) {
        loadStoreNotifications();
        // تحديث كل 30 ثانية
        setInterval(loadStoreNotifications, 30000);
    }
});