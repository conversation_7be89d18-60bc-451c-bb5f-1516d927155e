# 📱 توثيق API نظام إدارة المتاجر الذكي

## 🌟 نظرة عامة

هذا API مصمم خصيصاً لتطبيقات Flutter المحمولة لنظام إدارة المتاجر الذكي. يوفر جميع الوظائف المطلوبة للعملاء للتفاعل مع النظام.

### 🔗 Base URL
```
http://localhost:3001/api
```

### 🔐 المصادقة
يستخدم النظام JWT (JSON Web Tokens) للمصادقة. يجب إرسال التوكن في header:
```
Authorization: Bearer YOUR_JWT_TOKEN
```

### 📋 تنسيق الاستجابة
جميع الاستجابات تأتي بالتنسيق التالي:
```json
{
  "success": true/false,
  "message": "رسالة باللغة العربية",
  "data": {} // البيانات أو null
}
```

---

## 👤 مسارات العملاء (Customers)

### 1. تسجيل الدخول
```http
POST /api/customers/login
```

**Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "success": true,
  "message": "تم تسجيل الدخول بنجاح",
  "data": {
    "token": "jwt_token_here",
    "customer": {
      "id": 1,
      "name": "أحمد محمد",
      "email": "<EMAIL>",
      "phone": "01234567890",
      "address": "العنوان",
      "areaId": 1
    }
  }
}
```

### 2. تسجيل عميل جديد
```http
POST /api/customers/register
```

**Body:**
```json
{
  "name": "أحمد محمد",
  "email": "<EMAIL>",
  "phone": "01234567890",
  "password": "password123",
  "address": "العنوان",
  "areaId": 1
}
```

### 3. الحصول على بيانات العميل
```http
GET /api/customers/profile
Authorization: Bearer TOKEN
```

### 4. تحديث بيانات العميل
```http
PUT /api/customers/profile
Authorization: Bearer TOKEN
```

**Body:**
```json
{
  "name": "الاسم الجديد",
  "phone": "01234567890",
  "address": "العنوان الجديد",
  "areaId": 2
}
```

### 5. تغيير كلمة المرور
```http
PUT /api/customers/change-password
Authorization: Bearer TOKEN
```

**Body:**
```json
{
  "currentPassword": "old_password",
  "newPassword": "new_password"
}
```

---

## 🏪 مسارات المتاجر (Stores)

### 1. الحصول على جميع المتاجر
```http
GET /api/stores?page=1&limit=10&search=&areaId=&categoryId=
```

**Parameters:**
- `page`: رقم الصفحة (افتراضي: 1)
- `limit`: عدد العناصر (افتراضي: 10)
- `search`: نص البحث
- `areaId`: فلتر بالمنطقة
- `categoryId`: فلتر بالفئة

### 2. تفاصيل متجر واحد
```http
GET /api/stores/:id
```

### 3. البحث في المتاجر
```http
GET /api/stores/search?q=نص البحث&areaId=1&minRating=4
```

### 4. المتاجر المميزة
```http
GET /api/stores/featured?limit=5
```

---

## 📦 مسارات المنتجات (Products)

### 1. الحصول على جميع المنتجات
```http
GET /api/products?page=1&limit=10&search=&categoryId=&storeId=&minPrice=&maxPrice=
```

### 2. تفاصيل منتج واحد
```http
GET /api/products/:id
```

### 3. البحث في المنتجات
```http
GET /api/products/search?q=نص البحث&categoryId=1&minPrice=10&maxPrice=100
```

### 4. المنتجات المميزة
```http
GET /api/products/featured?limit=10
```

### 5. منتجات حسب الفئة
```http
GET /api/products/category/:categoryId?page=1&limit=10
```

---

## 🛒 مسارات الطلبات (Orders)

### 1. إنشاء طلب جديد
```http
POST /api/orders
Authorization: Bearer TOKEN
```

**Body:**
```json
{
  "storeId": 1,
  "products": [
    {
      "productId": 1,
      "quantity": 2
    },
    {
      "productId": 2,
      "quantity": 1
    }
  ],
  "deliveryAddress": "عنوان التوصيل",
  "notes": "ملاحظات إضافية"
}
```

### 2. الحصول على طلبات العميل
```http
GET /api/orders?page=1&limit=10&status=pending
Authorization: Bearer TOKEN
```

### 3. تفاصيل طلب واحد
```http
GET /api/orders/:id
Authorization: Bearer TOKEN
```

### 4. إلغاء طلب
```http
PUT /api/orders/:id/cancel
Authorization: Bearer TOKEN
```

### 5. إحصائيات الطلبات
```http
GET /api/orders/stats
Authorization: Bearer TOKEN
```

---

## 📂 مسارات عامة (General)

### 1. الفئات
```http
GET /api/categories?includeCount=true
GET /api/categories/:id?page=1&limit=10
```

### 2. المدن
```http
GET /api/countries?includeAreas=true
```

### 3. المناطق
```http
GET /api/areas?countryId=1&includeStoresCount=true
GET /api/areas/:id?page=1&limit=10
```

### 4. البحث العام
```http
GET /api/search?q=نص البحث&type=all&limit=5
```

**Types:** `all`, `stores`, `products`, `categories`

---

## 🔔 مسارات الإشعارات (Notifications)

### 1. الحصول على الإشعارات
```http
GET /api/notifications?page=1&limit=20&unreadOnly=false
Authorization: Bearer TOKEN
```

### 2. عدد الإشعارات غير المقروءة
```http
GET /api/notifications/unread-count
Authorization: Bearer TOKEN
```

### 3. وضع علامة مقروء
```http
PUT /api/notifications/:id/read
Authorization: Bearer TOKEN
```

### 4. وضع علامة مقروء على الكل
```http
PUT /api/notifications/mark-all-read
Authorization: Bearer TOKEN
```

---

## 🛡️ أمان API

### Rate Limiting
- **عام**: 200 طلب كل 15 دقيقة
- **تسجيل الدخول**: 5 محاولات كل 15 دقيقة

### أكواد الخطأ
- `400`: بيانات غير صحيحة
- `401`: غير مصرح (توكن غير صحيح)
- `403`: ممنوع (لا توجد صلاحية)
- `404`: غير موجود
- `409`: تضارب (بيانات موجودة مسبقاً)
- `429`: تجاوز الحد الأقصى للطلبات
- `500`: خطأ في الخادم

---

## 📱 أمثلة Flutter

### إعداد HTTP Client
```dart
import 'package:http/http.dart' as http;
import 'dart:convert';

class ApiService {
  static const String baseUrl = 'http://localhost:3001/api';
  static String? _token;
  
  static Map<String, String> get headers => {
    'Content-Type': 'application/json',
    if (_token != null) 'Authorization': 'Bearer $_token',
  };
  
  static void setToken(String token) {
    _token = token;
  }
}
```

### تسجيل الدخول
```dart
Future<Map<String, dynamic>> login(String email, String password) async {
  final response = await http.post(
    Uri.parse('${ApiService.baseUrl}/customers/login'),
    headers: ApiService.headers,
    body: json.encode({
      'email': email,
      'password': password,
    }),
  );
  
  final data = json.decode(response.body);
  
  if (data['success']) {
    ApiService.setToken(data['data']['token']);
  }
  
  return data;
}
```

### جلب المتاجر
```dart
Future<List<Store>> getStores({int page = 1, String? search}) async {
  final uri = Uri.parse('${ApiService.baseUrl}/stores').replace(
    queryParameters: {
      'page': page.toString(),
      'limit': '10',
      if (search != null) 'search': search,
    },
  );
  
  final response = await http.get(uri, headers: ApiService.headers);
  final data = json.decode(response.body);
  
  if (data['success']) {
    return (data['data']['stores'] as List)
        .map((store) => Store.fromJson(store))
        .toList();
  }
  
  throw Exception(data['message']);
}
```

### إنشاء طلب
```dart
Future<Order> createOrder({
  required int storeId,
  required List<CartItem> items,
  required String address,
  String? notes,
}) async {
  final response = await http.post(
    Uri.parse('${ApiService.baseUrl}/orders'),
    headers: ApiService.headers,
    body: json.encode({
      'storeId': storeId,
      'products': items.map((item) => {
        'productId': item.productId,
        'quantity': item.quantity,
      }).toList(),
      'deliveryAddress': address,
      'notes': notes,
    }),
  );
  
  final data = json.decode(response.body);
  
  if (data['success']) {
    return Order.fromJson(data['data']['order']);
  }
  
  throw Exception(data['message']);
}
```

---

## 🔧 اختبار API

يمكنك اختبار API باستخدام:
- **Postman**: استيراد collection
- **curl**: أوامر سطر الأوامر
- **Flutter**: تطبيق تجريبي

### مثال curl
```bash
# تسجيل الدخول
curl -X POST http://localhost:3001/api/customers/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'

# جلب المتاجر
curl -X GET "http://localhost:3001/api/stores?page=1&limit=5" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

## 📞 الدعم

للمساعدة والدعم:
- **البريد الإلكتروني**: <EMAIL>
- **التوثيق**: `/api/` للمعلومات الأساسية
- **فحص الصحة**: `/api/health` للتأكد من عمل API

---

**تم تطوير هذا API بعناية فائقة ليوفر تجربة مطور ممتازة لتطبيقات Flutter** 🚀
