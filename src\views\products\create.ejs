<div class="container">
    <div class="form-container">
        <h1>Add New Product</h1>
        <form action="/products" method="POST" enctype="multipart/form-data">
            <div class="form-group">
                <label for="name">Product Name</label>
                <input type="text" class="form-control" id="name" name="name" required>
            </div>

            <div class="form-group">
                <label for="price">Price</label>
                <input type="number" step="0.01" class="form-control" id="price" name="price" required>
            </div>

            <div class="form-group">
                <label for="description">Description</label>
                <textarea class="form-control" id="description" name="description" rows="3"></textarea>
            </div>

            <div class="form-group">
                <label for="storeId">Store</label>
                <select class="form-control" id="storeId" name="storeId" required>
                    <option value="">Select a Store</option>
                    <% stores.forEach(store => { %>
                        <option value="<%= store.id %>"><%= store.name %></option>
                    <% }); %>
                </select>
            </div>

            <div class="form-group">
                <label for="images">Product Images</label>
                <input type="file" class="form-control" id="images" name="images" multiple accept="image/*">
            </div>

            <div class="mt-4">
                <button type="submit" class="btn btn-primary">Create Product</button>
                <a href="/products" class="btn btn-secondary">Cancel</a>
            </div>
        </form>
    </div>
</div> 