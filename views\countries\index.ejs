<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Countries</h1>
        <a href="/admin/countries/create" class="btn btn-primary">Add New Country</a>
    </div>

    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Areas Count</th>
                            <th>Notes</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <% countries.forEach(country => { %>
                            <tr>
                                <td><%= country.id %></td>
                                <td><%= country.name %></td>
                                <td><%= country.areas ? country.areas.length : 0 %></td>
                                <td><%= country.notes || '-' %></td>
                                <td class="action-buttons">
                                    <a href="/admin/countries/<%= country.id %>/edit" class="btn btn-sm btn-warning">
                                        <i class="fas fa-edit"></i> Edit
                                    </a>
                                    <form action="/admin/countries/<%= country.id %>/delete" method="POST" class="d-inline">
                                        <button type="submit" class="btn btn-sm btn-danger" 
                                                onclick="return confirm('Are you sure you want to delete this country? This will also delete all associated areas.')">
                                            <i class="fas fa-trash"></i> Delete
                                        </button>
                                    </form>
                                </td>
                            </tr>
                        <% }); %>
                    </tbody>
                </table>
                <% if (totalPages > 1) { %>
                    <nav aria-label="Page navigation" class="d-flex justify-content-center mt-4">
                      <ul class="pagination">
                        <% if (currentPage > 1) { %>
                          <li class="page-item">
                            <a class="page-link" href="?page=<%= currentPage - 1 %>">السابق</a>
                          </li>
                        <% } else { %>
                          <li class="page-item disabled">
                            <span class="page-link">السابق</span>
                          </li>
                        <% } %>
                    
                        <% for(let i = 1; i <= totalPages; i++) { %>
                          <li class="page-item <%= currentPage === i ? 'active' : '' %>">
                            <a class="page-link" href="?page=<%= i %>"><%= i %></a>
                          </li>
                        <% } %>
                    
                        <% if (currentPage < totalPages) { %>
                          <li class="page-item">
                            <a class="page-link" href="?page=<%= currentPage + 1 %>">التالي</a>
                          </li>
                        <% } else { %>
                          <li class="page-item disabled">
                            <span class="page-link">التالي</span>
                          </li>
                        <% } %>
                      </ul>
                    </nav>
                    <% } %>
            </div>

            <% if (countries.length === 0) { %>
                <div class="alert alert-info">No countries found.</div>
            <% } %>
        </div>
    </div>
</div> 