/* نظام إدارة المتاجر الذكي - ملف الأنماط الرئيسي */

/* متغيرات CSS */
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 1rem;
    --box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.075);
    --box-shadow-lg: 0 0.5rem 1rem rgba(0,0,0,0.15);
    --transition: all 0.3s ease;
    --font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* إعادة تعيين الأنماط الأساسية */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family);
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    line-height: 1.6;
    color: var(--dark-color);
}

/* تحسينات الطباعة */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--dark-color);
}

p {
    margin-bottom: 1rem;
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--secondary-color);
}

/* أنماط الأزرار المحسنة */
.btn {
    border-radius: 0.75rem;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: var(--transition);
    border: none;
    position: relative;
    overflow: hidden;
    text-transform: none;
    letter-spacing: 0.5px;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow);
}

.btn:active {
    transform: translateY(0);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--secondary-color) 0%, var(--primary-color) 100%);
    color: white;
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
    color: white;
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    color: white;
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
    color: white;
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: white;
}

/* أنماط البطاقات */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
    background: white;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow-lg);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border: none;
    padding: 1.25rem;
    font-weight: 600;
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    background: rgba(0,0,0,0.03);
    border: none;
    padding: 1rem 1.5rem;
}

/* أنماط الجداول */
.table-container {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    background: white;
}

.table {
    margin: 0;
    font-size: 0.9rem;
}

.table thead th {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border: none;
    font-weight: 600;
    padding: 1.25rem;
    position: relative;
    text-align: center;
}

.table thead th a {
    color: white;
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.table thead th a:hover {
    color: rgba(255, 255, 255, 0.8);
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.05);
    transform: scale(1.01);
}

.table td {
    padding: 1rem 1.25rem;
    vertical-align: middle;
    border-color: rgba(0,0,0,0.05);
    text-align: center;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0,0,0,0.02);
}

/* أنماط الشارات */
.badge {
    font-size: 0.8rem;
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-weight: 500;
    letter-spacing: 0.5px;
}

.badge-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.badge-danger {
    background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
}

.badge-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    color: var(--dark-color);
}

.badge-info {
    background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
}

.badge-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

/* أنماط النماذج */
.form-control, .form-select {
    border-radius: 0.75rem;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: var(--transition);
    font-size: 0.95rem;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-label {
    font-weight: 500;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.input-group {
    border-radius: 0.75rem;
    overflow: hidden;
}

.input-group-text {
    background: var(--light-color);
    border: 2px solid #e9ecef;
    color: var(--dark-color);
}

/* أنماط التنبيهات */
.alert {
    border-radius: var(--border-radius);
    border: none;
    padding: 1.25rem;
    margin-bottom: 1.5rem;
    font-weight: 500;
}

.alert-success {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(32, 201, 151, 0.1) 100%);
    color: #155724;
    border-left: 4px solid #28a745;
}

.alert-danger {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1) 0%, rgba(232, 62, 140, 0.1) 100%);
    color: #721c24;
    border-left: 4px solid #dc3545;
}

.alert-warning {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(253, 126, 20, 0.1) 100%);
    color: #856404;
    border-left: 4px solid #ffc107;
}

.alert-info {
    background: linear-gradient(135deg, rgba(23, 162, 184, 0.1) 0%, rgba(111, 66, 193, 0.1) 100%);
    color: #0c5460;
    border-left: 4px solid #17a2b8;
}

/* أنماط الإشعارات */
.notification-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: var(--danger-color);
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    animation: pulse 2s infinite;
    z-index: 10;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.notification-dropdown {
    min-width: 350px;
    max-height: 400px;
    overflow-y: auto;
}

.notification-item {
    padding: 1rem;
    border-bottom: 1px solid #eee;
    transition: var(--transition);
}

.notification-item:hover {
    background-color: var(--light-color);
}

.notification-item.unread {
    background-color: rgba(102, 126, 234, 0.05);
    border-left: 3px solid var(--primary-color);
}

/* أنماط البحث والفلتر */
.search-filter-container {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.filter-section {
    background: var(--light-color);
    border-radius: 0.75rem;
    padding: 1rem;
    margin-top: 1rem;
}

.filter-toggle {
    cursor: pointer;
    user-select: none;
}

.active-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 1rem;
}

.filter-tag {
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filter-tag .remove {
    cursor: pointer;
    font-weight: bold;
}

/* أنماط الصفحات */
.pagination {
    justify-content: center;
    margin-top: 2rem;
}

.page-link {
    border-radius: 0.5rem;
    margin: 0 0.25rem;
    border: 2px solid #e9ecef;
    color: var(--primary-color);
    padding: 0.75rem 1rem;
    transition: var(--transition);
}

.page-link:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.page-item.active .page-link {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border-color: var(--primary-color);
}

/* أنماط الشريط الجانبي */
.sidebar {
    min-height: calc(100vh - 76px);
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    box-shadow: var(--box-shadow);
    position: sticky;
    top: 76px;
}

.sidebar .nav-link {
    color: rgba(255,255,255,0.8);
    padding: 1rem 1.5rem;
    margin: 0.25rem 1rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    color: white;
    background: rgba(255,255,255,0.15);
    transform: translateX(-5px);
    box-shadow: var(--box-shadow);
}

.sidebar .nav-link i {
    width: 20px;
    text-align: center;
}

/* أنماط المحتوى الرئيسي */
.main-content {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin: 1.5rem;
    padding: 2rem;
    min-height: calc(100vh - 152px);
}

/* أنماط الإحصائيات */
.stats-card {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    text-align: center;
    transition: var(--transition);
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow-lg);
}

.stats-card .icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    opacity: 0.8;
}

.stats-card .number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stats-card .label {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* أنماط الرسوم البيانية */
.chart-container {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

/* أنماط الأزرار العائمة */
.floating-btn {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border: none;
    box-shadow: var(--box-shadow-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    transition: var(--transition);
    z-index: 1000;
}

.floating-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 1rem 2rem rgba(0,0,0,0.2);
}

/* أنماط التحميل */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.3s ease;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* أنماط الشريط المخصص للتمرير */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-color);
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        top: 76px;
        left: -100%;
        width: 280px;
        z-index: 1000;
        transition: var(--transition);
    }

    .sidebar.show {
        left: 0;
    }

    .main-content {
        margin: 1rem;
        padding: 1rem;
    }

    .table-responsive {
        border-radius: var(--border-radius);
    }

    .floating-btn {
        bottom: 1rem;
        right: 1rem;
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .stats-card {
        margin-bottom: 1rem;
    }

    .notification-dropdown {
        min-width: 300px;
    }
}

@media (max-width: 576px) {
    .main-content {
        margin: 0.5rem;
        padding: 1rem;
    }

    .card-body {
        padding: 1rem;
    }

    .table td, .table th {
        padding: 0.75rem 0.5rem;
        font-size: 0.8rem;
    }

    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
}

/* دعم الوضع المظلم */
@media (prefers-color-scheme: dark) {
    .main-content {
        background: #2d3748;
        color: white;
    }

    .card {
        background: #4a5568;
        color: white;
    }

    .table {
        color: white;
    }

    .table tbody tr:hover {
        background-color: rgba(255, 255, 255, 0.05);
    }

    .form-control, .form-select {
        background: #4a5568;
        border-color: #718096;
        color: white;
    }

    .form-control:focus, .form-select:focus {
        background: #4a5568;
        border-color: var(--primary-color);
        color: white;
    }
}

/* أنماط الطباعة */
@media print {
    .navbar, .sidebar, .floating-btn, .alert {
        display: none !important;
    }

    .main-content {
        margin: 0;
        padding: 0;
        box-shadow: none;
        border-radius: 0;
    }

    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }

    .btn {
        display: none !important;
    }
}

/* تحسينات الأداء */
.card, .btn, .table tbody tr {
    will-change: transform;
}

/* أنماط إضافية للتحسين */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.shadow-custom {
    box-shadow: var(--box-shadow-lg);
}

.border-radius-custom {
    border-radius: var(--border-radius);
}

.transition-custom {
    transition: var(--transition);
}
