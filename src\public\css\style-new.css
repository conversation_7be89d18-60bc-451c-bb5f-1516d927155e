/* نظام إدارة المتاجر الذكي - ملف الأنماط الرئيسي */

/* متغيرات CSS - نظام ألوان مذهل */
:root {
    /* الألوان الأساسية - تدرجات زرقاء وبنفسجية مذهلة */
    --primary-color: #6366f1;
    --primary-light: #818cf8;
    --primary-dark: #4f46e5;
    --secondary-color: #8b5cf6;
    --secondary-light: #a78bfa;
    --secondary-dark: #7c3aed;

    /* ألوان الحالة - متناسقة ومذهلة */
    --success-color: #10b981;
    --success-light: #34d399;
    --success-dark: #059669;
    --danger-color: #ef4444;
    --danger-light: #f87171;
    --danger-dark: #dc2626;
    --warning-color: #f59e0b;
    --warning-light: #fbbf24;
    --warning-dark: #d97706;
    --info-color: #06b6d4;
    --info-light: #22d3ee;
    --info-dark: #0891b2;

    /* ألوان الخلفية والنص */
    --light-color: #f8fafc;
    --lighter-color: #ffffff;
    --dark-color: #1e293b;
    --darker-color: #0f172a;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;

    /* تدرجات مذهلة */
    --gradient-primary: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #ec4899 100%);
    --gradient-secondary: linear-gradient(135deg, #06b6d4 0%, #3b82f6 50%, #8b5cf6 100%);
    --gradient-success: linear-gradient(135deg, #10b981 0%, #059669 100%);
    --gradient-danger: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    --gradient-warning: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    --gradient-info: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
    --gradient-dark: linear-gradient(135deg, #374151 0%, #1f2937 100%);

    /* خلفيات مذهلة */
    --bg-gradient-main: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --bg-gradient-light: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    --bg-gradient-cool: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    --bg-gradient-warm: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);

    /* الظلال والتأثيرات */
    --border-radius: 1.25rem;
    --border-radius-sm: 0.75rem;
    --border-radius-lg: 1.75rem;
    --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --box-shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --box-shadow-xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --box-shadow-colored: 0 10px 15px -3px rgba(99, 102, 241, 0.1), 0 4px 6px -2px rgba(99, 102, 241, 0.05);

    /* الانتقالات */
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);

    /* الخطوط */
    --font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
}

/* إعادة تعيين الأنماط الأساسية */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family);
    background: var(--gradient-primary);
    background-attachment: fixed;
    min-height: 100vh;
    line-height: 1.7;
    color: var(--dark-color);
    font-size: var(--font-size-base);
    overflow-x: hidden;
    position: relative;
}

/* خلفية متحركة مذهلة */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
    z-index: -1;
    animation: backgroundMove 20s ease-in-out infinite;
}

@keyframes backgroundMove {
    0%, 100% { transform: translateX(0) translateY(0); }
    25% { transform: translateX(-20px) translateY(-10px); }
    50% { transform: translateX(20px) translateY(10px); }
    75% { transform: translateX(-10px) translateY(20px); }
}

/* تحسينات الطباعة */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--dark-color);
}

p {
    margin-bottom: 1rem;
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--secondary-color);
}

/* أنماط الأزرار المذهلة */
.btn {
    border-radius: var(--border-radius);
    padding: 0.875rem 2rem;
    font-weight: 600;
    font-size: var(--font-size-sm);
    transition: var(--transition);
    border: none;
    position: relative;
    overflow: hidden;
    text-transform: none;
    letter-spacing: 0.025em;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    text-decoration: none;
    box-shadow: var(--box-shadow);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition);
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: var(--box-shadow-lg);
}

.btn:active {
    transform: translateY(-1px) scale(0.98);
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--box-shadow-colored);
}

.btn-primary:hover {
    background: var(--gradient-primary);
    color: white;
    box-shadow: 0 15px 30px -5px rgba(99, 102, 241, 0.4);
}

.btn-success {
    background: var(--gradient-success);
    color: white;
    box-shadow: 0 10px 15px -3px rgba(16, 185, 129, 0.1), 0 4px 6px -2px rgba(16, 185, 129, 0.05);
}

.btn-success:hover {
    box-shadow: 0 15px 30px -5px rgba(16, 185, 129, 0.4);
    color: white;
}

.btn-danger {
    background: var(--gradient-danger);
    color: white;
    box-shadow: 0 10px 15px -3px rgba(239, 68, 68, 0.1), 0 4px 6px -2px rgba(239, 68, 68, 0.05);
}

.btn-danger:hover {
    box-shadow: 0 15px 30px -5px rgba(239, 68, 68, 0.4);
    color: white;
}

.btn-warning {
    background: var(--gradient-warning);
    color: white;
    box-shadow: 0 10px 15px -3px rgba(245, 158, 11, 0.1), 0 4px 6px -2px rgba(245, 158, 11, 0.05);
}

.btn-warning:hover {
    box-shadow: 0 15px 30px -5px rgba(245, 158, 11, 0.4);
    color: white;
}

.btn-info {
    background: var(--gradient-info);
    color: white;
    box-shadow: 0 10px 15px -3px rgba(6, 182, 212, 0.1), 0 4px 6px -2px rgba(6, 182, 212, 0.05);
}

.btn-info:hover {
    box-shadow: 0 15px 30px -5px rgba(6, 182, 212, 0.4);
    color: white;
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.btn-outline-success {
    border: 2px solid var(--success-color);
    color: var(--success-color);
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
}

.btn-outline-success:hover {
    background: var(--success-color);
    color: white;
    border-color: var(--success-color);
}

.btn-outline-danger {
    border: 2px solid var(--danger-color);
    color: var(--danger-color);
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
}

.btn-outline-danger:hover {
    background: var(--danger-color);
    color: white;
    border-color: var(--danger-color);
}

.btn-outline-warning {
    border: 2px solid var(--warning-color);
    color: var(--warning-color);
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
}

.btn-outline-warning:hover {
    background: var(--warning-color);
    color: white;
    border-color: var(--warning-color);
}

.btn-outline-info {
    border: 2px solid var(--info-color);
    color: var(--info-color);
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
}

.btn-outline-info:hover {
    background: var(--info-color);
    color: white;
    border-color: var(--info-color);
}

/* أحجام الأزرار */
.btn-sm {
    padding: 0.5rem 1.25rem;
    font-size: var(--font-size-xs);
    border-radius: var(--border-radius-sm);
}

.btn-lg {
    padding: 1.25rem 2.5rem;
    font-size: var(--font-size-lg);
    border-radius: var(--border-radius-lg);
}

/* أنماط البطاقات المذهلة */
.card {
    border: none;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
}

.card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--box-shadow-xl);
    border-color: rgba(99, 102, 241, 0.3);
}

.card-header {
    background: var(--gradient-primary);
    color: white;
    border: none;
    padding: 1.5rem 2rem;
    font-weight: 700;
    font-size: var(--font-size-lg);
    position: relative;
    overflow: hidden;
}

.card-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: var(--transition);
}

.card:hover .card-header::before {
    left: 100%;
}

.card-body {
    padding: 2rem;
    color: var(--dark-color);
}

.card-footer {
    background: var(--gray-50);
    border: none;
    padding: 1.25rem 2rem;
    border-top: 1px solid var(--gray-200);
}

/* بطاقات ملونة خاصة */
.card-primary {
    border-left: 5px solid var(--primary-color);
    box-shadow: 0 10px 15px -3px rgba(99, 102, 241, 0.1);
}

.card-success {
    border-left: 5px solid var(--success-color);
    box-shadow: 0 10px 15px -3px rgba(16, 185, 129, 0.1);
}

.card-danger {
    border-left: 5px solid var(--danger-color);
    box-shadow: 0 10px 15px -3px rgba(239, 68, 68, 0.1);
}

.card-warning {
    border-left: 5px solid var(--warning-color);
    box-shadow: 0 10px 15px -3px rgba(245, 158, 11, 0.1);
}

.card-info {
    border-left: 5px solid var(--info-color);
    box-shadow: 0 10px 15px -3px rgba(6, 182, 212, 0.1);
}

/* أنماط الجداول المذهلة */
.table-container {
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--box-shadow-lg);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    margin-bottom: 2rem;
}

.table {
    margin: 0;
    font-size: var(--font-size-sm);
    color: var(--dark-color);
}

.table thead th {
    background: var(--gradient-primary);
    color: white;
    border: none;
    font-weight: 700;
    padding: 1.5rem 1.25rem;
    position: relative;
    text-align: center;
    font-size: var(--font-size-sm);
    letter-spacing: 0.025em;
    text-transform: uppercase;
}

.table thead th::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: rgba(255, 255, 255, 0.3);
}

.table thead th a {
    color: white;
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    transition: var(--transition);
}

.table thead th a:hover {
    color: rgba(255, 255, 255, 0.8);
    transform: scale(1.05);
}

.table tbody tr {
    transition: var(--transition);
    border-bottom: 1px solid var(--gray-200);
}

.table tbody tr:hover {
    background: linear-gradient(90deg, rgba(99, 102, 241, 0.05) 0%, rgba(139, 92, 246, 0.05) 100%);
    transform: scale(1.01);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.table tbody tr:last-child {
    border-bottom: none;
}

.table td {
    padding: 1.25rem;
    vertical-align: middle;
    border: none;
    text-align: center;
    color: var(--dark-color);
    font-weight: 500;
}

.table-striped tbody tr:nth-of-type(odd) {
    background: linear-gradient(90deg, rgba(248, 250, 252, 0.5) 0%, rgba(241, 245, 249, 0.5) 100%);
}

/* أنماط خاصة للجداول */
.table-modern {
    border-collapse: separate;
    border-spacing: 0 0.5rem;
}

.table-modern tbody tr {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.table-modern tbody tr td:first-child {
    border-radius: var(--border-radius) 0 0 var(--border-radius);
}

.table-modern tbody tr td:last-child {
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

/* أنماط الشارات */
.badge {
    font-size: 0.8rem;
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-weight: 500;
    letter-spacing: 0.5px;
}

.badge-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.badge-danger {
    background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
}

.badge-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    color: var(--dark-color);
}

.badge-info {
    background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
}

.badge-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

/* أنماط النماذج */
.form-control, .form-select {
    border-radius: 0.75rem;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: var(--transition);
    font-size: 0.95rem;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-label {
    font-weight: 500;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.input-group {
    border-radius: 0.75rem;
    overflow: hidden;
}

.input-group-text {
    background: var(--light-color);
    border: 2px solid #e9ecef;
    color: var(--dark-color);
}

/* أنماط التنبيهات */
.alert {
    border-radius: var(--border-radius);
    border: none;
    padding: 1.25rem;
    margin-bottom: 1.5rem;
    font-weight: 500;
}

.alert-success {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(32, 201, 151, 0.1) 100%);
    color: #155724;
    border-left: 4px solid #28a745;
}

.alert-danger {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1) 0%, rgba(232, 62, 140, 0.1) 100%);
    color: #721c24;
    border-left: 4px solid #dc3545;
}

.alert-warning {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(253, 126, 20, 0.1) 100%);
    color: #856404;
    border-left: 4px solid #ffc107;
}

.alert-info {
    background: linear-gradient(135deg, rgba(23, 162, 184, 0.1) 0%, rgba(111, 66, 193, 0.1) 100%);
    color: #0c5460;
    border-left: 4px solid #17a2b8;
}

/* أنماط الإشعارات */
.notification-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: var(--danger-color);
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    animation: pulse 2s infinite;
    z-index: 10;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.notification-dropdown {
    min-width: 350px;
    max-height: 400px;
    overflow-y: auto;
}

.notification-item {
    padding: 1rem;
    border-bottom: 1px solid #eee;
    transition: var(--transition);
}

.notification-item:hover {
    background-color: var(--light-color);
}

.notification-item.unread {
    background-color: rgba(102, 126, 234, 0.05);
    border-left: 3px solid var(--primary-color);
}

/* أنماط البحث والفلتر */
.search-filter-container {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.filter-section {
    background: var(--light-color);
    border-radius: 0.75rem;
    padding: 1rem;
    margin-top: 1rem;
}

.filter-toggle {
    cursor: pointer;
    user-select: none;
}

.active-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 1rem;
}

.filter-tag {
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filter-tag .remove {
    cursor: pointer;
    font-weight: bold;
}

/* أنماط الصفحات */
.pagination {
    justify-content: center;
    margin-top: 2rem;
}

.page-link {
    border-radius: 0.5rem;
    margin: 0 0.25rem;
    border: 2px solid #e9ecef;
    color: var(--primary-color);
    padding: 0.75rem 1rem;
    transition: var(--transition);
}

.page-link:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.page-item.active .page-link {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border-color: var(--primary-color);
}

/* أنماط الشريط الجانبي المذهل */
.sidebar {
    min-height: calc(100vh - 80px);
    background: var(--gradient-primary);
    box-shadow: var(--box-shadow-xl);
    position: sticky;
    top: 80px;
    border-radius: 0 var(--border-radius-lg) var(--border-radius-lg) 0;
    overflow: hidden;
    backdrop-filter: blur(20px);
}

.sidebar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
    pointer-events: none;
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.9);
    padding: 1.25rem 2rem;
    margin: 0.5rem 1.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
    font-weight: 600;
    font-size: var(--font-size-sm);
    display: flex;
    align-items: center;
    gap: 1rem;
    position: relative;
    overflow: hidden;
    text-decoration: none;
    border: 1px solid transparent;
}

.sidebar .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: var(--transition);
}

.sidebar .nav-link:hover::before {
    left: 100%;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    color: white;
    background: rgba(255, 255, 255, 0.2);
    transform: translateX(-8px) scale(1.02);
    box-shadow: var(--box-shadow-lg);
    border-color: rgba(255, 255, 255, 0.3);
}

.sidebar .nav-link.active {
    background: rgba(255, 255, 255, 0.25);
    box-shadow: inset 4px 0 0 rgba(255, 255, 255, 0.8);
}

.sidebar .nav-link i {
    width: 24px;
    height: 24px;
    text-align: center;
    font-size: var(--font-size-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.sidebar .nav-link:hover i {
    transform: scale(1.2) rotate(5deg);
}

/* عنوان الشريط الجانبي */
.sidebar-header {
    padding: 2rem;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    margin-bottom: 1rem;
}

.sidebar-header h4 {
    color: white;
    font-weight: 700;
    margin: 0;
    font-size: var(--font-size-xl);
}

/* أنماط المحتوى الرئيسي المذهل */
.main-content {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow-xl);
    margin: 2rem;
    padding: 3rem;
    min-height: calc(100vh - 160px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.main-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: var(--gradient-primary);
}

/* الشريط العلوي المذهل */
.navbar {
    background: var(--gradient-primary) !important;
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--box-shadow-lg);
    padding: 1.25rem 2rem;
    position: sticky;
    top: 0;
    z-index: 1000;
}

.navbar-brand {
    color: white !important;
    font-weight: 700;
    font-size: var(--font-size-xl);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.navbar-brand:hover {
    color: rgba(255, 255, 255, 0.9) !important;
    transform: scale(1.05);
}

.navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 600;
    padding: 0.75rem 1.5rem !important;
    border-radius: var(--border-radius);
    transition: var(--transition);
    margin: 0 0.25rem;
    position: relative;
}

.navbar-nav .nav-link::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: white;
    transition: var(--transition);
    transform: translateX(-50%);
}

.navbar-nav .nav-link:hover::before,
.navbar-nav .nav-link.active::before {
    width: 80%;
}

.navbar-nav .nav-link:hover {
    color: white !important;
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.navbar-toggler {
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: var(--border-radius);
    padding: 0.5rem;
}

.navbar-toggler:focus {
    box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.8%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

/* أنماط الإحصائيات المذهلة */
.stats-card {
    background: var(--gradient-primary);
    color: white;
    border-radius: var(--border-radius-lg);
    padding: 2.5rem 2rem;
    text-align: center;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(20px);
}

.stats-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: statsGlow 4s ease-in-out infinite;
}

@keyframes statsGlow {
    0%, 100% { transform: scale(1) rotate(0deg); opacity: 0.5; }
    50% { transform: scale(1.1) rotate(180deg); opacity: 0.8; }
}

.stats-card:hover {
    transform: translateY(-10px) scale(1.05);
    box-shadow: var(--box-shadow-xl);
}

.stats-card .icon {
    font-size: 3.5rem;
    margin-bottom: 1.5rem;
    opacity: 0.9;
    position: relative;
    z-index: 2;
    transition: var(--transition);
}

.stats-card:hover .icon {
    transform: scale(1.2) rotate(10deg);
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
}

.stats-card .number {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 0.75rem;
    position: relative;
    z-index: 2;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stats-card .label {
    font-size: var(--font-size-base);
    opacity: 0.95;
    font-weight: 600;
    position: relative;
    z-index: 2;
    letter-spacing: 0.025em;
}

/* بطاقات إحصائيات ملونة */
.stats-card-success {
    background: var(--gradient-success);
}

.stats-card-danger {
    background: var(--gradient-danger);
}

.stats-card-warning {
    background: var(--gradient-warning);
}

.stats-card-info {
    background: var(--gradient-info);
}

/* أنماط الرسوم البيانية */
.chart-container {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

/* أنماط الأزرار العائمة */
.floating-btn {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border: none;
    box-shadow: var(--box-shadow-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    transition: var(--transition);
    z-index: 1000;
}

.floating-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 1rem 2rem rgba(0,0,0,0.2);
}

/* أنماط التحميل */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.3s ease;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* أنماط الشريط المخصص للتمرير */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-color);
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        top: 76px;
        left: -100%;
        width: 280px;
        z-index: 1000;
        transition: var(--transition);
    }

    .sidebar.show {
        left: 0;
    }

    .main-content {
        margin: 1rem;
        padding: 1rem;
    }

    .table-responsive {
        border-radius: var(--border-radius);
    }

    .floating-btn {
        bottom: 1rem;
        right: 1rem;
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .stats-card {
        margin-bottom: 1rem;
    }

    .notification-dropdown {
        min-width: 300px;
    }
}

@media (max-width: 576px) {
    .main-content {
        margin: 0.5rem;
        padding: 1rem;
    }

    .card-body {
        padding: 1rem;
    }

    .table td, .table th {
        padding: 0.75rem 0.5rem;
        font-size: 0.8rem;
    }

    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
}

/* دعم الوضع المظلم */
@media (prefers-color-scheme: dark) {
    .main-content {
        background: #2d3748;
        color: white;
    }

    .card {
        background: #4a5568;
        color: white;
    }

    .table {
        color: white;
    }

    .table tbody tr:hover {
        background-color: rgba(255, 255, 255, 0.05);
    }

    .form-control, .form-select {
        background: #4a5568;
        border-color: #718096;
        color: white;
    }

    .form-control:focus, .form-select:focus {
        background: #4a5568;
        border-color: var(--primary-color);
        color: white;
    }
}

/* أنماط الطباعة */
@media print {
    .navbar, .sidebar, .floating-btn, .alert {
        display: none !important;
    }

    .main-content {
        margin: 0;
        padding: 0;
        box-shadow: none;
        border-radius: 0;
    }

    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }

    .btn {
        display: none !important;
    }
}

/* تحسينات الأداء */
.card, .btn, .table tbody tr {
    will-change: transform;
}

/* أنماط إضافية للتحسين */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.shadow-custom {
    box-shadow: var(--box-shadow-lg);
}

.border-radius-custom {
    border-radius: var(--border-radius);
}

.transition-custom {
    transition: var(--transition);
}
