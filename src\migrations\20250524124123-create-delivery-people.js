'use strict';
module.exports = {
  up: async (qi, Sequelize) => {
    await qi.createTable('DeliveryPeople', {
      id: { type: Sequelize.INTEGER, autoIncrement: true, primaryKey: true },
      name: { type: Sequelize.STRING, allowNull: false },
      phoneNumber: { type: Sequelize.STRING, allowNull: true },
      status: { type: Sequelize.ENUM('active','inactive'), defaultValue: 'active' },
      createdAt: { type: Sequelize.DATE, allowNull: false, defaultValue: Sequelize.literal('CURRENT_TIMESTAMP') },
      updatedAt: { type: Sequelize.DATE, allowNull: false, defaultValue: Sequelize.literal('CURRENT_TIMESTAMP') }
    });
  },
  down: async (qi) => {
    await qi.dropTable('DeliveryPeople');
  }
};