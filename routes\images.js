const express = require('express');
const router = express.Router();
const imagesController = require('../controllers/ImagesController');

router.get('/', imagesController.index.bind(imagesController));
router.get('/create', imagesController.create.bind(imagesController));
router.post('/', imagesController.store.bind(imagesController));
router.post('/:id/delete', imagesController.delete.bind(imagesController));

module.exports = router;
