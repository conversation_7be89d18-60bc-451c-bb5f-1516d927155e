const express = require('express');
const router = express.Router();
const storesController = require('../controllers/StoresController');
const { controller: storeAuthController } = require('../controllers/StoreAuthController');
const upload = require('../middleware/upload');
const deliveryController = require('../controllers/DeliveryController');
const deliveryPersonController = require('../controllers/DeliveryPersonController');
const customerController = require('../controllers/CustomersController');
const requireStoreAuth = (req, res, next) => {
  if (!req.session.storeId) {
    return res.redirect('/store/login');
  }
  next();
};

router.get('/login', storeAuthController.showLogin.bind(storeAuthController));
router.get('/register', storeAuthController.showRegister.bind(storeAuthController));
router.post('/login', storeAuthController.login.bind(storeAuthController));
router.post('/register', storeAuthController.register.bind(storeAuthController));
router.get('/logout', storeAuthController.logout.bind(storeAuthController));
router.get('/dashboard', requireStoreAuth, storeAuthController.dashboard.bind(storeAuthController));
router.get('/products', requireStoreAuth, storeAuthController.products.bind(storeAuthController));
router.get('/products/:id/edit', requireStoreAuth, storeAuthController.editProduct.bind(storeAuthController));
router.post('/products/:id', requireStoreAuth, upload.array('images', 10),storeAuthController.updateProduct.bind(storeAuthController));
router.get('/orders', requireStoreAuth, storeAuthController.orders.bind(storeAuthController));
router.get('/orders/pending', requireStoreAuth, storeAuthController.pendingOrders.bind(storeAuthController));
router.get('/orders/:id', requireStoreAuth, storeAuthController.show.bind(storeAuthController));

router.post('/orders/:id/accept', requireStoreAuth, storeAuthController.acceptOrder.bind(storeAuthController));

router.get('/',requireStoreAuth,storesController.index.bind(storesController));
router.get('/:id/edit', requireStoreAuth,storesController.edit.bind(storesController));
router.post('/:id', requireStoreAuth, storesController.update.bind(storesController));
router.post('/:id/delete', requireStoreAuth, storesController.delete.bind(storesController));

router.get('/customers/:id', requireStoreAuth, customerController.show.bind(customerController));

router.get('/deliveries', requireStoreAuth, deliveryController.index.bind(deliveryController));
//router.get('/deliveries/create', requireStoreAuth, deliveryController.createForm.bind(deliveryController));
//router.post('/deliveries', requireStoreAuth, deliveryController.create.bind(deliveryController));
router.get('/deliveries/:id/edit', requireStoreAuth, deliveryController.editForm.bind(deliveryController));
router.post('/deliveries/:id', requireStoreAuth, deliveryController.update.bind(deliveryController));
router.post('/deliveries/:id/delete', requireStoreAuth, deliveryController.delete.bind(deliveryController));

// نموذج إضافة توصيل مع اختيار سائق تلقائي
router.get('/deliveries/create', requireStoreAuth, deliveryController.createForm.bind(deliveryController));

// حفظ التوصيل من النموذج
router.post('/deliveries', requireStoreAuth, deliveryController.create.bind(deliveryController));

// إنشاء توصيل تلقائي من طلب (اختياري، لو تريده)
router.post('/orders/:orderId/delivery', requireStoreAuth, deliveryController.createFromOrder.bind(deliveryController));

// عرض جميع التوصيلات للمتجر
router.get('/:storeId/deliveries', requireStoreAuth, deliveryController.index.bind(deliveryController));

// نموذج إنشاء توصيل جديد (مثلاً مرتبط بسائق)
router.get('/:storeId/deliveries/create', requireStoreAuth, deliveryController.createForm.bind(deliveryController));
// إنشاء التوصيل (POST)
router.post('/:storeId/deliveries',requireStoreAuth, deliveryController.create.bind(deliveryController));


// تحديث حالة التوصيل
router.post('/deliveries/:deliveryId/complete',requireStoreAuth, deliveryController.markComplete.bind(deliveryController));

// عرض تفاصيل التوصيل لطلب معين
router.get('/orders/:orderId/delivery', requireStoreAuth, deliveryController.showDelivery.bind(deliveryController));



// عرض نموذج تعديل توصيل
router.get('/deliveries/:id/edit', requireStoreAuth, deliveryController.editForm.bind(deliveryController));
// تحديث بيانات التوصيل
router.post('/deliveries/:id/update', requireStoreAuth, deliveryController.update.bind(deliveryController));
// حذف توصيل
router.post('/deliveries/:id/delete', requireStoreAuth, deliveryController.delete.bind(deliveryController));
////////////********************************** */
// عرض جميع التوصيلات الخاصة بسائق معين داخل متجر
router.get('/:storeId/delivery-people/:personId/deliveries', requireStoreAuth, deliveryController.personDeliveries.bind(deliveryController));

// عرض جميع السائقين التابعين لمتجر
router.get('/:storeId/delivery-people', requireStoreAuth, deliveryPersonController.index.bind(deliveryPersonController));

// عرض صفحة إضافة سائق
router.get('/:storeId/delivery-people/create', requireStoreAuth, deliveryPersonController.createForm.bind(deliveryPersonController));

// إنشاء سائق
router.post('/:storeId/delivery-people', requireStoreAuth, deliveryPersonController.create.bind(deliveryPersonController));

// عرض صفحة تعديل سائق
router.get('/:storeId/delivery-people/:id/edit', requireStoreAuth, deliveryPersonController.editForm.bind(deliveryPersonController));

// تعديل بيانات السائق
router.post('/:storeId/delivery-people/:id', requireStoreAuth, deliveryPersonController.update.bind(deliveryPersonController));
router.post('/:storeId/delivery-people/:id/delete', requireStoreAuth, deliveryPersonController.delete.bind(deliveryPersonController));
module.exports = router;
