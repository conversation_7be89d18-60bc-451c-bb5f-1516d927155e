const express = require('express');
const router = express.Router();
const storesController = require('../controllers/StoresController');
const { controller: storeAuthController } = require('../controllers/StoreAuthController');
const upload = require('../middleware/upload');
const deliveryController = require('../controllers/DeliveryController');
const deliveryPersonController = require('../controllers/DeliveryPersonController');
const customerController = require('../controllers/CustomersController');
const { auth } = require('../middleware/auth');

router.get('/login', storeAuthController.showLogin.bind(storeAuthController));
router.get('/register', storeAuthController.showRegister.bind(storeAuthController));
router.post('/login', storeAuthController.login.bind(storeAuthController));
router.post('/register', storeAuthController.register.bind(storeAuthController));
router.get('/logout', storeAuthController.logout.bind(storeAuthController));

router.get('/dashboard', auth.store, storesController.dashboard.bind(storeAuthController));
router.get('/products', auth.store, storesController.products.bind(storeAuthController));
router.get('/products/:id/edit', auth.store, storesController.editProduct.bind(storeAuthController));
router.post('/products/:id', auth.store, upload.array('images', 10),storesController.updateProduct.bind(storeAuthController));
router.get('/orders', auth.store, storesController.orders.bind(storeAuthController));
router.get('/orders/pending', auth.store, storesController.pendingOrders.bind(storeAuthController));
router.get('/orders/:id', auth.store, storesController.show.bind(storeAuthController));

router.post('/orders/:id/accept', auth.store, storesController.acceptOrder.bind(storeAuthController));

router.get('/', auth.store, storesController.index.bind(storesController));
router.get('/:id/edit', auth.store, storesController.edit.bind(storesController));
router.post('/:id', auth.store, storesController.update.bind(storesController));
router.post('/:id/delete', auth.store, storesController.delete.bind(storesController));

router.get('/customers/:id', auth.store, customerController.show.bind(customerController));

router.get('/deliveries', auth.store, deliveryController.index.bind(deliveryController));
router.get('/deliveries/:id/edit', auth.store, deliveryController.editForm.bind(deliveryController));
router.post('/deliveries/:id', auth.store, deliveryController.update.bind(deliveryController));
router.post('/deliveries/:id/delete', auth.store, deliveryController.delete.bind(deliveryController));

// نموذج إضافة توصيل مع اختيار سائق تلقائي
router.get('/deliveries/create', auth.store, deliveryController.createForm.bind(deliveryController));

// حفظ التوصيل من النموذج
router.post('/deliveries', auth.store, deliveryController.create.bind(deliveryController));

// إنشاء توصيل تلقائي من طلب (اختياري، لو تريده)
router.post('/orders/:orderId/delivery', auth.store, deliveryController.createFromOrder.bind(deliveryController));

// عرض جميع التوصيلات للمتجر
router.get('/:storeId/deliveries', auth.store, deliveryController.index.bind(deliveryController));

// نموذج إنشاء توصيل جديد (مثلاً مرتبط بسائق)
router.get('/:storeId/deliveries/create', auth.store, deliveryController.createForm.bind(deliveryController));
// إنشاء التوصيل (POST)
router.post('/:storeId/deliveries', auth.store, deliveryController.create.bind(deliveryController));


// تحديث حالة التوصيل
router.post('/deliveries/:deliveryId/complete', auth.store, deliveryController.markComplete.bind(deliveryController));

// عرض تفاصيل التوصيل لطلب معين
router.get('/orders/:orderId/delivery', auth.store, deliveryController.showDelivery.bind(deliveryController));



// عرض نموذج تعديل توصيل
router.get('/deliveries/:id/edit', auth.store, deliveryController.editForm.bind(deliveryController));
// تحديث بيانات التوصيل
router.post('/deliveries/:id/update', auth.store, deliveryController.update.bind(deliveryController));
// حذف توصيل
router.post('/deliveries/:id/delete', auth.store, deliveryController.delete.bind(deliveryController));
////////////********************************** */
// عرض جميع التوصيلات الخاصة بسائق معين داخل متجر
router.get('/:storeId/delivery-people/:personId/deliveries', auth.store, deliveryController.personDeliveries.bind(deliveryController));

// عرض جميع السائقين التابعين لمتجر
router.get('/:storeId/delivery-people', auth.store, deliveryPersonController.index.bind(deliveryPersonController));

// عرض صفحة إضافة سائق
router.get('/:storeId/delivery-people/create', auth.store, deliveryPersonController.createForm.bind(deliveryPersonController));

// إنشاء سائق
router.post('/:storeId/delivery-people', auth.store, deliveryPersonController.create.bind(deliveryPersonController));

// عرض صفحة تعديل سائق
router.get('/:storeId/delivery-people/:id/edit', auth.store, deliveryPersonController.editForm.bind(deliveryPersonController));

// تعديل بيانات السائق
router.post('/:storeId/delivery-people/:id', auth.store, deliveryPersonController.update.bind(deliveryPersonController));
router.post('/:storeId/delivery-people/:id/delete', auth.store, deliveryPersonController.delete.bind(deliveryPersonController));
module.exports = router;
