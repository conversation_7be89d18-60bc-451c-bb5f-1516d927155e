'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class Product extends Model {
    static associate(models) {
      Product.belongsTo(models.Store, {
        foreignKey: 'storeId',
        as: 'store'
      });
      Product.belongsTo(models.Category, {
        foreignKey: 'categoryId',
        as: 'category'
      });
      Product.hasMany(models.OrderDetail, {
        foreignKey: 'productId',
        as: 'orderDetails'
      });
      Product.hasMany(models.Image, {
        foreignKey: 'productId',
        as: 'images'
      });

      // العلاقة مع السلة
      Product.hasMany(models.Cart, {
        foreignKey: 'productId',
        as: 'cartItems',
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE'
      });
    }

    // Calculate final price including discount
    getFinalPrice() {
      if (!this.discountPercentage || this.discountPercentage === 0) {
        return this.price;
      }

      const now = new Date();
      if (this.discountStartDate && this.discountStartDate > now) {
        return this.price;
      }
      if (this.discountEndDate && this.discountEndDate < now) {
        return this.price;
      }

      return this.price * (1 - this.discountPercentage / 100);
    }
  }

  Product.init({
    storeId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Stores',
        key: 'id'
      }
    },
    categoryId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'Categories',
        key: 'id'
      }
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    barcode: {
      type: DataTypes.STRING,
      unique: true
    },
    quantity: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    price: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false
    },
    status: {
      type: DataTypes.ENUM('active', 'inactive'),
      allowNull: false,
      defaultValue: 'active'
    },
    featured: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false
    },
    discountPercentage: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: false,
      defaultValue: 0
    },
    discountStartDate: {
      type: DataTypes.DATE,
      allowNull: true
    },
    discountEndDate: {
      type: DataTypes.DATE,
      allowNull: true
    },
    notes: {
      type: DataTypes.TEXT
    }
  }, {
    sequelize,
    modelName: 'Product',
    // Add hooks for validation
    hooks: {
      beforeSave: (product) => {
        if (product.discountPercentage < 0 || product.discountPercentage > 100) {
          throw new Error('Discount percentage must be between 0 and 100');
        }
      }
    }
  });

  return Product;
};