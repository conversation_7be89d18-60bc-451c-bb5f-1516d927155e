{"compilerOptions": {"target": "ES2021", "module": "commonjs", "lib": ["ES2021"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "typeRoots": ["./node_modules/@types", "./src/types"], "sourceMap": true, "declaration": true, "removeComments": true, "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "emitDecoratorMetadata": true}, "include": ["src/**/*", "src/middleware"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]}