<div class="container">
    <h1>Order #<%= order.id %></h1>

    <h4>Customer: <%= order.customer ? order.customer.name : 'N/A' %></h4>
    <h4>Store: <%= order.store ? order.store.name : 'N/A' %></h4>
    <h4>Status: <%= order.status %></h4>
    <h4>Created At: <%= order.createdAt.toLocaleString() %></h4>

    <hr>

    <h3>Order Items</h3>
    <table class="table table-bordered">
        <thead>
            <tr>
                <th>#</th>
                <th>Product</th>
                <th>Quantity</th>
                <th>Total Price</th>
                <th>Notes</th>
            </tr>
        </thead>
        <tbody>
            <% order.orderDetails.forEach((detail, index) => { %>
                <tr>
                    <td><%= index + 1 %></td>
                    <td><%= detail.product ? detail.product.name : 'N/A' %></td>
                    <td><%= detail.quantity %></td>
                    <td><%= detail.totalPrice %></td>
                    <td><%= detail.notes || '-' %></td>
                </tr>
            <% }); %>
        </tbody>
    </table>
</div>
