<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
      <h1>Create New Store</h1>
      <a href="/admin/stores" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> Back to Stores
      </a>
    </div>
  
    <div class="card">
      <div class="card-body">
        <form action="/admin/stores" method="POST">
          <!-- اسم المستخدم -->
          <div class="mb-3">
            <label for="userName" class="form-label">Username</label>
            <input type="text" class="form-control" id="userName" name="userName" required>
          </div>
  
          <!-- اسم المتجر -->
          <div class="mb-3">
            <label for="name" class="form-label">Store Name</label>
            <input type="text" class="form-control" id="name" name="name" required>
          </div>
  
          <!-- كلمات السر -->
          <div class="mb-3">
            <label for="password" class="form-label">Password</label>
            <input type="password" class="form-control" id="password" name="password" required>
          </div>
  
          <!-- اختيار متعدد للأقسام -->
          <div class="mb-3">
            <label for="categoryIds" class="form-label">Categories</label>
            <select class="form-select" id="categoryIds" name="categoryIds" multiple required>
              <% categories.forEach(cat => { %>
                <option value="<%= cat.id %>"><%= cat.name %></option>
              <% }) %>
            </select>
            <small class="form-text text-muted">Hold Ctrl (Cmd on Mac) to select multiple</small>
          </div>
  
          <!-- المناطق -->
          <div class="mb-3">
            <label for="areaId" class="form-label">Area</label>
            <select class="form-select" id="areaId" name="areaId" required>
              <option value="">Select Area</option>
              <% areas.forEach(area => { %>
                <option value="<%= area.id %>"><%= area.name %></option>
              <% }) %>
            </select>
          </div>
  
          <!-- العنوان -->
          <div class="mb-3">
            <label for="address" class="form-label">Address</label>
            <textarea class="form-control" id="address" name="address" rows="3" required></textarea>
          </div>
  
          <!-- رقم الهاتف -->
          <div class="mb-3">
            <label for="phoneNumber" class="form-label">Phone Number</label>
            <input type="text" class="form-control" id="phoneNumber" name="phoneNumber">
          </div>
  
          <!-- ملاحظات -->
          <div class="mb-3">
            <label for="notes" class="form-label">Notes</label>
            <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
          </div>
  
          <div class="d-grid">
            <button type="submit" class="btn btn-primary">Create Store</button>
          </div>
        </form>
      </div>
    </div>
  </div>
  