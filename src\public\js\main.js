/**
 * ملف JavaScript بسيط لضمان عمل الموقع
 */

// إخفاء شاشة التحميل فوراً
document.addEventListener('DOMContentLoaded', function() {
    // إخفاء شاشة التحميل
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay) {
        loadingOverlay.style.display = 'none';
    }
    
    console.log('✅ تم تحميل الصفحة بنجاح');
});

// إخفاء شاشة التحميل عند تحميل النافذة
window.addEventListener('load', function() {
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay) {
        loadingOverlay.style.display = 'none';
    }
});

// إخفاء التنبيهات تلقائياً بعد 5 ثوان
setTimeout(function() {
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(function(alert) {
        if (alert.classList.contains('alert-dismissible')) {
            try {
                const closeBtn = alert.querySelector('.btn-close');
                if (closeBtn) {
                    closeBtn.click();
                } else {
                    alert.style.display = 'none';
                }
            } catch (e) {
                alert.style.display = 'none';
            }
        }
    });
}, 5000);

// تأكيد الحذف
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('delete-btn') || e.target.closest('.delete-btn')) {
        if (!confirm('هل أنت متأكد من أنك تريد حذف هذا العنصر؟')) {
            e.preventDefault();
            return false;
        }
    }
});

// تحسين تجربة المستخدم للنماذج
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثيرات للأزرار
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(function(btn) {
        btn.addEventListener('click', function() {
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 100);
        });
    });
    
    // تحسين النماذج
    const forms = document.querySelectorAll('form');
    forms.forEach(function(form) {
        form.addEventListener('submit', function(e) {
            const submitBtn = form.querySelector('button[type="submit"], input[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري المعالجة...';
                
                // إعادة تفعيل الزر بعد 5 ثوان في حالة عدم انتقال الصفحة
                setTimeout(() => {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = submitBtn.getAttribute('data-original-text') || 'حفظ';
                }, 5000);
            }
        });
    });
});

// وظائف مساعدة
window.SmartStore = {
    // عرض رسالة تأكيد
    confirm: function(message, callback) {
        if (confirm(message || 'هل أنت متأكد؟')) {
            if (typeof callback === 'function') {
                callback();
            }
            return true;
        }
        return false;
    },
    
    // عرض تنبيه
    alert: function(message, type) {
        type = type || 'info';
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        const container = document.querySelector('.container, .container-fluid');
        if (container) {
            container.insertBefore(alertDiv, container.firstChild);
            
            // إخفاء التنبيه بعد 5 ثوان
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }
    },
    
    // إعادة تحميل الصفحة
    reload: function() {
        window.location.reload();
    },
    
    // الانتقال لصفحة أخرى
    redirect: function(url) {
        window.location.href = url;
    }
};

// تحسين الأداء
document.addEventListener('DOMContentLoaded', function() {
    // تحسين الصور
    const images = document.querySelectorAll('img');
    images.forEach(function(img) {
        img.addEventListener('error', function() {
            this.src = '/images/placeholder.png';
            this.alt = 'صورة غير متاحة';
        });
    });
    
    // تحسين الروابط
    const links = document.querySelectorAll('a[href]');
    links.forEach(function(link) {
        // تأكد من أن الروابط تعمل بشكل صحيح
        link.addEventListener('click', function(e) {
            const href = this.getAttribute('href');
            if (href && href !== '#' && href !== 'javascript:void(0)') {
                // السماح للرابط بالعمل بشكل طبيعي
                console.log('انتقال إلى:', href);
            }
        });
    });
});

// إصلاح مشاكل Bootstrap إذا لم يتم تحميله
if (typeof bootstrap === 'undefined') {
    console.warn('Bootstrap غير محمل، سيتم استخدام وظائف بديلة');
    
    // وظائف بديلة بسيطة
    window.bootstrap = {
        Alert: function(element) {
            return {
                close: function() {
                    element.style.display = 'none';
                }
            };
        }
    };
}

console.log('🚀 تم تحميل JavaScript بنجاح');
