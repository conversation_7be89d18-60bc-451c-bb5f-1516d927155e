const fs = require('fs');
const path = require('path');

const dirPath = __dirname;

fs.readdirSync(dirPath).forEach(file => {
    const filePath = path.join(dirPath, file);
    if (file !== 'index.js' && file.endsWith('.js')) {
        try {
            require(filePath);
            console.log(`✅ ${file} => OK`);
        } catch (err) {
            console.error(`❌ ${file} =>`, err.message);
        }
    }
});
