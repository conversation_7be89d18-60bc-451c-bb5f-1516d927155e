<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Customers</h1>
        <a href="/admin/customers/create" class="btn btn-primary">Add New Customer</a>
    </div>

    <div class="table-responsive">
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Name</th>
                    <th>Phone Number</th>
                    <th>Areas</th>
                    <th>Discount Rate</th>
                    <th>Orders</th>
                    <th>Status</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <% customers.forEach(customer => { %>
                    <tr>
                        <td><%= customer.id %></td>
                        <td><%= customer.name %></td>
                        <td><%= customer.phoneNumber || 'N/A' %></td>
                        <td>
                            <% if (customer.areas && customer.areas.length > 0) { %>
                                <%= customer.areas.map(area => area.name).join(', ') %>
                            <% } else { %>
                                No Areas
                            <% } %>
                        </td>
                        <td><%= customer.discountRate ? customer.discountRate + '%' : 'N/A' %></td>
                        <td><%= customer.orders ? customer.orders.length : 0 %></td>
                        <td>
                            <% if (customer.status === 'active') { %>
                                <span class="badge bg-success">Active</span>
                            <% } else if (customer.status === 'inactive') { %>
                                <span class="badge bg-danger">Inactive</span>
                            <% } else if (customer.status === 'pending') { %>
                                <span class="badge bg-warning text-dark">Pending</span>
                            <% } else { %>
                                <span class="badge bg-secondary">Unknown</span>
                            <% } %>
                        </td>
                        <td class="action-buttons">
                            <form action="/admin/customers/<%= customer.id %>/status" method="POST" class="d-inline">
                                <input type="hidden" name="status" value="active">
                                <button type="submit" class="btn btn-sm btn-success">
                                    <i class="fas fa-check"></i> Activate
                                </button>
                            </form>
                            <a href="/admin/customers/<%= customer.id %>/edit" class="btn btn-sm btn-warning">
                                <i class="fas fa-edit"></i> Edit
                            </a>
                            <form action="/admin/customers/<%= customer.id %>/delete" method="POST" class="d-inline">
                                <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this customer?')">
                                    <i class="fas fa-trash"></i> Delete
                                </button>
                            </form>
                        </td>
                    </tr>
                <% }); %>
            </tbody>
        </table>
    </div>

    <% if (customers.length === 0) { %>
        <div class="alert alert-info">No customers found.</div>
    <% } %>
</div> 