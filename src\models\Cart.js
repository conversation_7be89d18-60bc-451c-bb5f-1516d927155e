'use strict';

const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class Cart extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // العلاقة مع العميل
      Cart.belongsTo(models.Customer, {
        foreignKey: 'customerId',
        as: 'customer',
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE'
      });

      // العلاقة مع المنتج
      Cart.belongsTo(models.Product, {
        foreignKey: 'productId',
        as: 'product',
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE'
      });
    }

    /**
     * حساب السعر الإجمالي
     */
    calculateTotalPrice() {
      return parseFloat(this.price) * parseInt(this.quantity);
    }

    /**
     * التحقق من صلاحية العنصر
     */
    isExpired() {
      if (!this.expiresAt) return false;
      return new Date() > new Date(this.expiresAt);
    }

    /**
     * تحديث السعر الإجمالي تلقائياً
     */
    async updateTotalPrice() {
      this.totalPrice = this.calculateTotalPrice();
      return await this.save();
    }

    /**
     * تحديث الكمية مع إعادة حساب السعر
     */
    async updateQuantity(newQuantity) {
      this.quantity = newQuantity;
      this.totalPrice = this.calculateTotalPrice();
      return await this.save();
    }

    /**
     * تحديث السعر مع إعادة حساب الإجمالي
     */
    async updatePrice(newPrice) {
      this.price = newPrice;
      this.totalPrice = this.calculateTotalPrice();
      return await this.save();
    }

    /**
     * تعيين تاريخ انتهاء الصلاحية
     */
    setExpiration(days = 7) {
      const expirationDate = new Date();
      expirationDate.setDate(expirationDate.getDate() + days);
      this.expiresAt = expirationDate;
      return this;
    }

    /**
     * تنسيق البيانات للإرجاع في API
     */
    toJSON() {
      const values = Object.assign({}, this.get());
      
      return {
        id: values.id,
        customerId: values.customerId,
        productId: values.productId,
        quantity: values.quantity,
        price: parseFloat(values.price),
        totalPrice: parseFloat(values.totalPrice),
        sessionId: values.sessionId,
        isActive: values.isActive,
        expiresAt: values.expiresAt,
        notes: values.notes,
        createdAt: values.createdAt,
        updatedAt: values.updatedAt,
        // إضافة بيانات المنتج والعميل إذا كانت محملة
        product: values.product,
        customer: values.customer
      };
    }
  }

  Cart.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    customerId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        notNull: {
          msg: 'معرف العميل مطلوب'
        },
        isInt: {
          msg: 'معرف العميل يجب أن يكون رقم صحيح'
        }
      }
    },
    productId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        notNull: {
          msg: 'معرف المنتج مطلوب'
        },
        isInt: {
          msg: 'معرف المنتج يجب أن يكون رقم صحيح'
        }
      }
    },
    quantity: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1,
      validate: {
        notNull: {
          msg: 'الكمية مطلوبة'
        },
        isInt: {
          msg: 'الكمية يجب أن تكون رقم صحيح'
        },
        min: {
          args: [1],
          msg: 'الكمية يجب أن تكون أكبر من صفر'
        },
        max: {
          args: [999],
          msg: 'الكمية لا يمكن أن تتجاوز 999'
        }
      }
    },
    price: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      validate: {
        notNull: {
          msg: 'السعر مطلوب'
        },
        isDecimal: {
          msg: 'السعر يجب أن يكون رقم صحيح'
        },
        min: {
          args: [0],
          msg: 'السعر لا يمكن أن يكون سالب'
        }
      }
    },
    totalPrice: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      validate: {
        notNull: {
          msg: 'السعر الإجمالي مطلوب'
        },
        isDecimal: {
          msg: 'السعر الإجمالي يجب أن يكون رقم صحيح'
        },
        min: {
          args: [0],
          msg: 'السعر الإجمالي لا يمكن أن يكون سالب'
        }
      }
    },
    sessionId: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: {
          args: [0, 255],
          msg: 'معرف الجلسة لا يمكن أن يتجاوز 255 حرف'
        }
      }
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true
    },
    expiresAt: {
      type: DataTypes.DATE,
      allowNull: true,
      validate: {
        isDate: {
          msg: 'تاريخ انتهاء الصلاحية يجب أن يكون تاريخ صحيح'
        },
        isAfter: {
          args: new Date().toISOString(),
          msg: 'تاريخ انتهاء الصلاحية يجب أن يكون في المستقبل'
        }
      }
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
      validate: {
        len: {
          args: [0, 1000],
          msg: 'الملاحظات لا يمكن أن تتجاوز 1000 حرف'
        }
      }
    }
  }, {
    sequelize,
    modelName: 'Cart',
    tableName: 'Carts',
    timestamps: true,
    paranoid: false, // لا نحتاج soft delete للسلة
    indexes: [
      {
        unique: true,
        fields: ['customerId', 'productId'],
        name: 'unique_customer_product_cart'
      },
      {
        fields: ['customerId'],
        name: 'cart_customer_id_index'
      },
      {
        fields: ['productId'],
        name: 'cart_product_id_index'
      },
      {
        fields: ['sessionId'],
        name: 'cart_session_id_index'
      },
      {
        fields: ['isActive'],
        name: 'cart_is_active_index'
      },
      {
        fields: ['expiresAt'],
        name: 'cart_expires_at_index'
      }
    ],
    hooks: {
      // حساب السعر الإجمالي تلقائياً قبل الحفظ
      beforeCreate: (cart, options) => {
        cart.totalPrice = parseFloat(cart.price) * parseInt(cart.quantity);
      },
      beforeUpdate: (cart, options) => {
        if (cart.changed('price') || cart.changed('quantity')) {
          cart.totalPrice = parseFloat(cart.price) * parseInt(cart.quantity);
        }
      },
      // تعيين تاريخ انتهاء الصلاحية تلقائياً
      beforeCreate: (cart, options) => {
        if (!cart.expiresAt) {
          const expirationDate = new Date();
          expirationDate.setDate(expirationDate.getDate() + 7); // 7 أيام افتراضياً
          cart.expiresAt = expirationDate;
        }
      }
    }
  });

  return Cart;
};
