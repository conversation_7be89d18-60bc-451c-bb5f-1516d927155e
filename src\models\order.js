'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class Order extends Model {
    static associate(models) {
      Order.belongsTo(models.Store, {
        foreignKey: 'storeId',
        as: 'store'
      });
      Order.belongsTo(models.Customer, {
        foreignKey: 'customerId',
        as: 'customer'
      });
      Order.hasMany(models.OrderDetail, {
        foreignKey: 'orderId',
        as: 'orderDetails'
      });
      Order.hasOne(models.Delivery, {
        as: 'delivery',
        foreignKey: 'orderId'
      });
    }
  }

  Order.init({
    storeId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Stores',
        key: 'id'
      }
    },
    customerId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Customers',
        key: 'id'
      }
    },
    totalPrice: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0
    },
    totalAmount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0
    },
    status: {
      type: DataTypes.STRING,
      defaultValue: 'pending'
    },
    deliveryAddress: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    rating: DataTypes.INTEGER,
    notes: DataTypes.TEXT
  }, {
    sequelize,
    modelName: 'Order',
  });

  return Order;
};