const express = require('express');
const router = express.Router();
const CustomerApiController = require('../../controllers/api/CustomerApiController');
const CustomersController = require('../../controllers/CustomersController');
const { authenticateCustomer, rateLimiter ,optionalAuth} = require('../../middleware/apiAuth');
const CartService = require('../../services/CartService');

// Rate limiting for auth endpoints
const authLimiter = rateLimiter(5, 15 * 60 * 1000); // 5 attempts per 15 minutes

/**
 * مسارات المصادقة (بدون حاجة لتسجيل دخول)
 */

// تسجيل دخول العميل
router.post('/login', authLimiter, CustomerApiController.login);

// تسجيل عميل جديد
router.post('/register', authLimiter, CustomerApiController.register);

/**
 * مسارات محمية (تحتاج تسجيل دخول)
 */

// الحصول على بيانات العميل
router.get('/profile',authenticateCustomer, CustomerApiController.getProfile.bind(CustomerApiController));

// تحديث بيانات العميل
router.put('/profile', authenticateCustomer, CustomerApiController.updateProfile.bind(CustomerApiController));

router.post('/profile/address/add', CustomerApiController.addAddress.bind(CustomerApiController));

router.post('/profile/address/:id/delete', authenticateCustomer, CustomerApiController.deleteAddress.bind(CustomerApiController));

// تغيير كلمة المرور
router.put('/change-password', authenticateCustomer, CustomerApiController.changePassword);

// تسجيل الخروج
router.post('/logout', authenticateCustomer, CustomerApiController.logout);

/**
 * مسارات إضافية للعملاء
 */
// الصفحة الرئيسية (عامة - لا تحتاج مصادقة)
router.get('/Home', optionalAuth, CustomersController.getHome);
router.get('/categories', optionalAuth, CustomersController.getCategory);
router.get('/categories/:id', optionalAuth, CustomersController.getStoresByCategory);// متاجر حسب الفئة (عامة)
router.get('/stores/:id', optionalAuth, CustomersController.getStoreProducts);// منتجات متجر معين (عامة)
router.get('/products/:id', optionalAuth, CustomersController.getProductDetails);// تفاصيل منتج (عامة)

/**
 * مسارات السلة 
 */
router.get('/cart',authenticateCustomer, CustomersController.getCart);// عرض السلة
router.post('/cart/add',authenticateCustomer, CustomersController.addToCart);// إضافة للسلة
router.put('/cart/:id',authenticateCustomer, CustomersController.updateCartItem);// تحديث كمية في السلة
router.delete('/cart/:id',authenticateCustomer, CustomersController.removeFromCart);// حذف من السلة
router.delete('/cart',authenticateCustomer, CustomersController.clearCart);// مسح السلة بالكامل

/**
 * مسارات الدفع والطلبات (تحتاج مصادقة)
 */

// بيانات الدفع
router.get('/checkout',authenticateCustomer, CustomersController.getCheckoutData);

// معالجة الدفع وإنشاء الطلب
router.post('/checkout', CustomersController.processAddCheckout.bind(CustomersController));

// الحصول على طلبات العميل
router.get('/orders',authenticateCustomer, CustomersController.getOrders);

// التحقق من صحة التوكن
router.get('/verify-token', authenticateCustomer, (req, res) => {
    res.json({
        success: true,
        message: 'التوكن صحيح',
        data: {
            customer: {
                id: req.customer.id,
                name: req.customer.name,
                email: req.customer.email,
                phone: req.customer.phone
            }
        }
    });
});

// تجديد التوكن
router.post('/refresh-token', authenticateCustomer, (req, res) => {
    const jwt = require('jsonwebtoken');
    
    try {
        // إنشاء توكن جديد
        const newToken = jwt.sign(
            { 
                customerId: req.customer.id,
                email: req.customer.email,
                type: 'customer'
            },
            process.env.JWT_SECRET || 'your-secret-key',
            { expiresIn: '30d' }
        );

        res.json({
            success: true,
            message: 'تم تجديد التوكن بنجاح',
            data: { token: newToken }
        });

    } catch (error) {
        console.error('خطأ في تجديد التوكن:', error);
        res.status(500).json({
            success: false,
            message: 'حدث خطأ في الخادم',
            data: null
        });
    }
});

module.exports = router;
