<div class="container mt-4">
    <h2 class="mb-4">Your Shopping Cart</h2>

    <% if (cartItems.length === 0) { %>
      <div class="alert alert-info">Your cart is empty.</div>
    <% } else { %>
      <table class="table table-bordered">
        <thead>
          <tr>
            <th>Product</th>
            <th>Store</th>
            <th>Category</th>
            <th>Price</th>
            <th>Quantity</th>
            <th>Subtotal</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <% cartItems.forEach(item => { %>
            <tr>
              <td><a href="/customers/products/<%= item.product.id %>"><%= item.product.name %></a></td>
              <td><%= item.product.Store ? item.product.Store.name : 'N/A' %></td>
              <td><%= item.product.Category ? item.product.Category.name : 'N/A' %></td>
              <td>$<%= item.product.price.toFixed(2) %></td>
              <td><%= item.quantity %></td>
              <td>$<%= (item.product.price * item.quantity).toFixed(2) %></td>
              <td>
                <form action="/customers/cart/remove" method="POST" style="display:inline-block;">
                  <input type="hidden" name="productId" value="<%= item.product.id %>">
                  <button type="submit" class="btn btn-danger btn-sm">Remove</button>
                </form>
              </td>
            </tr>
          <% }) %>
        </tbody>
        <tfoot>
          <tr>
            <th colspan="5" class="text-end">Total:</th>
            <th colspan="2">$<%= totalPrice.toFixed(2) %></th>
          </tr>
        </tfoot>
      </table>
    <% } %>

    <div class="d-flex justify-content-between align-items-center mt-4">
        <a href="/customers/home" class="btn btn-outline-primary">
            <i class="fas fa-arrow-right"></i>
            مواصلة التسوق
        </a>
        <% if (cartItems.length > 0) { %>
            <a href="/customers/checkout" class="btn btn-success btn-lg">
                <i class="fas fa-credit-card"></i>
                مواصلة الشراء
                <span class="badge bg-light text-dark ms-2"><%= totalPrice.toFixed(2) %> ريال</span>
            </a>
        <% } %>
    </div>
  </div>
