// utils/databaseUtils.js
const { sequelize } = require('../models');

async function checkDatabaseHealth() {
    try {
        await sequelize.authenticate();
        return true;
    } catch (error) {
        console.error('Database health check failed:', error);
        return false;
    }
}

/**
 * تنفيذ استعلام مع إعادة المحاولة في حالة فشل الاتصال
 * @param {Function} operation العملية المراد تنفيذها
 * @param {Object} options خيارات إضافية
 * @returns {Promise} نتيجة العملية
 */
async function executeWithRetry(operation, options = { maxRetries: 3, useTransaction: true }) {
    const { maxRetries = 3, useTransaction = true } = options;
    let error;
    let retriesAttempted = 0;
    
    while (retriesAttempted < maxRetries) {
        let transaction;
        try {
            if (useTransaction) {
                transaction = await sequelize.transaction();
                const result = await operation(transaction);
                await transaction.commit();
                return result;
            } else {
                return await operation();
            }
        } catch (err) {
            if (transaction) await transaction.rollback();
            retriesAttempted++;
            error = err;
            error.retriesAttempted = retriesAttempted;
            
            if (retriesAttempted < maxRetries) {
                const delay = Math.min(1000 * Math.pow(2, retriesAttempted), 10000);
                console.log(`Retry attempt ${retriesAttempted} after ${delay}ms`);
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }
    }
    
    throw error;
}

module.exports = { checkDatabaseHealth, executeWithRetry };
