/**
 * سكريبت تشغيل المشروع مع إنشاء جدول Cart
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🚀 بدء تشغيل المشروع مع جدول Cart...\n');

try {
    // التحقق من وجود مجلد src
    if (!fs.existsSync('src')) {
        console.error('❌ مجلد src غير موجود!');
        process.exit(1);
    }

    // الانتقال إلى مجلد src
    process.chdir('src');
    console.log('📁 تم الانتقال إلى مجلد src');

    // تثبيت المكتبات المطلوبة
    console.log('\n📦 تثبيت المكتبات المطلوبة...');
    try {
        execSync('npm install node-cron', { stdio: 'inherit' });
        console.log('✅ تم تثبيت node-cron');
    } catch (error) {
        console.log('⚠️ node-cron مثبت مسبقاً أو حدث خطأ');
    }

    // تشغيل migrations لإنشاء جدول Cart
    console.log('\n🗃️ إنشاء جدول Cart...');
    try {
        execSync('npx sequelize-cli db:migrate --migrations-path migrations --config config/config.js', {
            stdio: 'inherit'
        });
        console.log('✅ تم إنشاء جدول Cart بنجاح!');
    } catch (migrationError) {
        console.log('⚠️ جدول Cart موجود مسبقاً أو حدث خطأ في Migration');
    }

    // تشغيل الخادم
    console.log('\n🚀 تشغيل الخادم...');
    console.log('📋 المميزات المتاحة:');
    console.log('  ✅ جدول Cart مع جميع العلاقات');
    console.log('  ✅ API شامل للموبايل');
    console.log('  ✅ نظام سلة متطور');
    console.log('  ✅ معالجة طلبات آمنة');
    console.log('  ✅ إشعارات تلقائية');
    console.log('  ✅ تنظيف تلقائي للسلة');
    console.log('\n🌐 الروابط المتاحة:');
    console.log('  📱 API: http://localhost:3001/api');
    console.log('  🏠 الرئيسية: http://localhost:3001');
    console.log('  💊 فحص الصحة: http://localhost:3001/health');
    console.log('\n📱 مسارات الموبايل الجديدة:');
    console.log('  🏠 GET /api/mobile/home');
    console.log('  🛒 GET /api/mobile/cart');
    console.log('  ➕ POST /api/mobile/cart/add');
    console.log('  💳 GET /api/mobile/checkout');
    console.log('  📦 POST /api/mobile/checkout');
    console.log('  📋 GET /api/mobile/orders');
    console.log('\n⚡ تشغيل الخادم...\n');

    // تشغيل الخادم
    execSync('node app.js', { stdio: 'inherit' });

} catch (error) {
    console.error('\n❌ خطأ في تشغيل المشروع:', error.message);
    console.log('\n🔧 حلول مقترحة:');
    console.log('  1. تأكد من وجود ملف package.json');
    console.log('  2. تأكد من إعداد قاعدة البيانات');
    console.log('  3. تأكد من وجود ملف config/config.js');
    console.log('  4. جرب تشغيل: npm install');
    process.exit(1);
}
