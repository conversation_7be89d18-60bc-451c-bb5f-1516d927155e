<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>إدارة المتاجر</h1>
        <a href="/admin/stores/create" class="btn btn-primary">
            <i class="fas fa-plus"></i> إضافة متجر جديد
        </a>
    </div>

    <!-- مكون البحث والفلتر -->
    <%- include('../../partials/search-filter', {
        filters: filters || {},
        sortOptions: sortOptions || [],
        customFilters: '../partials/filters/stores-filters',
        currentUrl: currentUrl || '',
        pagination: pagination || null
    }) %>

    <div class="table-responsive">
        <table class="table table-striped table-hover">
            <thead class="thead-dark">
                <tr>
                    <th>
                        <a href="?sortBy=id&sortOrder=<%= (filters.sortBy === 'id' && filters.sortOrder === 'asc') ? 'desc' : 'asc' %>"
                           class="text-white text-decoration-none">
                            ID
                            <% if (filters.sortBy === 'id') { %>
                                <i class="fas fa-sort-<%= filters.sortOrder === 'asc' ? 'up' : 'down' %>"></i>
                            <% } else { %>
                                <i class="fas fa-sort text-muted"></i>
                            <% } %>
                        </a>
                    </th>
                    <th>
                        <a href="?sortBy=name&sortOrder=<%= (filters.sortBy === 'name' && filters.sortOrder === 'asc') ? 'desc' : 'asc' %>"
                           class="text-white text-decoration-none">
                            اسم المتجر
                            <% if (filters.sortBy === 'name') { %>
                                <i class="fas fa-sort-<%= filters.sortOrder === 'asc' ? 'up' : 'down' %>"></i>
                            <% } else { %>
                                <i class="fas fa-sort text-muted"></i>
                            <% } %>
                        </a>
                    </th>
                    <th>اسم المستخدم</th>
                    <th>الفئات</th>
                    <th>المنطقة</th>
                    <th>العنوان</th>
                    <th>الطلبات</th>
                    <th>
                        <a href="?sortBy=status&sortOrder=<%= (filters.sortBy === 'status' && filters.sortOrder === 'asc') ? 'desc' : 'asc' %>"
                           class="text-white text-decoration-none">
                            الحالة
                            <% if (filters.sortBy === 'status') { %>
                                <i class="fas fa-sort-<%= filters.sortOrder === 'asc' ? 'up' : 'down' %>"></i>
                            <% } else { %>
                                <i class="fas fa-sort text-muted"></i>
                            <% } %>
                        </a>
                    </th>
                    <th>السائقين</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                <% stores.forEach(store => { %>
                    <tr>
                        <td><%= store.id %></td>
                        <td><%= store.name %></td>
                        <td><%= store.userName %></td>
                        <td>
                            <% if (store.categories && store.categories.length) { %>
                                <%= store.categories.map(c => c.name).join(', ') %>
                            <% } else { %>
                                N/A
                            <% } %>
                        </td>
                        <td><%= store.area ? store.area.name : 'N/A' %></td>
                        <td><%= store.address %></td>
                        <td>
                            <a href="/admin/stores/<%= store.id %>/orders">
                                <%= store.orders ? store.orders.length : 0 %>
                            </a>
                        </td>
                        <td>
                            <% if (store.status === 'active') { %>
                                <span class="badge badge-success">نشط</span>
                            <% } else if (store.status === 'inactive') { %>
                                <span class="badge badge-danger">غير نشط</span>
                            <% } else if (store.status === 'pending') { %>
                                <span class="badge badge-warning">في الانتظار</span>
                            <% } else if (store.status === 'banned') { %>
                                <span class="badge badge-dark">محظور</span>
                            <% } else { %>
                                <span class="badge badge-secondary">غير محدد</span>
                            <% } %>
                        </td>
                        <td>
                            <a href="/admin/stores/<%= store.id %>/drivers">
                              <%= store.deliveryPeople ? store.deliveryPeople.length : 0 %>
                            </a>
                          </td>
                        <td class="action-buttons">
                            <div class="btn-group" role="group">
                                <a href="/admin/stores/<%= store.id %>/edit"
                                   class="btn btn-sm btn-warning"
                                   title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="/admin/stores/<%= store.id %>/orders"
                                   class="btn btn-sm btn-info"
                                   title="عرض الطلبات">
                                    <i class="fas fa-shopping-cart"></i>
                                </a>
                                <a href="/admin/stores/<%= store.id %>/drivers"
                                   class="btn btn-sm btn-secondary"
                                   title="عرض السائقين">
                                    <i class="fas fa-truck"></i>
                                </a>
                                <form action="/admin/stores/<%= store.id %>/delete" method="POST" class="d-inline">
                                    <button type="submit"
                                            class="btn btn-sm btn-danger"
                                            title="حذف"
                                            onclick="return confirm('هل أنت متأكد من حذف هذا المتجر؟')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                <% }); %>

                <% if (!stores || stores.length === 0) { %>
                    <tr>
                        <td colspan="10" class="text-center py-4">
                            <div class="text-muted">
                                <i class="fas fa-store fa-3x mb-3"></i>
                                <p class="mb-0">لا توجد متاجر مطابقة للبحث</p>
                                <% if (Object.keys(filters || {}).length > 0) { %>
                                    <a href="/admin/stores" class="btn btn-sm btn-primary mt-2">
                                        <i class="fas fa-times"></i> مسح الفلاتر
                                    </a>
                                <% } %>
                            </div>
                        </td>
                    </tr>
                <% } %>
            </tbody>
        </table>
    </div>

    <!-- الـ Pagination الجديد -->
    <%- include('../../partials/pagination', {
        pagination: pagination || null,
        currentUrl: currentUrl || '',
        originalUrl: originalUrl || ''
    }) %>
</div>
