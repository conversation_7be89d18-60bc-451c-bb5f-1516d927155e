<div class="container mt-4">
    <h2>Deliveries Dashboard</h2>
    <table class="table table-striped">
      <thead>
        <tr>
          <th>Delivery ID</th>
          <th>Order ID</th>
          <th>Customer</th>
          <th>Status</th>
          <th>Created At</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        <% deliveries.forEach(delivery => { %>
          <tr>
            <td><%= delivery.id %></td>
            <td>
              <a href="/store/orders/<%= delivery.order.id %>">
                #<%= delivery.order.id %>
              </a>
            </td>
            <td>
              <a href="/store/customers/<%= delivery.order.customer.id %>">
                <%= delivery.order.customer.name || 'Unknown Customer' %>
              </a>
            </td>
            <td><%= delivery.status %></td>
            <td><%= new Date(delivery.createdAt).toLocaleString() %></td>
            <td>
              <a href="/store/deliveries/<%= delivery.id %>/edit" class="btn btn-sm btn-primary">Edit</a>
            </td>
          </tr>
        <% }) %>
      </tbody>
    </table>
    
<% if (totalPages > 1) { %>
  <nav aria-label="Page navigation" class="d-flex justify-content-center mt-4">
    <ul class="pagination">
      <% if (currentPage > 1) { %>
        <li class="page-item">
          <a class="page-link" href="?page=<%= currentPage - 1 %>">السابق</a>
        </li>
      <% } else { %>
        <li class="page-item disabled">
          <span class="page-link">السابق</span>
        </li>
      <% } %>
  
      <% for(let i = 1; i <= totalPages; i++) { %>
        <li class="page-item <%= currentPage === i ? 'active' : '' %>">
          <a class="page-link" href="?page=<%= i %>"><%= i %></a>
        </li>
      <% } %>
  
      <% if (currentPage < totalPages) { %>
        <li class="page-item">
          <a class="page-link" href="?page=<%= currentPage + 1 %>">التالي</a>
        </li>
      <% } else { %>
        <li class="page-item disabled">
          <span class="page-link">التالي</span>
        </li>
      <% } %>
    </ul>
  </nav>
  <% } %>
  </div>
  