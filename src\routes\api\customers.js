const express = require('express');
const router = express.Router();
const CustomerApiController = require('../../controllers/api/CustomerApiController');
const { authenticateCustomer, rateLimiter } = require('../../middleware/apiAuth');

// Rate limiting for auth endpoints
const authLimiter = rateLimiter(5, 15 * 60 * 1000); // 5 attempts per 15 minutes

/**
 * مسارات المصادقة (بدون حاجة لتسجيل دخول)
 */

// تسجيل دخول العميل
router.post('/login', authLimiter, CustomerApiController.login);

// تسجيل عميل جديد
router.post('/register', authLimiter, CustomerApiController.register);

/**
 * مسارات محمية (تحتاج تسجيل دخول)
 */

// الحصول على بيانات العميل
router.get('/profile', authenticateCustomer, CustomerApiController.getProfile);

// تحديث بيانات العميل
router.put('/profile', authenticateCustomer, CustomerApiController.updateProfile);

// تغيير كلمة المرور
router.put('/change-password', authenticateCustomer, CustomerApiController.changePassword);

// تسجيل الخروج
router.post('/logout', authenticateCustomer, CustomerApiController.logout);

/**
 * مسارات إضافية للعملاء
 */
router.get('/', authenticateCustomer, customersController.index.bind(customersController));
router.get('/home', authenticateCustomer, customersController.index.bind(customersController));
router.get('/categories/:id', authenticateCustomer, customersController.storesByCategory.bind(customersController));
router.get('/stores/:id', authenticateCustomer, customersController.showStoreProducts.bind(customersController));
router.get('/products/:id', authenticateCustomer, customersController.productDetails.bind(customersController));
router.get('/cart', authenticateCustomer, customersController.showCart.bind(customersController));
router.post('/cart/add', authenticateCustomer, customersController.addToCart.bind(customersController));
router.post('/cart/remove', authenticateCustomer, customersController.removeFromCart.bind(customersController));
router.get('/checkout', authenticateCustomer, customersController.showCheckout.bind(customersController));
router.post('/checkout', authenticateCustomer, customersController.processCheckout.bind(customersController));
router.get('/orders', authenticateCustomer, customersController.getOrders.bind(customersController));
router.get('/profile', authenticateCustomer, customersController.profile.bind(customersController));
router.post('/profile/address/add', authenticateCustomer, customersController.addAddress.bind(customersController));
router.post('/profile/address/:id/delete', authenticateCustomer, customersController.deleteAddress.bind(customersController));
// التحقق من صحة التوكن
router.get('/verify-token', authenticateCustomer, (req, res) => {
    res.json({
        success: true,
        message: 'التوكن صحيح',
        data: {
            customer: {
                id: req.customer.id,
                name: req.customer.name,
                email: req.customer.email,
                phone: req.customer.phone
            }
        }
    });
});

// تجديد التوكن
router.post('/refresh-token', authenticateCustomer, (req, res) => {
    const jwt = require('jsonwebtoken');
    
    try {
        // إنشاء توكن جديد
        const newToken = jwt.sign(
            { 
                customerId: req.customer.id,
                email: req.customer.email,
                type: 'customer'
            },
            process.env.JWT_SECRET || 'your-secret-key',
            { expiresIn: '30d' }
        );

        res.json({
            success: true,
            message: 'تم تجديد التوكن بنجاح',
            data: { token: newToken }
        });

    } catch (error) {
        console.error('خطأ في تجديد التوكن:', error);
        res.status(500).json({
            success: false,
            message: 'حدث خطأ في الخادم',
            data: null
        });
    }
});

module.exports = router;
