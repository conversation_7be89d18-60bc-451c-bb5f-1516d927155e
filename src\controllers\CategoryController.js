const { Category, Store } = require('../models');

exports.index = async (req, res) => {
    const page = parseInt(req.query.page) || 1;
    const limit = 20;
    const offset = (page - 1) * limit;
  
    try {
      const { count, rows: categories } = await Category.findAndCountAll({
        include: [{ model: Store, as: 'stores' }],
        order: [['createdAt', 'DESC']],
        limit,
        offset
      });
  
      const totalPages = Math.ceil(count / limit);
  
      res.render('admin/categories/index', {
        categories,
        currentPage: page,
        totalPages
      });
    } catch (error) {
      console.error('Error fetching categories:', error);
      res.status(500).render('error', { error: { message: 'Unable to fetch categories' } });
    }
  };
  

exports.createForm = (req, res) => {
  res.render('admin/categories/create');
};

exports.create = async (req, res) => {
  try {
    await Category.create(req.body );
    req.flash('success', 'Category created');
    res.redirect('/admin/categories');
  } catch (err) {
    req.flash('error', 'Error creating category');
    res.redirect('/admin/categories/create');
  }
};

exports.editForm = async (req, res) => {
    try {
        const category = await Category.findByPk(req.params.id);
    
        if (!category) {
            return res.status(404).render('error', {
                error: { status: 404, message: 'category not found' }
            });
        }
    
        res.render('admin/categories/edit', {category});
    } catch (error) {
        console.error('Error loading edit category:', error);
        res.status(500).render('error', { error: { message: 'Unable to load category data' } });
    }
};

exports.update = async (req, res) => {
  try {
        const cat = await Category.findByPk(req.params.id);
        if (!cat) {
            return res.status(404).render('error', {
                error: { status: 404, message: 'Category not found' }
            });
        }

       await Category.update(req.body, {
            where: { id: req.params.id }});
        req.flash('success', 'Category updated successfully');
        res.redirect('/admin/categories');
    } catch (error) {
        console.error('Error updating categories:', error);
        res.status(500).render('error', { error: { message: 'Unable to update category' } });
    }
};

// حذف 
exports.delete = async (req, res) => {
    try {
        const category = await Category.findByPk(req.params.id);
        if (!category) {
            return res.status(404).render('error', {
                error: { status: 404, message: 'Category not found' }
            });
        }

        await category.destroy();
        req.flash('success', 'Category deleted successfully');
        res.redirect('/admin/categories');
    } catch (error) {
        console.error('Error deleting category:', error);
        res.status(500).render('error', { error: { message: 'Unable to delete category' } });
    }
}