const express = require('express');
const router = express.Router();

// Import API routes
const customerRoutes = require('./customers');
const customerMobileRoutes = require('./customer-mobile');
const storeRoutes = require('./stores');
const productRoutes = require('./products');
const orderRoutes = require('./orders');
const generalRoutes = require('./general');
const notificationRoutes = require('./notifications');

// Import middleware
const { rateLimiter } = require('../../middleware/apiAuth');

// Apply rate limiting to all API routes
router.use(rateLimiter(200, 15 * 60 * 1000)); // 200 requests per 15 minutes

// API version and info
router.get('/', (req, res) => {
    res.json({
        success: true,
        message: 'مرحباً بك في API نظام إدارة المتاجر الذكي',
        data: {
            version: '1.0.0',
            name: 'Smart Store System API',
            description: 'API شامل لإدارة المتاجر والطلبات والعملاء',
            endpoints: {
                customers: '/api/customers',
                stores: '/api/stores',
                products: '/api/products',
                orders: '/api/orders',
                categories: '/api/categories',
                areas: '/api/areas',
                countries: '/api/countries',
                notifications: '/api/notifications',
                search: '/api/search'
            },
            features: [
                'مصادقة JWT آمنة',
                'تحديد معدل الطلبات',
                'بحث وفلتر متقدم',
                'نظام إشعارات',
                'إدارة الطلبات',
                'دعم الصفحات',
                'استجابات JSON منظمة'
            ],
            documentation: '/api/docs',
            support: '<EMAIL>'
        }
    });
});

// Health check endpoint
router.get('/health', (req, res) => {
    res.json({
        success: true,
        message: 'API يعمل بشكل طبيعي',
        data: {
            status: 'healthy',
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            version: '1.0.0'
        }
    });
});

// API Routes
router.use('/customers', customerRoutes);
router.use('/mobile', customerMobileRoutes); // مسارات الموبايل الجديدة
router.use('/stores', storeRoutes);
router.use('/products', productRoutes);
router.use('/orders', orderRoutes);
router.use('/notifications', notificationRoutes);

// General routes (categories, areas, countries, search)
router.use('/', generalRoutes);

// 404 handler for API routes
router.use('*', (req, res) => {
    res.status(404).json({
        success: false,
        message: 'المسار غير موجود',
        data: {
            requestedPath: req.originalUrl,
            method: req.method,
            availableEndpoints: [
                'GET /api/',
                'GET /api/health',
                'POST /api/customers/login',
                'POST /api/customers/register',
                'GET /api/stores',
                'GET /api/products',
                'GET /api/categories',
                'GET /api/areas',
                'GET /api/countries',
                'GET /api/search'
            ]
        }
    });
});

// Error handler for API routes
router.use((error, req, res, next) => {
    console.error('API Error:', error);

    // Sequelize validation errors
    if (error.name === 'SequelizeValidationError') {
        return res.status(400).json({
            success: false,
            message: 'خطأ في التحقق من البيانات',
            data: {
                errors: error.errors.map(err => ({
                    field: err.path,
                    message: err.message
                }))
            }
        });
    }

    // Sequelize unique constraint errors
    if (error.name === 'SequelizeUniqueConstraintError') {
        return res.status(409).json({
            success: false,
            message: 'البيانات موجودة مسبقاً',
            data: {
                field: error.errors[0]?.path || 'unknown'
            }
        });
    }

    // JWT errors
    if (error.name === 'JsonWebTokenError') {
        return res.status(401).json({
            success: false,
            message: 'رمز المصادقة غير صحيح',
            data: null
        });
    }

    if (error.name === 'TokenExpiredError') {
        return res.status(401).json({
            success: false,
            message: 'انتهت صلاحية رمز المصادقة',
            data: null
        });
    }

    // Multer errors (file upload)
    if (error.code === 'LIMIT_FILE_SIZE') {
        return res.status(400).json({
            success: false,
            message: 'حجم الملف كبير جداً',
            data: null
        });
    }

    if (error.code === 'LIMIT_UNEXPECTED_FILE') {
        return res.status(400).json({
            success: false,
            message: 'نوع الملف غير مدعوم',
            data: null
        });
    }

    // Default error response
    res.status(500).json({
        success: false,
        message: 'حدث خطأ في الخادم',
        data: process.env.NODE_ENV === 'development' ? {
            error: error.message,
            stack: error.stack
        } : null
    });
});

module.exports = router;
