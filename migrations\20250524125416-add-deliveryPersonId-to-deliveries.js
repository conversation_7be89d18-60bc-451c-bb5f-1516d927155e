'use strict';
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('Deliveries', 'deliveryPersonId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: { model: 'DeliveryPeople', key: 'id' },
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE'
    });
  },
  down: async (queryInterface) => {
    await queryInterface.removeColumn('Deliveries', 'deliveryPersonId');
  }
};