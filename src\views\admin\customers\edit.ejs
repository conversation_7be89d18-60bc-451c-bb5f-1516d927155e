<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Edit Customer</h1>
        <a href="/admin/customers" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Customers
        </a>
    </div>

    <div class="card">
        <div class="card-body">
            <form action="/admin/customers/<%= customer.id %>" method="POST">
                <div class="mb-3">
                    <label for="name" class="form-label">Customer Name</label>
                    <input type="text" class="form-control" id="name" name="name" value="<%= customer.name %>" required>
                </div>

                <div class="mb-3">
                    <label for="phoneNumber" class="form-label">Phone Number</label>
                    <input type="text" class="form-control" id="phoneNumber" name="phoneNumber" value="<%= customer.phoneNumber || '' %>">
                </div>

                <div class="mb-3">
                    <label for="areaIds" class="form-label">Areas</label>
                    <select multiple class="form-select" id="areaIds" name="areaIds">
                        <% areas.forEach(area => { %>
                            <option value="<%= area.id %>" <%= customer.areas.some(a => a.id === area.id) ? 'selected' : '' %>>
                                <%= area.name %>
                            </option>
                        <% }); %>
                    </select>
                </div>

                <div class="mb-3">
                    <label for="discountRate" class="form-label">Discount Rate (%)</label>
                    <input type="number" class="form-control" id="discountRate" name="discountRate" step="0.01" value="<%= customer.discountRate || 0 %>">
                </div>

                <div class="mb-3">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status" required>
                        <option value="active" <%= customer.status === 'active' ? 'selected' : '' %>>Active</option>
                        <option value="inactive" <%= customer.status === 'inactive' ? 'selected' : '' %>>Inactive</option>
                        <option value="pending" <%= customer.status === 'pending' ? 'selected' : '' %>>Pending</option>
                    </select>
                </div>

                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">Update Customer</button>
                </div>
            </form>
        </div>
    </div>
</div>
