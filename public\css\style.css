/* ملف CSS احتياطي بسيط */

/* إعادة تعيين أساسية */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #f8f9fa;
    color: #343a40;
    line-height: 1.6;
    direction: rtl;
}

/* إخفاء شاشة التحميل فوراً */
.loading-overlay {
    display: none !important;
}

/* تأكد من ظهور النص */
h1, h2, h3, h4, h5, h6 {
    color: #343a40 !important;
}

p, span, div, td, th {
    color: #343a40 !important;
}

/* الروابط */
a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    color: #0056b3;
    text-decoration: underline;
}

/* الأزرار */
.btn {
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    border: none;
    cursor: pointer;
    font-weight: 500;
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-success {
    background-color: #28a745;
    color: white;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

.btn-warning {
    background-color: #ffc107;
    color: #212529;
}

/* الجداول */
.table {
    width: 100%;
    margin-bottom: 1rem;
    color: #343a40;
}

.table th,
.table td {
    padding: 0.75rem;
    vertical-align: top;
    border-top: 1px solid #dee2e6;
    color: #343a40 !important;
}

.table thead th {
    vertical-align: bottom;
    border-bottom: 2px solid #dee2e6;
    background-color: #e9ecef;
    color: #495057 !important;
}

/* البطاقات */
.card {
    background-color: white;
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 1rem;
    color: #495057 !important;
}

.card-body {
    padding: 1rem;
    color: #343a40 !important;
}

/* النماذج */
.form-control {
    display: block;
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    color: #495057;
}

.form-control:focus {
    border-color: #007bff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* التنبيهات */
.alert {
    padding: 0.75rem 1.25rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: 0.25rem;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.alert-warning {
    color: #856404;
    background-color: #fff3cd;
    border-color: #ffeaa7;
}

.alert-info {
    color: #0c5460;
    background-color: #d1ecf1;
    border-color: #bee5eb;
}

/* الشريط الجانبي */
.sidebar {
    background-color: #343a40;
    min-height: 100vh;
    padding: 1rem;
}

.sidebar .nav-link {
    color: #adb5bd;
    padding: 0.5rem 1rem;
    margin-bottom: 0.25rem;
    border-radius: 0.25rem;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    color: white;
    background-color: #495057;
}

/* الشريط العلوي */
.navbar {
    background-color: #007bff !important;
    padding: 1rem;
}

.navbar-brand,
.navbar-nav .nav-link {
    color: white !important;
}

.navbar-nav .nav-link:hover {
    color: #f8f9fa !important;
}

/* المحتوى الرئيسي */
.main-content {
    background-color: white;
    padding: 2rem;
    margin: 1rem;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    color: #343a40 !important;
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        top: 0;
        left: -100%;
        width: 250px;
        z-index: 1000;
        transition: left 0.3s ease;
    }
    
    .sidebar.show {
        left: 0;
    }
    
    .main-content {
        margin: 0.5rem;
        padding: 1rem;
    }
}

/* أدوات مساعدة */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }
.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.justify-content-center { justify-content: center; }
.align-items-center { align-items: center; }
.mb-3 { margin-bottom: 1rem; }
.mt-3 { margin-top: 1rem; }
.p-3 { padding: 1rem; }

/* تأكد من عدم وجود نص أبيض على خلفية بيضاء */
.text-white {
    color: white !important;
}

/* إصلاح مشاكل الألوان */
.container,
.container-fluid,
.row,
.col,
[class*="col-"] {
    color: #343a40;
}

/* إصلاح الروابط في الجداول */
.table a {
    color: #007bff;
}

.table a:hover {
    color: #0056b3;
}

/* إصلاح النص في البطاقات */
.card * {
    color: inherit;
}

.card-header * {
    color: #495057 !important;
}

.card-body * {
    color: #343a40 !important;
}
