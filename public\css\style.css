/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

main {
    flex: 1;
}

/* Loading Indicator */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-overlay.show {
    display: flex;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error Page */
.error-container {
    max-width: 800px;
    margin: 0 auto;
}

.error-card {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    overflow: hidden;
}

.error-header {
    background-color: #dc3545;
    color: white;
    padding: 15px;
}

.error-body {
    padding: 20px;
}

.error-footer {
    background-color: #f8f9fa;
    padding: 10px;
    border-top: 1px solid #e9ecef;
}

/* Tables */
.table-responsive {
    overflow-x: auto;
}

.table th {
    background-color: #f8f9fa;
}

.action-buttons .btn {
    margin-right: 5px;
}

/* Cards */
.card {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    margin-bottom: 20px;
}

/* Form Styling */
.form-control:focus {
    border-color: #4a6fdc;
    box-shadow: 0 0 0 0.25rem rgba(74, 111, 220, 0.25);
}

/* Alerts */
.alert {
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Pagination */
.pagination .page-item.active .page-link {
    background-color: #4a6fdc;
    border-color: #4a6fdc;
}

.pagination .page-link {
    color: #4a6fdc;
}

/* Navbar styling */
.navbar {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Footer */
footer {
    margin-top: auto;
    box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.05);
}

/* Notifications Styles */
.notification-card {
    position: relative;
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
}

.notification-card.unread {
    border-left-color: #007bff;
    background-color: #f8f9ff;
}

.notification-card.priority-urgent {
    border-left-color: #dc3545;
}

.notification-card.priority-high {
    border-left-color: #ffc107;
}

.notification-card.priority-normal {
    border-left-color: #17a2b8;
}

.notification-card.priority-low {
    border-left-color: #6c757d;
}

.notification-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.unread-indicator {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 8px;
    height: 8px;
    background-color: #007bff;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.7;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.notification-meta {
    font-size: 0.875rem;
}

.notification-meta .meta-item {
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.notification-meta .meta-item strong {
    min-width: 100px;
    color: #495057;
}

.notification-detail {
    border: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.notification-detail.unread {
    border-left: 5px solid #007bff;
}

.notification-message {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 0.375rem;
    border-left: 3px solid #dee2e6;
}

/* Notification Bell Animation */
.notification-bell {
    position: relative;
    display: inline-block;
}

.notification-bell.has-notifications {
    animation: ring 2s ease-in-out infinite;
}

@keyframes ring {
    0%, 20%, 50%, 80%, 100% {
        transform: rotate(0deg);
    }
    10% {
        transform: rotate(-10deg);
    }
    30% {
        transform: rotate(10deg);
    }
    60% {
        transform: rotate(-5deg);
    }
    70% {
        transform: rotate(5deg);
    }
}

.notification-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: #dc3545;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

/* Notification Dropdown */
.notification-dropdown {
    width: 350px;
    max-height: 400px;
    overflow-y: auto;
}

.notification-dropdown .dropdown-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #e9ecef;
    white-space: normal;
}

.notification-dropdown .dropdown-item:last-child {
    border-bottom: none;
}

.notification-dropdown .dropdown-item.unread {
    background-color: #f8f9ff;
    border-left: 3px solid #007bff;
}

.notification-dropdown .notification-title {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.25rem;
}

.notification-dropdown .notification-text {
    color: #6c757d;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

.notification-dropdown .notification-time {
    color: #adb5bd;
    font-size: 0.75rem;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1055;
}

.toast.notification-toast {
    min-width: 300px;
    border-left: 4px solid #007bff;
}

.toast.notification-toast.type-success {
    border-left-color: #28a745;
}

.toast.notification-toast.type-warning {
    border-left-color: #ffc107;
}

.toast.notification-toast.type-error {
    border-left-color: #dc3545;
}

/* Responsive fixes */
@media (max-width: 768px) {
    .action-buttons {
        display: flex;
        flex-direction: column;
    }

    .action-buttons .btn {
        margin-bottom: 5px;
        width: 100%;
    }

    .notification-dropdown {
        width: 280px;
    }

    .notification-card {
        margin-bottom: 1rem;
    }

    .toast-container {
        right: 10px;
        left: 10px;
    }

    .toast.notification-toast {
        min-width: auto;
    }
}