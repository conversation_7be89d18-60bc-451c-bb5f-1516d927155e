const { Order, Delivery, Store, Customer, DeliveryPerson } = require('../models');

class DeliveryController {

  // عرض توصيلات سائق معين داخل متجر
  async personDeliveries(req, res) {
    const { storeId, personId } = req.params;

    try {
      const inProgressDeliveries = await Delivery.findAll({
        where: {
          deliveryPersonId: personId,
          status: ['pending', 'in_transit']
        },
        include: [
          {
            model: Order,
            as: 'order',
            where: { storeId },
            include: ['customer']
          },
          { model: DeliveryPerson, as: 'courier' }
        ],
        order: [['createdAt', 'DESC']]
      });

      const completedDeliveries = await Delivery.findAll({
        where: {
          deliveryPersonId: personId,
          status: ['delivered', 'cancelled']
        },
        include: [
          {
            model: Order,
            as: 'order',
            where: { storeId },
            include: ['customer']
          },
          { model: DeliveryPerson, as: 'courier' }
        ],
        order: [['createdAt', 'DESC']]
      });

      const deliveryPersonName = inProgressDeliveries[0]?.courier?.name || completedDeliveries[0]?.courier?.name || 'Unknown';

      res.render('stores/delivery/index', {
        storeId,
        personId,
        deliveryPersonName,
        inProgressDeliveries,
        completedDeliveries
      });

    } catch (error) {
      console.error(error);
      res.status(500).render('error', { error: { message: 'Server error' } });
    }
  }
  
  // عرض كل التوصيلات الخاصة بالمتجر (dashboard)
  async index(req, res) {
    try {
      const storeId = req.session.storeId;
      const currentPage = parseInt(req.query.page) || 1;
      const limit = 20;
      const offset = (currentPage - 1) * limit;
  
      const { count, rows: deliveries } = await Delivery.findAndCountAll({
        include: [
          {
            model: Order,
            as: 'order',
            where: { storeId },
            include: [{ model: Customer, as: 'customer' }]
          }
        ],
        order: [['createdAt', 'DESC']],
        limit,
        offset
      });
  
      const totalPages = Math.ceil(count / limit);
  
      res.render('stores/delivery/dashboard', {
        deliveries,
        currentPage,
        totalPages
      });
  
    } catch (error) {
      console.error('Error fetching deliveries:', error);
      res.status(500).render('error', { error: { message: 'Unable to fetch deliveries' } });
    }
  }
  

  // عرض صفحة تفاصيل التوصيل لطلب معين
  async show(req, res) {
    const orderId = req.params.orderId;
    const order = await Order.findByPk(orderId, {
      include: [
        { model: Delivery, as: 'delivery' },
        { model: Customer, as: 'customer' },
        { model: Store, as: 'store' }
      ]
    });
    if (!order) return res.status(404).render('error', { error: { message: 'Order not found' } });
    res.render('delivery/show', { order, delivery: order.delivery });
  }

  // إنشاء سجل توصيل جديد (يمكن استدعاؤها من API أو أمر محدد)
  async createFromOrder(req, res) {
    const orderId = req.params.orderId;
    const order = await Order.findByPk(orderId);
    if (!order) return res.status(404).render('error', { error: { message: 'Order not found' } });

    // تفادي تكرار السجل
    let delivery = await Delivery.findOne({ where: { orderId } });
    if (!delivery) {
      delivery = await Delivery.create({ orderId, status: 'pending', storeId: order.storeId });
    }
    res.redirect(`/admin/orders/${orderId}/delivery`);
  }

  // عرض نموذج إنشاء توصيل جديد (مع اختيار سائق تلقائي)
async createForm(req, res) {
  const storeId = req.params.storeId;
  const deliveryPersonId = req.query.deliveryPersonId || null;

  try {
    const deliveryPeople = await DeliveryPerson.findAll({ where: { storeId } });

    // جلب الطلبات التي لم تُربط بعد بتوصيل (Pending)
    const pendingOrders = await Order.findAll({
      where: {
        storeId,
        status: 'pending' // أو أي حالة تمثل "بانتظار التوصيل"
      },
      include: [{ model: Customer, as: 'customer' }]
    });

    res.render('stores/delivery/create', {
      storeId,
      deliveryPeople,
      pendingOrders,
      selectedDeliveryPersonId: deliveryPersonId,
      errors: null,
      formData: {}
    });
  } catch (error) {
    console.error(error);
    res.status(500).send('Server error');
  }
}

  // معالجة إنشاء توصيل جديد من نموذج
  async create(req, res) {
    const storeId = req.params.storeId;
    const { deliveryPersonId, orderId, status, pickupTime, deliveryTime, notes } = req.body;

    try {
      const existingDelivery = await Delivery.findOne({ where: { orderId } });
    
    if (existingDelivery) {
      // إذا موجود، ارجع رسالة خطأ أو إعادة توجيه مع رسالة
      return res.status(400).render('error', {
        error: 'هذا الطلب لديه توصيل موجود مسبقاً.',
        orderId
      });
    }
      await Delivery.create({
        orderId: orderId || null,
        deliveryPersonId: deliveryPersonId || null,
        status,
        pickupTime: pickupTime || null,
        deliveryTime: deliveryTime || null,
        notes: notes || null,
        storeId
      });
      res.redirect(`/store/${storeId}/delivery-people`);
    } catch (error) {
      console.error(error);
      const deliveryPeople = await DeliveryPerson.findAll({ where: { storeId } });
      res.render('stores/delivery/create', {
        storeId,
        deliveryPeople,
        selectedDeliveryPersonId: deliveryPersonId,
        errors: error.errors,
        formData: req.body
      });
    }
  }

  // عرض نموذج تعديل توصيل
  async editForm(req, res) {
    const delivery = await Delivery.findByPk(req.params.id, { include: ['order'] });
    if (!delivery) return res.status(404).render('error', { error: { message: 'Delivery not found' } });
    res.render('stores/delivery/edit', { delivery });
  }

  // تحديث بيانات توصيل
  async update(req, res) {
    const { status, pickupTime, deliveryTime, notes } = req.body;
    const delivery = await Delivery.findByPk(req.params.id);
    if (!delivery) return res.status(404).render('error', { error: { message: 'Delivery not found' } });

    await delivery.update({ status, pickupTime, deliveryTime, notes });
    res.redirect('/store/deliveries');
  }

  // حذف توصيل
  async delete(req, res) {
    const delivery = await Delivery.findByPk(req.params.id);
    if (!delivery) return res.status(404).render('error', { error: { message: 'Delivery not found' } });

    await delivery.destroy();
    res.redirect('/store/deliveries');
  }

  // تحديث حالة التوصيل (مثلاً من صفحة خاصة)
  async markComplete(req, res) {
    const { deliveryId } = req.params;
    const storeId = req.session.storeId;
    try {
      // جلب التوصيل مع الطلب المرتبط للتأكد من صلاحية المتجر
      const delivery = await Delivery.findOne({
        where: { id: deliveryId },
        include: {
          model: Order,
          as: 'order',
          where: { storeId }
        }
      });
  
      if (!delivery) {
        return res.status(404).render('error', { error: { message: 'Delivery not found' } });
      }
  
      // تحديث حالة التوصيل
      delivery.status = 'delivered'; // أو حسب الحالة التي تستخدمها
      await delivery.save();
  
      // تحديث حالة الطلب المرتبط
      delivery.order.status = 'delivered'; // حسب النظام لديك
      await delivery.order.save();

      res.redirect('/store/'+storeId+'/delivery-people/');
    } catch (error) {
      console.error(error);
      res.status(500).render('error', { error: { message: 'Unable to update delivery status' } });
    }
  }

  async showDelivery(req, res) {
    const { orderId } = req.params;
    const storeId = req.session.storeId;
  
    try {
      // جلب التوصيل المرتبط بالطلب مع بيانات السائق والطلب والعميل (حسب الربط)
      const delivery = await Delivery.findOne({
        where: { orderId },
        include: [
          {
            model: Order,
            as: 'order',
            where: { storeId },
            include: [{ model: Customer, as: 'customer' }]
          },
          {
            model: DeliveryPerson, // اسم الموديل للسائق عندك
            as: 'courier'          // تأكد من alias في associations
          }
        ]
      });
  
      if (!delivery) {
        return res.status(404).render('error', { error: { message: 'Delivery not found' } });
      }
  
      res.render('stores/delivery/show', { delivery });
    } catch (error) {
      console.error(error);
      res.status(500).render('error', { error: { message: 'Unable to fetch delivery details' } });
    }
  }
  
}

module.exports = new DeliveryController();
