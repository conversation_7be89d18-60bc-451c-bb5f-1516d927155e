@echo off
chcp 65001 >nul
cls

echo 🚀 تشغيل سريع لنظام إدارة المتاجر الذكي
echo ========================================

echo 📦 تثبيت التبعيات...
npm install

echo 🗄️ إعداد قاعدة البيانات...
if not exist .env (
    echo ⚠️ إنشاء ملف .env...
    copy .env.example .env >nul 2>&1
)

echo 📁 إنشاء المجلدات...
if not exist uploads mkdir uploads
if not exist logs mkdir logs

echo 🌐 تشغيل التطبيق...
echo ========================================
echo 🔗 الرابط: http://localhost:3001
echo 📊 لوحة الإدارة: http://localhost:3001/admin/auth/login
echo 🏪 المتاجر: http://localhost:3001/store/auth/login
echo 👤 العملاء: http://localhost:3001/customers/auth/login
echo ========================================

cd src
node app.js

pause
