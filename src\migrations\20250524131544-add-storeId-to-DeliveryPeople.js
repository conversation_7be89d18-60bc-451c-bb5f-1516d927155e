'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('DeliveryPeople', 'storeId', {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: 'Stores',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE' // أو SET NULL إذا أردت السماح بحذف المتجر دون حذف السائقين
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('DeliveryPeople', 'storeId');
  }
};
