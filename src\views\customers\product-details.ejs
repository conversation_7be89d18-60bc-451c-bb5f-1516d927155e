<div class="container mt-4">
    <a href="javascript:history.back()" class="btn btn-light mb-3">← Back</a>
  
    <div class="card shadow border-0">
        <div class="d-flex flex-wrap mb-3 gap-2">
            <% if (product.images && product.images.length > 0) { %>
              <% product.images.forEach(img => { %>
                <img 
                src="<%= img.image %>"
                  alt="Product image" 
                  style="max-width: 120px; max-height: 120px; object-fit: cover; border-radius: 6px; border: 1px solid #ddd;"
                >
              <% }) %>
            <% } else if (product.image) { %>
              <img 
              src="<%= product.image %>" 
                class="card-img-top mb-3" 
                alt="<%= product.name %>"
              >
            <% } %>
          </div>        
      <div class="card-body">
        <h2 class="card-title fw-bold mb-2"><%= product.name %></h2>
        <p class="text-muted mb-1">Category: <strong><%= product.category?.name %></strong></p>
        <p class="text-muted mb-1">Store: <strong><%= product.store?.name %></strong></p>
        <hr>
        <p class="card-text"><%= product.description || 'No description available.' %></p>
        <div class="d-flex justify-content-between align-items-center mt-3">
          <h4 class="text-success">$<%= product.price.toFixed(2) %></h4>
          <span class="badge bg-<%= product.status === 'available' ? 'success' : 'danger' %>">
            <%= product.status %>
          </span>
        </div>
        <form action="/customers/cart/add" method="POST" class="mt-3">
            <input type="hidden" name="productId" value="<%= product.id %>">
            <div class="mb-3" style="max-width: 100px;">
              <label for="quantity" class="form-label">Quantity:</label>
              <input type="number" name="quantity" id="quantity" value="1" min="1" class="form-control" required>
            </div>
            <button type="submit" class="btn btn-primary">Add to Cart</button>
          </form>
      </div>
    </div>
  </div>
  