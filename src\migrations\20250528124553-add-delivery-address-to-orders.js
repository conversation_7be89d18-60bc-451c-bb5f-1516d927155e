'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.addColumn('Orders', 'deliveryAddress', {
      type: Sequelize.TEXT,
      allowNull: true
    });

    await queryInterface.addColumn('Orders', 'totalAmount', {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0
    });
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.removeColumn('Orders', 'deliveryAddress');
    await queryInterface.removeColumn('Orders', 'totalAmount');
  }
};
