module.exports = {
  apps: [{
    name: 'smart-store-system',
    script: './src/app.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'development',
      PORT: 3001
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    // Logging
    log_file: './logs/combined.log',
    out_file: './logs/out.log',
    error_file: './logs/error.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    
    // Performance monitoring
    monitoring: false,
    
    // Auto restart
    watch: false,
    ignore_watch: ['node_modules', 'logs'],
    
    // Memory management
    max_memory_restart: '1G',
    
    // Graceful shutdown
    kill_timeout: 5000,
    
    // Health check
    health_check_grace_period: 3000,
    
    // Environment variables for production
    env_production: {
      NODE_ENV: 'production',
      PORT: 3000,
      DB_HOST: process.env.DB_HOST || 'localhost',
      DB_NAME: process.env.DB_NAME || 'smart_store_system',
      DB_USER: process.env.DB_USER || 'root',
      DB_PASS: process.env.DB_PASS || '',
      DB_PORT: process.env.DB_PORT || 3306,
      SESSION_SECRET: process.env.SESSION_SECRET || 'your-super-secret-key-change-in-production',
      JWT_SECRET: process.env.JWT_SECRET || 'your-jwt-secret-key-change-in-production',
      UPLOAD_PATH: process.env.UPLOAD_PATH || './uploads',
      MAX_FILE_SIZE: process.env.MAX_FILE_SIZE || '10MB',
      REDIS_URL: process.env.REDIS_URL || 'redis://localhost:6379',
      EMAIL_HOST: process.env.EMAIL_HOST || 'smtp.gmail.com',
      EMAIL_PORT: process.env.EMAIL_PORT || 587,
      EMAIL_USER: process.env.EMAIL_USER || '',
      EMAIL_PASS: process.env.EMAIL_PASS || '',
      ADMIN_EMAIL: process.env.ADMIN_EMAIL || '<EMAIL>',
      SITE_URL: process.env.SITE_URL || 'https://your-domain.com',
      ENABLE_HTTPS: process.env.ENABLE_HTTPS || 'true',
      SSL_CERT_PATH: process.env.SSL_CERT_PATH || '',
      SSL_KEY_PATH: process.env.SSL_KEY_PATH || '',
      RATE_LIMIT_WINDOW: process.env.RATE_LIMIT_WINDOW || 900000, // 15 minutes
      RATE_LIMIT_MAX: process.env.RATE_LIMIT_MAX || 100,
      CORS_ORIGIN: process.env.CORS_ORIGIN || 'https://your-domain.com',
      ENABLE_COMPRESSION: process.env.ENABLE_COMPRESSION || 'true',
      ENABLE_HELMET: process.env.ENABLE_HELMET || 'true',
      LOG_LEVEL: process.env.LOG_LEVEL || 'info',
      BACKUP_ENABLED: process.env.BACKUP_ENABLED || 'true',
      BACKUP_SCHEDULE: process.env.BACKUP_SCHEDULE || '0 2 * * *', // Daily at 2 AM
      NOTIFICATION_ENABLED: process.env.NOTIFICATION_ENABLED || 'true',
      CACHE_TTL: process.env.CACHE_TTL || 3600, // 1 hour
      API_RATE_LIMIT: process.env.API_RATE_LIMIT || 1000,
      MAINTENANCE_MODE: process.env.MAINTENANCE_MODE || 'false'
    }
  }],

  deploy: {
    production: {
      user: 'deploy',
      host: ['your-server-ip'],
      ref: 'origin/main',
      repo: 'https://github.com/your-username/smart-store-system.git',
      path: '/var/www/smart-store-system',
      'post-deploy': 'npm install && npm run build && pm2 reload ecosystem.config.js --env production',
      'pre-setup': 'apt update && apt install git -y'
    }
  }
};
