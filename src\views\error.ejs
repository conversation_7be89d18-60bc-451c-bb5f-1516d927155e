<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-danger text-white">
                    <h3 class="text-center m-0">
                        Error <%= error && error.status ? error.status : 500 %>
                    </h3>
                </div>
                <div class="card-body text-center">
                    <% if (error) { %>
                        <h5 class="card-title">
                            <%= error.message || 'An unexpected error occurred' %>
                        </h5>
                        
                        <% if (process.env.NODE_ENV === 'development') { %>
                            <% if (error.details) { %>
                                <div class="alert alert-warning mt-3">
                                    <strong>Error Details:</strong> <%= error.details %>
                                </div>
                            <% } %>
                            
                            <% if (error.sql) { %>
                                <div class="mt-3">
                                    <h6>SQL Error:</h6>
                                    <div class="bg-light p-3 text-start">
                                        <code><%= error.sql %></code>
                                    </div>
                                </div>
                            <% } %>
                            
                            <% if (error.stack) { %>
                                <div class="mt-3">
                                    <h6>Stack Trace:</h6>
                                    <div class="bg-light p-3 text-start overflow-auto" style="max-height: 300px">
                                        <pre class="mb-0"><%= error.stack %></pre>
                                    </div>
                                </div>
                            <% } %>
                        <% } %>
                    <% } else { %>
                        <h5 class="card-title">An unexpected error occurred</h5>
                    <% } %>
                    
                    <div class="mt-4">
                        <a href="/" class="btn btn-primary me-2">
                            <i class="fas fa-home"></i> Go Home
                        </a>
                        <button onclick="window.history.back()" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Go Back
                        </button>
                    </div>
                </div>
                <div class="card-footer bg-light">
                    <div class="text-center">
                        <small>If this problem persists, please contact support.</small>
                    </div>
                </div>
            </div>
            
            <% if (process.env.NODE_ENV === 'development') { %>
                <div class="card mt-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="m-0">Diagnostic Tools</h5>
                    </div>
                    <div class="card-body">
                        <p>Use these tools to diagnose the issue:</p>
                        <div class="d-flex flex-wrap gap-2">
                            <a href="/diagnostic/db-connection" class="btn btn-outline-info" target="_blank">
                                Check DB Connection
                            </a>
                            <a href="/diagnostic/db-tables" class="btn btn-outline-info" target="_blank">
                                Check DB Tables
                            </a>
                            <a href="/diagnostic/db-health" class="btn btn-outline-info" target="_blank">
                                Full DB Health Check
                            </a>
                        </div>
                    </div>
                </div>
            <% } %>
        </div>
    </div>
</div> 