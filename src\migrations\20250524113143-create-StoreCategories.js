// migrations/xxxx-create-store-categories.js
'use strict';
module.exports = {
  up: async (qi, Sequelize) => {
    await qi.createTable('StoreCategories', {
      storeId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: { model: 'Stores', key: 'id' },
        onDelete: 'CASCADE',   // إذا كنت تريد الحذف المتسلسل
        onUpdate: 'CASCADE'
      },
      categoryId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: { model: 'Categories', key: 'id' },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE'
      },
      createdAt: { type: Sequelize.DATE, allowNull: false, defaultValue: Sequelize.NOW },
      updatedAt: { type: Sequelize.DATE, allowNull: false, defaultValue: Sequelize.NOW }
    });

    // مفتاح أساسي مركب
    await qi.addConstraint('StoreCategories', {
      fields: ['storeId', 'categoryId'],
      type: 'primary key',
      name: 'PK_StoreCategories'
    });
  },
  down: async (qi) => {
    await qi.dropTable('StoreCategories');
  }
};
