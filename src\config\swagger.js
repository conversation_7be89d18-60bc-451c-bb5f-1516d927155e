// src/config/swagger.js

const swaggerJsDoc = require('swagger-jsdoc');

const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'My API',
      version: '1.0.0',
      description: 'API documentation using Swagger',
    },
    servers: [
      {
        url: 'http://localhost:3000/api', // عدل على حسب مشروعك
      },
    ],
  },
  apis: ['../routes/*.js'], // المسار إلى ملفات الراوتر التي تحتوي على توثيق Swagger
};

const swaggerSpec = swaggerJsDoc(options);

module.exports = swaggerSpec;
