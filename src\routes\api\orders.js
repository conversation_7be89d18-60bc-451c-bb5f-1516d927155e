const express = require('express');
const router = express.Router();
const OrderApiController = require('../../controllers/api/OrderApiController');
const { authenticateCustomer } = require('../../middleware/apiAuth');

/**
 * مسارات الطلبات (محمية - تحتاج مصادقة العميل)
 */

// إنشاء طلب جديد
router.post('/', authenticateCustomer, OrderApiController.createOrder);

// الحصول على طلبات العميل
router.get('/', authenticateCustomer, OrderApiController.getCustomerOrders);

// الحصول على إحصائيات الطلبات
router.get('/stats', authenticateCustomer, OrderApiController.getOrderStats);

// الحصول على تفاصيل طلب واحد
router.get('/:id', authenticateCustomer, OrderApiController.getOrderById);

// إلغاء طلب
router.put('/:id/cancel', authenticateCustomer, OrderApiController.cancelOrder);

module.exports = router;
