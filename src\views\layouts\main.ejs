<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>E-Market Platform</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <!-- Navigation -->
<nav class="navbar navbar-expand-lg navbar-dark bg-primary">
    <div class="container">
        <a class="navbar-brand" href="/">E-Market</a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <% if (locals.session && locals.session.customerId) { %>
                    <li class="nav-item">
                        <a class="nav-link" href="/products">Browse Products</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/stores">Stores</a>
                    </li>
                <% } else if (locals.session && locals.session.storeId) { %>
                    <li class="nav-item">
                        <a class="nav-link" href="/products">Browse Products</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/stores">Stores</a>
                    </li>
                <% } else if (locals.session && locals.session.adminId) { %>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/countries">Cities</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/areas">Areas</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/categories">Categories</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/stores">Stores</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/customers">Customers</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/orders">Orders</a>
                    </li>
                <% } %>
            </ul>

            <ul class="navbar-nav">
                <% if (locals.session && locals.session.customerId) { %>
                    <!-- Notifications for Customer -->
                    <li class="nav-item dropdown">
                        <a class="nav-link notification-bell" href="#" id="customerNotificationDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-bell"></i>
                            <span class="notification-count" id="customerNotificationCount" style="display: none;">0</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end notification-dropdown" aria-labelledby="customerNotificationDropdown">
                            <li class="dropdown-header d-flex justify-content-between align-items-center">
                                <span>الإشعارات</span>
                                <button class="btn btn-sm btn-link text-primary" onclick="markAllCustomerNotificationsAsRead()">
                                    وضع الكل كمقروء
                                </button>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <div id="customerNotificationsList">
                                <li class="dropdown-item text-center text-muted">
                                    <i class="fas fa-spinner fa-spin"></i> جاري التحميل...
                                </li>
                            </div>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item text-center" href="/customers/notifications">
                                    <i class="fas fa-eye"></i> عرض جميع الإشعارات
                                </a>
                            </li>
                        </ul>
                    </li>

                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="dropdownCustomer" role="button" data-bs-toggle="dropdown">
                            My Account
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/customers/orders">My Orders</a></li>
                            <li><a class="dropdown-item" href="/customers/notifications">
                                <i class="fas fa-bell"></i> إشعاراتي
                            </a></li>
                            <li><a class="dropdown-item" href="/customers/profile">Profile</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/customers/logout">Logout</a></li>
                        </ul>
                    </li>
                <% } else if (locals.session && locals.session.storeId) { %>
                    <!-- Notifications for Store -->
                    <li class="nav-item dropdown">
                        <a class="nav-link notification-bell" href="#" id="storeNotificationDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-bell"></i>
                            <span class="notification-count" id="storeNotificationCount" style="display: none;">0</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end notification-dropdown" aria-labelledby="storeNotificationDropdown">
                            <li class="dropdown-header d-flex justify-content-between align-items-center">
                                <span>الإشعارات</span>
                                <button class="btn btn-sm btn-link text-primary" onclick="markAllStoreNotificationsAsRead()">
                                    وضع الكل كمقروء
                                </button>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <div id="storeNotificationsList">
                                <li class="dropdown-item text-center text-muted">
                                    <i class="fas fa-spinner fa-spin"></i> جاري التحميل...
                                </li>
                            </div>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item text-center" href="/store/notifications">
                                    <i class="fas fa-eye"></i> عرض جميع الإشعارات
                                </a>
                            </li>
                        </ul>
                    </li>

                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="dropdownStore" role="button" data-bs-toggle="dropdown">
                            Store Dashboard
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/store/products">Products</a></li>
                            <li><a class="dropdown-item" href="/store/orders">Orders</a></li>
                            <li><a class="dropdown-item" href="/store/notifications">
                                <i class="fas fa-bell"></i> إشعاراتي
                            </a></li>
                            <li><a class="dropdown-item" href="/store/profile">Store Profile</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/store/logout">Logout</a></li>
                        </ul>
                    </li>
                <% } else if (locals.session && locals.session.adminId) { %>
                    <!-- Notifications for Admin -->
                    <li class="nav-item dropdown">
                        <a class="nav-link notification-bell" href="#" id="notificationDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-bell"></i>
                            <span class="notification-count" id="notificationCount" style="display: none;">0</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end notification-dropdown" aria-labelledby="notificationDropdown">
                            <li class="dropdown-header d-flex justify-content-between align-items-center">
                                <span>الإشعارات</span>
                                <button class="btn btn-sm btn-link text-primary" onclick="markAllNotificationsAsRead()">
                                    وضع الكل كمقروء
                                </button>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <div id="notificationsList">
                                <li class="dropdown-item text-center text-muted">
                                    <i class="fas fa-spinner fa-spin"></i> جاري التحميل...
                                </li>
                            </div>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item text-center" href="/notifications">
                                    <i class="fas fa-eye"></i> عرض جميع الإشعارات
                                </a>
                            </li>
                        </ul>
                    </li>

                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="dropdownAdmin" role="button" data-bs-toggle="dropdown">
                            Admin Panel
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/notifications">
                                <i class="fas fa-bell"></i> إدارة الإشعارات
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/admin/auth/logout">Logout</a></li>
                        </ul>
                    </li>
                <% } else { %>
                    <li class="nav-item">
                        <a class="nav-link" href="/customer/login">Login</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/customer/register">Register</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/store/register">Register Store</a>
                    </li>
                <% } %>

                <!-- Cart -->
                <li class="nav-item">
                    <a class="nav-link" href="/customers/cart">
                        <i class="fas fa-shopping-cart"></i>
                        <% if (locals.session && locals.session.cart && locals.session.cart.length > 0) { %>
                            <span class="badge bg-danger"><%= locals.session.cart.length %></span>
                        <% } %>
                    </a>
                </li>
            </ul>
        </div>
    </div>
</nav>


    <!-- Loading Overlay -->
    <div class="loading-overlay">
        <div class="spinner"></div>
    </div>

    <!-- Flash Messages -->
    <div class="container mt-3">
        <% if (locals.success && success.length > 0) { %>
            <div class="alert alert-success alert-dismissible fade show">
                <%= success %>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <% } %>
        <% if (locals.error && error.length > 0) { %>
            <div class="alert alert-danger alert-dismissible fade show">
                <%= error %>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <% } %>
    </div>

    <!-- Main Content -->
    <main class="container my-4">
        <%- body %>
    </main>

    <!-- Footer -->
    <footer class="bg-light py-4 mt-auto">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>E-Market Platform</h5>
                    <p>Your one-stop shop for all your needs</p>
                    <p>&copy; <span class="copyright-year"><%= new Date().getFullYear() %></span> E-Market. All rights reserved.</p>
                </div>
                <div class="col-md-3">
                    <h5>Quick Links</h5>
                    <ul class="list-unstyled">
                        <li><a href="/about">About Us</a></li>
                        <li><a href="/contact">Contact</a></li>
                        <li><a href="/terms">Terms & Conditions</a></li>
                        <li><a href="/privacy">Privacy Policy</a></li>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h5>Connect With Us</h5>
                    <div class="social-links">
                        <a href="#" class="me-2"><i class="fab fa-facebook"></i></a>
                        <a href="#" class="me-2"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="me-2"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="me-2"><i class="fab fa-linkedin"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="/js/main.js"></script>
</body>
</html>