const { Notification, Customer, Store, Admin } = require('../models');
const { Op } = require('sequelize');
const logger = require('../utils/logger');

class CustomerNotificationsController {
    // عرض إشعارات العميل
    async index(req, res) {
        try {
            const customerId = req.user.id;

            const page = parseInt(req.query.page) || 1;
            const limit = parseInt(req.query.limit) || 12;
            const offset = (page - 1) * limit;
            const type = req.query.type;
            const priority = req.query.priority;
            const status = req.query.status;

            // بناء شروط البحث
            const whereClause = {
                customerId: customerId,
                expiresAt: {
                    [Op.or]: [
                        null,
                        { [Op.gt]: new Date() }
                    ]
                }
            };

            if (type) whereClause.type = type;
            if (priority) whereClause.priority = priority;
            if (status === 'read') whereClause.readAt = { [Op.not]: null };
            if (status === 'unread') whereClause.readAt = null;

            const { count, rows: notifications } = await Notification.findAndCountAll({
                where: whereClause,
                include: [
                    { model: Store, as: 'store', required: false },
                    { model: Admin, as: 'admin', required: false }
                ],
                order: [
                    ['priority', 'DESC'],
                    ['createdAt', 'DESC']
                ],
                limit,
                offset
            });

            const totalPages = Math.ceil(count / limit);

            // حساب الإحصائيات
            const stats = await this.getCustomerNotificationStats(customerId);

            res.render('customers/notifications/index', {
                notifications,
                currentPage: page,
                totalPages,
                totalCount: count,
                filters: { type, priority, status },
                hasNextPage: page < totalPages,
                hasPrevPage: page > 1,
                nextPage: page + 1,
                prevPage: page - 1,
                ...stats
            });
        } catch (error) {
            logger.error('Error fetching customer notifications:', error);
            res.status(500).render('error', { error });
        }
    }

    // جلب إحصائيات الإشعارات للعميل
    async getCustomerNotificationStats(customerId) {
        try {
            const [
                unreadCount,
                orderNotificationsCount,
                promotionNotificationsCount,
                totalCount
            ] = await Promise.all([
                Notification.count({
                    where: {
                        customerId: customerId,
                        readAt: null,
                        expiresAt: {
                            [Op.or]: [null, { [Op.gt]: new Date() }]
                        }
                    }
                }),
                Notification.count({
                    where: {
                        customerId: customerId,
                        type: 'order',
                        expiresAt: {
                            [Op.or]: [null, { [Op.gt]: new Date() }]
                        }
                    }
                }),
                Notification.count({
                    where: {
                        customerId: customerId,
                        type: 'promotion',
                        expiresAt: {
                            [Op.or]: [null, { [Op.gt]: new Date() }]
                        }
                    }
                }),
                Notification.count({
                    where: {
                        customerId: customerId,
                        expiresAt: {
                            [Op.or]: [null, { [Op.gt]: new Date() }]
                        }
                    }
                })
            ]);

            return {
                unreadCount,
                orderNotificationsCount,
                promotionNotificationsCount,
                totalCount
            };
        } catch (error) {
            logger.error('Error getting customer notification stats:', error);
            return {
                unreadCount: 0,
                orderNotificationsCount: 0,
                promotionNotificationsCount: 0,
                totalCount: 0
            };
        }
    }

    // وضع الإشعار كمقروء
    async markAsRead(req, res) {
        try {
            const customerId = req.user.id;
            const notificationId = req.params.id;

            const notification = await Notification.findOne({
                where: {
                    id: notificationId,
                    customerId: customerId
                }
            });

            if (!notification) {
                return res.status(404).json({ success: false, message: 'الإشعار غير موجود' });
            }

            await notification.update({ readAt: new Date() });

            if (req.xhr || req.headers.accept?.includes('application/json')) {
                return res.json({ success: true, message: 'تم وضع الإشعار كمقروء' });
            }

            req.flash('success', 'تم وضع الإشعار كمقروء');
            res.redirect('/customers/notifications');
        } catch (error) {
            logger.error('Error marking notification as read:', error);

            if (req.xhr || req.headers.accept?.includes('application/json')) {
                return res.status(500).json({ success: false, message: 'حدث خطأ' });
            }

            res.status(500).render('error', { error });
        }
    }

    // وضع جميع الإشعارات كمقروءة
    async markAllAsRead(req, res) {
        try {
            const customerId = req.user.id;

            await Notification.update(
                { readAt: new Date() },
                {
                    where: {
                        customerId: customerId,
                        readAt: null
                    }
                }
            );

            if (req.xhr || req.headers.accept?.includes('application/json')) {
                return res.json({ success: true, message: 'تم وضع جميع الإشعارات كمقروءة' });
            }

            req.flash('success', 'تم وضع جميع الإشعارات كمقروءة');
            res.redirect('/customers/notifications');
        } catch (error) {
            logger.error('Error marking all notifications as read:', error);

            if (req.xhr || req.headers.accept?.includes('application/json')) {
                return res.status(500).json({ success: false, message: 'حدث خطأ' });
            }

            res.status(500).render('error', { error });
        }
    }

    // API: جلب عدد الإشعارات غير المقروءة
    async getUnreadCount(req, res) {
        try {
            const customerId = req.user.id;

            const count = await Notification.count({
                where: {
                    customerId: customerId,
                    readAt: null,
                    expiresAt: {
                        [Op.or]: [null, { [Op.gt]: new Date() }]
                    }
                }
            });

            res.json({ success: true, count });
        } catch (error) {
            logger.error('Error getting unread count:', error);
            res.status(500).json({ success: false, message: 'حدث خطأ' });
        }
    }

    // API: جلب الإشعارات الحديثة للعميل
    async getRecentNotifications(req, res) {
        try {
            const customerId = req.user.id;
            const limit = parseInt(req.query.limit) || 5;

            const notifications = await Notification.findAll({
                where: {
                    customerId: customerId,
                    expiresAt: {
                        [Op.or]: [null, { [Op.gt]: new Date() }]
                    }
                },
                order: [
                    ['priority', 'DESC'],
                    ['createdAt', 'DESC']
                ],
                limit,
                include: [
                    { model: Store, as: 'store', required: false, attributes: ['id', 'name', 'userName'] },
                    { model: Admin, as: 'admin', required: false, attributes: ['id', 'userName'] }
                ]
            });

            res.json({
                success: true,
                notifications: notifications.map(notification => ({
                    id: notification.id,
                    title: notification.title,
                    message: notification.message,
                    type: notification.type,
                    priority: notification.priority,
                    readAt: notification.readAt,
                    createdAt: notification.createdAt,
                    actionUrl: notification.actionUrl,
                    actionText: notification.actionText,
                    store: notification.store,
                    admin: notification.admin
                }))
            });
        } catch (error) {
            logger.error('Error getting recent notifications:', error);
            res.status(500).json({ success: false, message: 'حدث خطأ' });
        }
    }

    // عرض تفاصيل الإشعار
    async show(req, res) {
        try {
            const customerId = req.user.id;
            const notificationId = req.params.id;

            const notification = await Notification.findOne({
                where: {
                    id: notificationId,
                    customerId: customerId
                },
                include: [
                    { model: Store, as: 'store', required: false },
                    { model: Admin, as: 'admin', required: false }
                ]
            });

            if (!notification) {
                return res.status(404).render('error', {
                    error: { status: 404, message: 'الإشعار غير موجود' }
                });
            }

            // وضع الإشعار كمقروء تلقائياً عند عرضه
            if (!notification.readAt) {
                await notification.update({ readAt: new Date() });
            }

            res.render('customers/notifications/show', { notification });
        } catch (error) {
            logger.error('Error showing notification:', error);
            res.status(500).render('error', { error });
        }
    }

    // حذف الإشعار
    async delete(req, res) {
        try {
            const customerId = req.user.id;
            const notificationId = req.params.id;

            const notification = await Notification.findOne({
                where: {
                    id: notificationId,
                    customerId: customerId
                }
            });

            if (!notification) {
                return res.status(404).json({ success: false, message: 'الإشعار غير موجود' });
            }

            await notification.destroy();

            if (req.xhr || req.headers.accept?.includes('application/json')) {
                return res.json({ success: true, message: 'تم حذف الإشعار بنجاح' });
            }

            req.flash('success', 'تم حذف الإشعار بنجاح');
            res.redirect('/customers/notifications');
        } catch (error) {
            logger.error('Error deleting notification:', error);

            if (req.xhr || req.headers.accept?.includes('application/json')) {
                return res.status(500).json({ success: false, message: 'حدث خطأ' });
            }

            res.status(500).render('error', { error });
        }
    }
}

module.exports = new CustomerNotificationsController();
