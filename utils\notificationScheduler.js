const cron = require('node-cron');
const NotificationService = require('../services/NotificationService');
const logger = require('./logger');

class NotificationScheduler {
    static init() {
        // تنظيف الإشعارات المنتهية الصلاحية كل يوم في الساعة 2:00 صباحاً
        cron.schedule('0 2 * * *', async () => {
            try {
                logger.info('Starting scheduled cleanup of expired notifications');
                const deletedCount = await NotificationService.cleanupExpiredNotifications();
                logger.info(`Scheduled cleanup completed: ${deletedCount} expired notifications deleted`);
            } catch (error) {
                logger.error('Error in scheduled notification cleanup:', error);
            }
        });

        // إرسال تقرير إحصائيات الإشعارات كل أسبوع يوم الاثنين في الساعة 9:00 صباحاً
        cron.schedule('0 9 * * 1', async () => {
            try {
                logger.info('Generating weekly notification statistics');
                const stats = await NotificationService.getNotificationStats();
                
                // يمكن إرسال هذه الإحصائيات للمشرفين
                await NotificationService.notifyAllAdmins({
                    title: 'تقرير إحصائيات الإشعارات الأسبوعي',
                    message: `تقرير إحصائيات الإشعارات للأسبوع الماضي:\n${JSON.stringify(stats, null, 2)}`,
                    type: 'system',
                    priority: 'low',
                    data: { type: 'weekly_stats', stats }
                });

                logger.info('Weekly notification statistics sent to admins');
            } catch (error) {
                logger.error('Error generating weekly notification statistics:', error);
            }
        });

        // تذكير بالإشعارات غير المقروءة كل 6 ساعات
        cron.schedule('0 */6 * * *', async () => {
            try {
                logger.info('Checking for unread notifications reminder');
                // يمكن إضافة منطق تذكير المستخدمين بالإشعارات غير المقروءة
                // هذا مثال بسيط - يمكن تطويره أكثر
            } catch (error) {
                logger.error('Error in unread notifications reminder:', error);
            }
        });

        logger.info('Notification scheduler initialized');
    }

    static async sendTestNotification() {
        try {
            await NotificationService.notifyAllAdmins({
                title: 'اختبار نظام الإشعارات',
                message: 'هذا إشعار تجريبي للتأكد من عمل النظام بشكل صحيح.',
                type: 'system',
                priority: 'normal',
                data: { type: 'test' }
            });
            logger.info('Test notification sent successfully');
        } catch (error) {
            logger.error('Error sending test notification:', error);
        }
    }

    static async sendWelcomeNotification(userId, userType) {
        try {
            const welcomeData = {
                title: 'مرحباً بك!',
                message: 'نرحب بك في منصتنا. نتمنى لك تجربة ممتعة ومفيدة.',
                type: 'success',
                priority: 'normal',
                data: { type: 'welcome' }
            };

            if (userType === 'customer') {
                await NotificationService.notifyCustomer(userId, welcomeData);
            } else if (userType === 'store') {
                await NotificationService.notifyStore(userId, welcomeData);
            } else if (userType === 'admin') {
                await NotificationService.notifyAdmin(userId, welcomeData);
            }

            logger.info(`Welcome notification sent to ${userType}:${userId}`);
        } catch (error) {
            logger.error('Error sending welcome notification:', error);
        }
    }

    static async sendSystemMaintenanceNotification(maintenanceDate, duration) {
        try {
            const maintenanceData = {
                title: 'صيانة مجدولة للنظام',
                message: `سيتم إجراء صيانة مجدولة للنظام في ${maintenanceDate} لمدة ${duration}. قد تواجه انقطاعاً مؤقتاً في الخدمة.`,
                type: 'warning',
                priority: 'high',
                data: { 
                    type: 'maintenance',
                    date: maintenanceDate,
                    duration: duration
                }
            };

            await NotificationService.notifyAll(maintenanceData);
            logger.info('System maintenance notification sent to all users');
        } catch (error) {
            logger.error('Error sending system maintenance notification:', error);
        }
    }

    static async sendPromotionNotification(promotionData) {
        try {
            await NotificationService.notifyPromotion(promotionData);
            logger.info('Promotion notification sent');
        } catch (error) {
            logger.error('Error sending promotion notification:', error);
        }
    }

    static async sendOrderReminder(orderId, customerId, reminderType = 'pending') {
        try {
            const reminderMessages = {
                'pending': 'لديك طلب معلق يحتاج للمراجعة',
                'payment': 'يرجى إكمال عملية الدفع لطلبك',
                'pickup': 'طلبك جاهز للاستلام'
            };

            const reminderData = {
                title: 'تذكير بالطلب',
                message: `${reminderMessages[reminderType]} رقم ${orderId}`,
                type: 'order',
                priority: 'normal',
                actionUrl: `/customers/orders/${orderId}`,
                actionText: 'عرض الطلب',
                data: { 
                    type: 'order_reminder',
                    orderId: orderId,
                    reminderType: reminderType
                }
            };

            await NotificationService.notifyCustomer(customerId, reminderData);
            logger.info(`Order reminder sent for order ${orderId} to customer ${customerId}`);
        } catch (error) {
            logger.error('Error sending order reminder:', error);
        }
    }

    static async sendLowStockAlert(productId, storeId, currentStock) {
        try {
            const alertData = {
                title: 'تنبيه: مخزون منخفض',
                message: `المنتج رقم ${productId} لديه مخزون منخفض (${currentStock} قطعة متبقية)`,
                type: 'warning',
                priority: 'high',
                actionUrl: `/store/products/${productId}/edit`,
                actionText: 'تحديث المخزون',
                data: { 
                    type: 'low_stock',
                    productId: productId,
                    currentStock: currentStock
                }
            };

            await NotificationService.notifyStore(storeId, alertData);
            logger.info(`Low stock alert sent for product ${productId} to store ${storeId}`);
        } catch (error) {
            logger.error('Error sending low stock alert:', error);
        }
    }

    static async sendNewUserRegistrationAlert(userId, userType, userName) {
        try {
            const alertData = {
                title: 'تسجيل مستخدم جديد',
                message: `تم تسجيل ${userType === 'customer' ? 'عميل' : 'متجر'} جديد: ${userName}`,
                type: 'info',
                priority: 'normal',
                actionUrl: userType === 'customer' ? `/admin/customers/${userId}` : `/admin/stores/${userId}`,
                actionText: 'عرض التفاصيل',
                data: { 
                    type: 'new_registration',
                    userId: userId,
                    userType: userType,
                    userName: userName
                }
            };

            await NotificationService.notifyAllAdmins(alertData);
            logger.info(`New user registration alert sent for ${userType}:${userId}`);
        } catch (error) {
            logger.error('Error sending new user registration alert:', error);
        }
    }

    static async sendSecurityAlert(userId, userType, alertType, details) {
        try {
            const alertMessages = {
                'login_attempt': 'محاولة دخول مشبوهة',
                'password_change': 'تم تغيير كلمة المرور',
                'account_locked': 'تم قفل الحساب',
                'suspicious_activity': 'نشاط مشبوه'
            };

            const alertData = {
                title: 'تنبيه أمني',
                message: `${alertMessages[alertType] || 'تنبيه أمني'}: ${details}`,
                type: 'error',
                priority: 'urgent',
                data: { 
                    type: 'security_alert',
                    userId: userId,
                    userType: userType,
                    alertType: alertType,
                    details: details,
                    timestamp: new Date()
                }
            };

            // إرسال للمستخدم المعني
            if (userType === 'customer') {
                await NotificationService.notifyCustomer(userId, alertData);
            } else if (userType === 'store') {
                await NotificationService.notifyStore(userId, alertData);
            }

            // إرسال للمشرفين أيضاً
            await NotificationService.notifyAllAdmins({
                ...alertData,
                title: `تنبيه أمني - ${userType}:${userId}`,
                actionUrl: `/admin/${userType}s/${userId}`,
                actionText: 'مراجعة الحساب'
            });

            logger.info(`Security alert sent for ${userType}:${userId} - ${alertType}`);
        } catch (error) {
            logger.error('Error sending security alert:', error);
        }
    }
}

module.exports = NotificationScheduler;
