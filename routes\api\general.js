const express = require('express');
const router = express.Router();
const GeneralApiController = require('../../controllers/api/GeneralApiController');
const { optionalAuth } = require('../../middleware/apiAuth');

/**
 * مسارات عامة (لا تحتاج مصادقة)
 */

// الفئات
router.get('/categories', optionalAuth, GeneralApiController.getCategories);
router.get('/categories/:id', optionalAuth, GeneralApiController.getCategoryById);

// المدن
router.get('/countries', optionalAuth, GeneralApiController.getCountries);

// المناطق
router.get('/areas', optionalAuth, GeneralApiController.getAreas);
router.get('/areas/:id', optionalAuth, GeneralApiController.getAreaById);

// البحث العام
router.get('/search', optionalAuth, GeneralApiController.globalSearch);

module.exports = router;
