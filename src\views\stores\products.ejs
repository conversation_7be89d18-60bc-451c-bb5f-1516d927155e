<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>إدارة المنتجات</h1>
        <a href="/store/products/create" class="btn btn-primary">
            <i class="fas fa-plus"></i> إضافة منتج جديد
        </a>
    </div>

    <!-- مكون البحث والفلتر -->
    <%- include('../partials/search-filter', {
        filters: filters || {},
        sortOptions: sortOptions || [],
        customFilters: '../partials/filters/products-filters',
        currentUrl: currentUrl || '',
        pagination: pagination || null
    }) %>

    <div class="table-responsive">
        <table class="table table-striped table-hover">
            <thead class="thead-dark">
                <tr>
                    <th>
                        <a href="?sortBy=id&sortOrder=<%= (filters.sortBy === 'id' && filters.sortOrder === 'asc') ? 'desc' : 'asc' %>"
                           class="text-white text-decoration-none">
                            ID
                            <% if (filters.sortBy === 'id') { %>
                                <i class="fas fa-sort-<%= filters.sortOrder === 'asc' ? 'up' : 'down' %>"></i>
                            <% } else { %>
                                <i class="fas fa-sort text-muted"></i>
                            <% } %>
                        </a>
                    </th>
                    <th>
                        <a href="?sortBy=name&sortOrder=<%= (filters.sortBy === 'name' && filters.sortOrder === 'asc') ? 'desc' : 'asc' %>"
                           class="text-white text-decoration-none">
                            اسم المنتج
                            <% if (filters.sortBy === 'name') { %>
                                <i class="fas fa-sort-<%= filters.sortOrder === 'asc' ? 'up' : 'down' %>"></i>
                            <% } else { %>
                                <i class="fas fa-sort text-muted"></i>
                            <% } %>
                        </a>
                    </th>
                    <th>الوصف</th>
                    <th>
                        <a href="?sortBy=price&sortOrder=<%= (filters.sortBy === 'price' && filters.sortOrder === 'asc') ? 'desc' : 'asc' %>"
                           class="text-white text-decoration-none">
                            السعر
                            <% if (filters.sortBy === 'price') { %>
                                <i class="fas fa-sort-<%= filters.sortOrder === 'asc' ? 'up' : 'down' %>"></i>
                            <% } else { %>
                                <i class="fas fa-sort text-muted"></i>
                            <% } %>
                        </a>
                    </th>
                    <th>
                        <a href="?sortBy=quantity&sortOrder=<%= (filters.sortBy === 'quantity' && filters.sortOrder === 'asc') ? 'desc' : 'asc' %>"
                           class="text-white text-decoration-none">
                            الكمية
                            <% if (filters.sortBy === 'quantity') { %>
                                <i class="fas fa-sort-<%= filters.sortOrder === 'asc' ? 'up' : 'down' %>"></i>
                            <% } else { %>
                                <i class="fas fa-sort text-muted"></i>
                            <% } %>
                        </a>
                    </th>
                    <th>الفئة</th>
                    <th>الحالة</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                <% products.forEach(product => { %>
                    <tr>
                        <td><%= product.id %></td>
                        <td><%= product.name %></td>
                        <td>
                            <% if (product.description && product.description.length > 50) { %>
                                <%= product.description.substring(0, 50) %>...
                            <% } else { %>
                                <%= product.description || 'لا يوجد وصف' %>
                            <% } %>
                        </td>
                        <td>
                            <span class="badge badge-success">$<%= product.price %></span>
                        </td>
                        <td>
                            <% if (product.quantity > 10) { %>
                                <span class="badge badge-success"><%= product.quantity %></span>
                            <% } else if (product.quantity > 0) { %>
                                <span class="badge badge-warning"><%= product.quantity %></span>
                            <% } else { %>
                                <span class="badge badge-danger">نفد المخزون</span>
                            <% } %>
                        </td>
                        <td>
                            <% if (product.category) { %>
                                <span class="badge badge-info"><%= product.category %></span>
                            <% } else { %>
                                <span class="text-muted">غير محدد</span>
                            <% } %>
                        </td>
                        <td>
                            <% if (product.status === 'active') { %>
                                <span class="badge badge-success">نشط</span>
                            <% } else if (product.status === 'inactive') { %>
                                <span class="badge badge-danger">غير نشط</span>
                            <% } else { %>
                                <span class="badge badge-secondary">غير محدد</span>
                            <% } %>
                        </td>
                        <td class="action-buttons">
                            <div class="btn-group" role="group">
                                <a href="/store/products/<%= product.id %>"
                                   class="btn btn-sm btn-info"
                                   title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="/store/products/<%= product.id %>/edit"
                                   class="btn btn-sm btn-warning"
                                   title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form action="/store/products/<%= product.id %>/delete" method="POST" class="d-inline">
                                    <button type="submit"
                                            class="btn btn-sm btn-danger"
                                            title="حذف"
                                            onclick="return confirm('هل أنت متأكد من حذف هذا المنتج؟')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                <% }); %>

                <% if (!products || products.length === 0) { %>
                    <tr>
                        <td colspan="8" class="text-center py-4">
                            <div class="text-muted">
                                <i class="fas fa-box fa-3x mb-3"></i>
                                <p class="mb-0">لا توجد منتجات مطابقة للبحث</p>
                                <% if (Object.keys(filters || {}).length > 0) { %>
                                    <a href="/store/products" class="btn btn-sm btn-primary mt-2">
                                        <i class="fas fa-times"></i> مسح الفلاتر
                                    </a>
                                <% } %>
                            </div>
                        </td>
                    </tr>
                <% } %>
            </tbody>
        </table>
    </div>

    <!-- الـ Pagination الجديد -->
    <%- include('../partials/pagination', {
        pagination: pagination || null,
        currentUrl: currentUrl || '',
        originalUrl: originalUrl || ''
    }) %>
</div>