<h1>منتجات المتجر</h1>

<table class="table">
  <thead>
    <tr>
      <th>الاسم</th>
      <th>الوصف</th>
      <th>السعر</th>
      <th>الكمية</th>
      <th>التحكم</th>
    </tr>
  </thead>
  <tbody>
    <% products.forEach(product => { %>
      <tr>
        <td><%= product.name %></td>
        <td><%= product.description || 'لا يوجد' %></td>
        <td><%= product.price %> $</td>
        <td><%= product.quantity %></td>
        <td>
          <a href="/store/products/<%= product.id %>/edit %>" class="btn btn-sm btn-primary">عرض</a>
        </td>
      </tr>
    <% }); %>
  </tbody>
</table>

<% if (totalPages > 1) { %>
<nav aria-label="Page navigation" class="d-flex justify-content-center mt-4">
  <ul class="pagination">
    <% if (currentPage > 1) { %>
      <li class="page-item">
        <a class="page-link" href="?page=<%= currentPage - 1 %>">السابق</a>
      </li>
    <% } else { %>
      <li class="page-item disabled">
        <span class="page-link">السابق</span>
      </li>
    <% } %>

    <% for(let i = 1; i <= totalPages; i++) { %>
      <li class="page-item <%= currentPage === i ? 'active' : '' %>">
        <a class="page-link" href="?page=<%= i %>"><%= i %></a>
      </li>
    <% } %>

    <% if (currentPage < totalPages) { %>
      <li class="page-item">
        <a class="page-link" href="?page=<%= currentPage + 1 %>">التالي</a>
      </li>
    <% } else { %>
      <li class="page-item disabled">
        <span class="page-link">التالي</span>
      </li>
    <% } %>
  </ul>
</nav>
<% } %>