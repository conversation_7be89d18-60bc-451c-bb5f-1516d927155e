<div class="container">
    <div class="jumbotron text-center mt-5">
        <h1 class="display-4">Welcome to Store Management System</h1>
        <p class="lead">Manage your store operations efficiently</p>
        
        <% if (typeof session !== 'undefined' && session.customerId) { %>
            <p>Welcome back, <%= session.customerName %>!</p>
            <div class="mt-4">
                <a href="/orders" class="btn btn-primary me-2">View Orders</a>
                <a href="/products" class="btn btn-success">Browse Products</a>
            </div>
        <% } else { %>
            <p>Please login or register to access the system</p>
            <div class="mt-4">
                <a href="/auth/login" class="btn btn-primary me-2">Login</a>
                <a href="/auth/register" class="btn btn-success">Register</a>
            </div>
        <% } %>
    </div>
</div> 