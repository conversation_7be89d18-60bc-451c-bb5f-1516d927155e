<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Create New Area</h1>
        <a href="/admin/areas" class="btn btn-secondary">Back to Areas</a>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-body">
                    <form action="/admin/areas" method="POST">
                        <div class="mb-3">
                            <label for="name" class="form-label">Area Name</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>

                        <div class="mb-3">
                            <label for="countryId" class="form-label">Country</label>
                            <select class="form-control" id="countryId" name="countryId" required>
                                <option value="">Select a Country</option>
                                <% countries.forEach(country => { %>
                                    <option value="<%= country.id %>" 
                                            <%= countryId && country.id == countryId ? 'selected' : '' %>>
                                        <%= country.name %>
                                    </option>
                                <% }); %>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="notes" class="form-label">Notes</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                        </div>

                        <button type="submit" class="btn btn-primary">Create Area</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div> 