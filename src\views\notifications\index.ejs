<div class="container-fluid">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">
                <i class="fas fa-bell text-primary"></i>
                إدارة الإشعارات
            </h1>
            <p class="text-muted mb-0">إجمالي الإشعارات: <%= totalCount %></p>
        </div>
        <div class="btn-group">
            <a href="/notifications/create" class="btn btn-primary">
                <i class="fas fa-plus"></i> إنشاء إشعار جديد
            </a>
            <button type="button" class="btn btn-outline-secondary" onclick="markAllAsRead()">
                <i class="fas fa-check-double"></i> وضع الكل كمقروء
            </button>
        </div>
    </div>

    <!-- Filters Section -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="/notifications" class="row g-3">
                <div class="col-md-3">
                    <label for="search" class="form-label">البحث</label>
                    <input type="text" class="form-control" id="search" name="search"
                           value="<%= filters.search || '' %>" placeholder="البحث في العنوان أو الرسالة">
                </div>
                <div class="col-md-2">
                    <label for="type" class="form-label">النوع</label>
                    <select class="form-select" id="type" name="type">
                        <option value="">جميع الأنواع</option>
                        <option value="info" <%= filters.type === 'info' ? 'selected' : '' %>>معلومات</option>
                        <option value="success" <%= filters.type === 'success' ? 'selected' : '' %>>نجاح</option>
                        <option value="warning" <%= filters.type === 'warning' ? 'selected' : '' %>>تحذير</option>
                        <option value="error" <%= filters.type === 'error' ? 'selected' : '' %>>خطأ</option>
                        <option value="order" <%= filters.type === 'order' ? 'selected' : '' %>>طلب</option>
                        <option value="promotion" <%= filters.type === 'promotion' ? 'selected' : '' %>>عرض</option>
                        <option value="system" <%= filters.type === 'system' ? 'selected' : '' %>>نظام</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="priority" class="form-label">الأولوية</label>
                    <select class="form-select" id="priority" name="priority">
                        <option value="">جميع الأولويات</option>
                        <option value="low" <%= filters.priority === 'low' ? 'selected' : '' %>>منخفضة</option>
                        <option value="normal" <%= filters.priority === 'normal' ? 'selected' : '' %>>عادية</option>
                        <option value="high" <%= filters.priority === 'high' ? 'selected' : '' %>>عالية</option>
                        <option value="urgent" <%= filters.priority === 'urgent' ? 'selected' : '' %>>عاجلة</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="status" class="form-label">الحالة</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">جميع الحالات</option>
                        <option value="unread" <%= filters.status === 'unread' ? 'selected' : '' %>>غير مقروء</option>
                        <option value="read" <%= filters.status === 'read' ? 'selected' : '' %>>مقروء</option>
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-outline-primary me-2">
                        <i class="fas fa-search"></i> بحث
                    </button>
                    <a href="/notifications" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i> مسح
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Notifications List -->
    <div class="row">
        <% if (notifications.length > 0) { %>
            <% notifications.forEach(notification => { %>
                <div class="col-lg-6 col-xl-4 mb-4">
                    <div class="card h-100 notification-card <%= !notification.readAt ? 'unread' : '' %> priority-<%= notification.priority %>">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <span class="badge bg-<%= getTypeColor(notification.type) %> me-2">
                                    <i class="fas fa-<%= getTypeIcon(notification.type) %>"></i>
                                    <%= getTypeLabel(notification.type) %>
                                </span>
                                <span class="badge bg-<%= getPriorityColor(notification.priority) %>">
                                    <%= getPriorityLabel(notification.priority) %>
                                </span>
                            </div>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="/notifications/<%= notification.id %>">
                                        <i class="fas fa-eye"></i> عرض التفاصيل
                                    </a></li>
                                    <% if (!notification.readAt) { %>
                                        <li><a class="dropdown-item" href="#" onclick="markAsRead(<%= notification.id %>)">
                                            <i class="fas fa-check"></i> وضع كمقروء
                                        </a></li>
                                    <% } %>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item text-danger" href="#" onclick="deleteNotification(<%= notification.id %>)">
                                        <i class="fas fa-trash"></i> حذف
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                        <div class="card-body">
                            <h6 class="card-title"><%= notification.title %></h6>
                            <p class="card-text text-muted">
                                <%= notification.message.length > 100 ? notification.message.substring(0, 100) + '...' : notification.message %>
                            </p>

                            <div class="notification-meta">
                                <small class="text-muted d-block">
                                    <i class="fas fa-user"></i>
                                    من: <%= notification.admin ? notification.admin.name :
                                           notification.store ? notification.store.userName : 'النظام' %>
                                </small>
                                <small class="text-muted d-block">
                                    <i class="fas fa-users"></i>
                                    إلى: <%= notification.customer ? notification.customer.name :
                                           notification.store ? 'المتجر' :
                                           notification.admin ? 'المشرف' : 'جميع المستخدمين' %>
                                </small>
                                <small class="text-muted d-block">
                                    <i class="fas fa-clock"></i>
                                    <%= new Date(notification.createdAt).toLocaleString('ar-SA') %>
                                </small>
                                <% if (notification.expiresAt) { %>
                                    <small class="text-warning d-block">
                                        <i class="fas fa-hourglass-end"></i>
                                        ينتهي: <%= new Date(notification.expiresAt).toLocaleString('ar-SA') %>
                                    </small>
                                <% } %>
                            </div>
                        </div>
                        <% if (notification.actionUrl && notification.actionText) { %>
                            <div class="card-footer">
                                <a href="<%= notification.actionUrl %>" class="btn btn-sm btn-primary">
                                    <%= notification.actionText %>
                                </a>
                            </div>
                        <% } %>
                        <% if (!notification.readAt) { %>
                            <div class="unread-indicator"></div>
                        <% } %>
                    </div>
                </div>
            <% }); %>
        <% } else { %>
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">لا توجد إشعارات</h4>
                    <p class="text-muted">لم يتم العثور على أي إشعارات تطابق معايير البحث</p>
                    <a href="/notifications/create" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إنشاء إشعار جديد
                    </a>
                </div>
            </div>
        <% } %>
    </div>

    <!-- Pagination -->
    <% if (totalPages > 1) { %>
        <nav aria-label="تنقل الصفحات" class="mt-4">
            <ul class="pagination justify-content-center">
                <li class="page-item <%= !hasPrevPage ? 'disabled' : '' %>">
                    <a class="page-link" href="?page=<%= prevPage %>&<%= new URLSearchParams(filters).toString() %>">
                        <i class="fas fa-chevron-right"></i> السابق
                    </a>
                </li>

                <% for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) { %>
                    <li class="page-item <%= i === currentPage ? 'active' : '' %>">
                        <a class="page-link" href="?page=<%= i %>&<%= new URLSearchParams(filters).toString() %>">
                            <%= i %>
                        </a>
                    </li>
                <% } %>

                <li class="page-item <%= !hasNextPage ? 'disabled' : '' %>">
                    <a class="page-link" href="?page=<%= nextPage %>&<%= new URLSearchParams(filters).toString() %>">
                        التالي <i class="fas fa-chevron-left"></i>
                    </a>
                </li>
            </ul>
        </nav>
    <% } %>
</div>

<!-- Helper Functions -->
<%
function getTypeColor(type) {
    const colors = {
        'info': 'info',
        'success': 'success',
        'warning': 'warning',
        'error': 'danger',
        'order': 'primary',
        'promotion': 'success',
        'system': 'secondary'
    };
    return colors[type] || 'secondary';
}

function getTypeIcon(type) {
    const icons = {
        'info': 'info-circle',
        'success': 'check-circle',
        'warning': 'exclamation-triangle',
        'error': 'times-circle',
        'order': 'shopping-cart',
        'promotion': 'tag',
        'system': 'cog'
    };
    return icons[type] || 'bell';
}

function getTypeLabel(type) {
    const labels = {
        'info': 'معلومات',
        'success': 'نجاح',
        'warning': 'تحذير',
        'error': 'خطأ',
        'order': 'طلب',
        'promotion': 'عرض',
        'system': 'نظام'
    };
    return labels[type] || type;
}

function getPriorityColor(priority) {
    const colors = {
        'low': 'secondary',
        'normal': 'info',
        'high': 'warning',
        'urgent': 'danger'
    };
    return colors[priority] || 'secondary';
}

function getPriorityLabel(priority) {
    const labels = {
        'low': 'منخفضة',
        'normal': 'عادية',
        'high': 'عالية',
        'urgent': 'عاجلة'
    };
    return labels[priority] || priority;
}
%>

<script>
// Mark notification as read
function markAsRead(notificationId) {
    fetch(`/notifications/${notificationId}/read`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('حدث خطأ: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في الاتصال');
    });
}

// Mark all notifications as read
function markAllAsRead() {
    if (!confirm('هل أنت متأكد من وضع جميع الإشعارات كمقروءة؟')) {
        return;
    }

    fetch('/notifications/mark-all-read', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        },
        body: JSON.stringify({
            userType: 'admin', // يمكن تعديل هذا حسب نوع المستخدم
            userId: 1 // يمكن تعديل هذا حسب معرف المستخدم
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('حدث خطأ: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في الاتصال');
    });
}

// Delete notification
function deleteNotification(notificationId) {
    if (!confirm('هل أنت متأكد من حذف هذا الإشعار؟')) {
        return;
    }

    fetch(`/notifications/${notificationId}/delete`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('حدث خطأ: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في الاتصال');
    });
}
</script>