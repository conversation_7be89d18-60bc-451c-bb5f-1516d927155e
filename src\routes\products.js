const express = require('express');
const router = express.Router();
const productsController = require('../controllers/ProductsController');

router.get('/', productsController.index.bind(productsController));
router.get('/create', productsController.create.bind(productsController));
router.post('/', productsController.store.bind(productsController));
router.get('/:id/edit', productsController.edit.bind(productsController));
router.post('/:id', productsController.update.bind(productsController));
router.post('/:id/delete', productsController.delete.bind(productsController));

module.exports = router;
