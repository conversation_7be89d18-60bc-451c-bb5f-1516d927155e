const BaseController = require('./BaseController');
const { Country, Area } = require('../models');
class CountriesController extends BaseController {
    constructor() {
        super(Country, 'countries');
    }

    async index(req, res) {
        const page = parseInt(req.query.page) || 1;
        const limit = 20;
        const offset = (page - 1) * limit;
    
        try {
            const { count, rows: countries } = await Country.findAndCountAll({
                include: [{ model: Area, as: 'areas' }],
                limit,
                offset,
                order: [['createdAt', 'DESC']]
            });
    
            const totalPages = Math.ceil(count / limit);
    
            res.render('countries/index', {
                countries,
                currentPage: page,
                totalPages
            });
        } catch (error) {
            console.error('Error fetching countries:', error);
            res.status(500).render('error', { error: { message: 'Unable to fetch countries' } });
        }
    }
    
    async show(req, res) {
        try {
            const country = await Country.findByPk(req.params.id, {
                include: [{ model: Area, as: 'areas' }]
            });
            if (!country) {
                return res.status(404).render('error', { error: 'Country not found' });
            }
            res.render('countries/show', { country });
        } catch (error) {
            res.status(500).render('error', { error });
        }
    }

    // عرض نموذج إنشاء دولة جديدة
    async create(req, res) {
        try {
            res.render('countries/create');
        } catch (error) {
            console.error('Error loading create country form:', error);
            res.status(500).render('error', { error: { message: 'Unable to load create form' } });
        }
    }

    // حفظ دولة جديدة
    async store(req, res) {
        try {
            const { name } = req.body;
            if (!name) {
                return res.status(400).render('error', { error: { message: 'Name is required' } });
            }
            await Country.create({ name });
            req.flash('success', 'Country created successfully');
            res.redirect('/admin/countries');
        } catch (error) {
            console.error('Error creating country:', error);
            res.status(500).render('error', { error: { message: 'Unable to create country' } });
        }
    }

    // عرض نموذج تعديل دولة موجودة
    async edit(req, res) {
        try {
            const country = await Country.findByPk(req.params.id);
            if (!country) {
                return res.status(404).render('error', { error: { message: 'Country not found' } });
            }
            res.render('countries/edit', { country });
        } catch (error) {
            console.error('Error loading edit country form:', error);
            res.status(500).render('error', { error: { message: 'Unable to load edit form' } });
        }
    }

    // تحديث دولة موجودة
    async update(req, res) {
        try {
            const country = await Country.findByPk(req.params.id);
            if (!country) {
                return res.status(404).render('error', { error: { message: 'Country not found' } });
            }
            await country.update(req.body);
            req.flash('success', 'Country updated successfully');
            res.redirect('/admin/countries');
        } catch (error) {
            console.error('Error updating country:', error);
            res.status(500).render('error', { error: { message: 'Unable to update country' } });
        }
    }

    // حذف دولة موجودة
    async delete(req, res) {
        try {
            const country = await Country.findByPk(req.params.id);
            if (!country) {
                return res.status(404).render('error', { error: { message: 'Country not found' } });
            }
            await country.destroy();
            req.flash('success', 'Country deleted successfully');
            res.redirect('/admin/countries');
        } catch (error) {
            console.error('Error deleting country:', error);
            res.status(500).render('error', { error: { message: 'Unable to delete country' } });
        }
    }
}

module.exports = new CountriesController();
