const db = require('../models');

async function ensureTablesExist() {
    try {
        await db.sequelize.authenticate();
        console.log('✅ Connection has been established successfully.');

        // التحقق من وجود الجداول بدلاً من إعادة إنشائها
        const tables = await db.sequelize.getQueryInterface().showAllTables();
        console.log('✅ Existing tables:', tables);

        // التحقق من الجداول المفقودة
        const modelNames = Object.keys(db).filter(key => 
            typeof db[key] === 'function' && 
            db[key].prototype && 
            db[key].prototype.constructor.name !== 'Sequelize'
        );
        
        console.log('✅ Models loaded:', modelNames);
        
        const missingTables = modelNames.filter(model => 
            !tables.includes(db[model].tableName || model.toLowerCase())
        );
        
        if (missingTables.length > 0) {
            console.warn('⚠️ Missing tables:', missingTables);
            // يمكن إضافة منطق لإنشاء الجداول المفقودة فقط
        }

        return true;
    } catch (error) {
        console.error('❌ Unable to connect to the database:', error);
        throw error;
    }
}

module.exports = ensureTablesExist;
