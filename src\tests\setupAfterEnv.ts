import { sequelize } from '../models';

// Global setup before all tests
beforeAll(async () => {
    // Connect to test database
    await sequelize.authenticate();
});

// Global teardown after all tests
afterAll(async () => {
    // Close database connection
    await sequelize.close();
});

// Reset database between tests
beforeEach(async () => {
    // Add any database cleanup here
    // For example: await sequelize.truncate({ cascade: true });
}); 