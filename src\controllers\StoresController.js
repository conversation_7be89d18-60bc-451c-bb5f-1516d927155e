const BaseController = require('./BaseController');
const { Store, Area, Customer } = require('../models');

class StoresController extends BaseController {
    constructor() {
        super(Store, 'stores');
    }

    // عرض قائمة المتاجر مع المناطق والعملاء
    async index(req, res) {
        try {
            const stores = await Store.findAll({
                include: [{
                    model: Area,
                    as: 'area',
                    include: [{
                        model: Customer,
                        as: 'customers',
                        through: 'CustomerArea'
                    }]
                }]
            });
            res.render('stores/index', { stores });
        } catch (error) {
            console.error('Error fetching stores:', error);
            res.status(500).render('error', { error });
        }
    }

    // عرض نموذج تعديل متجر مع بيانات المناطق والعملاء
    async edit(req, res) {
        try {
            const [store, areas] = await Promise.all([
                Store.findByPk(req.params.id, {
                    include: [{
                        model: Area,
                        as: 'area',
                        include: [{
                            model: Customer,
                            as: 'customers',
                            through: 'CustomerArea'
                        }]
                    }]
                }),
                Area.findAll({
                    include: [{
                        model: Customer,
                        as: 'customers',
                        through: 'CustomerArea'
                    }]
                })
            ]);

            if (!store) {
                return res.status(404).render('error', { error: 'Store not found' });
            }

            res.render('stores/edit', { store, areas });
        } catch (error) {
            console.error('Error fetching store or areas for edit:', error);
            res.status(500).render('error', { error });
        }
    }
}

module.exports = new StoresController();
