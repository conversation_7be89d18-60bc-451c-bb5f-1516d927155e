# نظام إدارة المتاجر الذكي - ملف الإعدادات البيئية

# إعدادات الخادم
NODE_ENV=development
PORT=3001
HOST=localhost

# إعدادات قاعدة البيانات
DB_HOST=localhost
DB_NAME=smart_store_system
DB_USER=root
DB_PASS=
DB_PORT=3306
DB_DIALECT=mysql

# إعدادات الأمان
SESSION_SECRET=your-super-secret-session-key-change-this-in-production
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
BCRYPT_ROUNDS=12

# إعدادات Redis (اختياري)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=
REDIS_DB=0

# إعدادات رفع الملفات
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=10MB
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx

# إعدادات البريد الإلكتروني
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
EMAIL_FROM=<EMAIL>

# إعدادات الموقع
SITE_NAME=نظام إدارة المتاجر الذكي
SITE_URL=http://localhost:3001
ADMIN_EMAIL=<EMAIL>
SUPPORT_EMAIL=<EMAIL>

# إعدادات HTTPS
ENABLE_HTTPS=false
SSL_CERT_PATH=
SSL_KEY_PATH=

# إعدادات الأمان المتقدم
ENABLE_HELMET=true
ENABLE_CORS=true
CORS_ORIGIN=http://localhost:3001
ENABLE_COMPRESSION=true
ENABLE_RATE_LIMITING=true
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100

# إعدادات السجلات
LOG_LEVEL=info
LOG_FILE=./logs/app.log
ERROR_LOG_FILE=./logs/error.log
ACCESS_LOG_FILE=./logs/access.log

# إعدادات النسخ الاحتياطي
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_PATH=./backups
BACKUP_RETENTION_DAYS=30

# إعدادات الإشعارات
NOTIFICATION_ENABLED=true
NOTIFICATION_UPDATE_INTERVAL=30000
PUSH_NOTIFICATION_KEY=
PUSH_NOTIFICATION_SECRET=

# إعدادات التخزين المؤقت
CACHE_ENABLED=true
CACHE_TTL=3600
CACHE_MAX_SIZE=100

# إعدادات API
API_VERSION=v1
API_RATE_LIMIT=1000
API_TIMEOUT=30000
ENABLE_API_DOCS=true

# إعدادات وضع الصيانة
MAINTENANCE_MODE=false
MAINTENANCE_MESSAGE=الموقع تحت الصيانة، يرجى المحاولة لاحقاً

# إعدادات التطوير
DEBUG=false
ENABLE_MOCK_DATA=false
ENABLE_PROFILING=false

# إعدادات قاعدة البيانات المتقدمة
DB_POOL_MAX=10
DB_POOL_MIN=0
DB_POOL_ACQUIRE=30000
DB_POOL_IDLE=10000

# إعدادات الجلسات
SESSION_NAME=smart-store-session
SESSION_MAX_AGE=86400000
SESSION_SECURE=false
SESSION_HTTP_ONLY=true
SESSION_SAME_SITE=lax

# إعدادات الأداء
ENABLE_CLUSTERING=false
CLUSTER_WORKERS=auto
MEMORY_LIMIT=1024
CPU_LIMIT=80

# إعدادات المراقبة
ENABLE_MONITORING=false
MONITORING_ENDPOINT=/metrics
HEALTH_CHECK_ENDPOINT=/health

# إعدادات التحليلات
ENABLE_ANALYTICS=false
ANALYTICS_KEY=
ANALYTICS_SECRET=

# إعدادات الدفع (للمستقبل)
PAYMENT_GATEWAY=
PAYMENT_PUBLIC_KEY=
PAYMENT_SECRET_KEY=
PAYMENT_WEBHOOK_SECRET=

# إعدادات الرسائل النصية (للمستقبل)
SMS_PROVIDER=
SMS_API_KEY=
SMS_API_SECRET=
SMS_FROM_NUMBER=

# إعدادات التخزين السحابي (للمستقبل)
CLOUD_STORAGE_PROVIDER=
CLOUD_STORAGE_BUCKET=
CLOUD_STORAGE_ACCESS_KEY=
CLOUD_STORAGE_SECRET_KEY=
CLOUD_STORAGE_REGION=

# إعدادات CDN (للمستقبل)
CDN_ENABLED=false
CDN_URL=
CDN_API_KEY=

# إعدادات الأمان الإضافية
ENABLE_2FA=false
ENABLE_CAPTCHA=false
CAPTCHA_SITE_KEY=
CAPTCHA_SECRET_KEY=

# إعدادات التوطين
DEFAULT_LANGUAGE=ar
SUPPORTED_LANGUAGES=ar,en
TIMEZONE=Asia/Riyadh

# إعدادات الشبكات الاجتماعية (للمستقبل)
FACEBOOK_APP_ID=
FACEBOOK_APP_SECRET=
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=
TWITTER_API_KEY=
TWITTER_API_SECRET=

# إعدادات التقارير
ENABLE_REPORTS=true
REPORT_CACHE_TTL=7200
REPORT_MAX_RECORDS=10000

# إعدادات البحث
SEARCH_ENGINE=mysql
ELASTICSEARCH_URL=
ELASTICSEARCH_INDEX=

# إعدادات الذكاء الاصطناعي (للمستقبل)
AI_ENABLED=false
AI_API_KEY=
AI_MODEL=

# إعدادات الاختبار
TEST_DB_NAME=smart_store_system_test
TEST_DB_USER=test_user
TEST_DB_PASS=test_password

# إعدادات Docker
DOCKER_REGISTRY=
DOCKER_IMAGE_TAG=latest

# إعدادات CI/CD
CI_ENABLED=false
DEPLOY_KEY=
WEBHOOK_SECRET=
