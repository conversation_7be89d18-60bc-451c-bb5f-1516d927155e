<div class="container mt-4">
  <h3>Add Delivery</h3>
  <form action="/store/<%= storeId %>/deliveries" method="POST">
    <div class="mb-3">
      <label for="deliveryPersonId" class="form-label">Delivery Person</label>
      <select id="deliveryPersonId" name="deliveryPersonId" class="form-select" required>
        <option value="">Select Delivery Person</option>
        <% deliveryPeople.forEach(person => { %>
          <option value="<%= person.id %>" <%= selectedDeliveryPersonId == person.id ? 'selected' : '' %>>
            <%= person.name %>
          </option>
        <% }); %>
      </select>
    </div>

    <div class="mb-3">
      <label for="orderId" class="form-label">Order</label>
      <select name="orderId" class="form-control">
        <% pendingOrders.forEach(order => { %>
          <option value="<%= order.id %>" <%= formData.orderId == order.id ? 'selected' : '' %>>
            #<%= order.id %> - <%= order.customer.name %>
          </option>
        <% }); %>
      </select>
    </div>

    <div class="mb-3">
      <label for="status" class="form-label">Status</label>
      <select id="status" name="status" class="form-select" required>
        <option value="pending" <%= formData.status === 'pending' ? 'selected' : '' %>>Pending</option>
        <option value="in_transit" <%= formData.status === 'in_transit' ? 'selected' : '' %>>In Transit</option>
        <option value="delivered" <%= formData.status === 'delivered' ? 'selected' : '' %>>Delivered</option>
        <option value="cancelled" <%= formData.status === 'cancelled' ? 'selected' : '' %>>Cancelled</option>
      </select>
    </div>

    <div class="mb-3">
      <label for="pickupTime" class="form-label">Pickup Time</label>
      <input type="datetime-local" id="pickupTime" name="pickupTime" class="form-control" value="<%= formData.pickupTime || '' %>">
    </div>

    <div class="mb-3">
      <label for="deliveryTime" class="form-label">Delivery Time</label>
      <input type="datetime-local" id="deliveryTime" name="deliveryTime" class="form-control" value="<%= formData.deliveryTime || '' %>">
    </div>

    <div class="mb-3">
      <label for="notes" class="form-label">Notes</label>
      <textarea id="notes" name="notes" class="form-control" rows="3"><%= formData.notes || '' %></textarea>
    </div>

    <button type="submit" class="btn btn-primary">Create Delivery</button>
  </form>
</div>
