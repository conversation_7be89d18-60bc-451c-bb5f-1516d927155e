<h3>Delivery People</h3>
<a href="/store/<%= storeId %>/delivery-people/create" class="btn btn-primary mb-3">Add Delivery Person</a>

<table class="table table-bordered">
  <thead>
    <tr>
      <th>Name</th>
      <th>Phone</th>
      <th>Total Deliveries</th>
      <th>Actions</th>
    </tr>
  </thead>
  <tbody>
    <% deliveryPeople.forEach(person => { %>
      <tr>
        <td><%= person.name %></td>
        <td><%= person.phoneNumber %></td>
        <td>
          <a href="/store/<%= storeId %>/delivery-people/<%= person.id %>/deliveries">
            <%= person.deliveries ? person.deliveries.length : 0 %>
          </a>
        </td>
        <td>
           <!-- زر إضافة طلب توصيل (دلفري) -->
          <a href="/store/<%= storeId %>/deliveries/create?deliveryPersonId=<%= person.id %>" class="btn btn-success btn-sm ms-1">
            Add Delivery
          </a>
          <a href="/store/<%= storeId %>/delivery-people/<%= person.id %>/edit" class="btn btn-warning btn-sm">Edit</a>
          <form action="/store/<%= storeId %>/delivery-people/<%= person.id %>/delete" method="POST" class="d-inline" onsubmit="return confirm('Are you sure?')">
            <button type="submit" class="btn btn-danger btn-sm">Delete</button>
          </form>
        </td>
      </tr>
    <% }) %>
  </tbody>
</table>

<% if (totalPages > 1) { %>
  <nav aria-label="Page navigation" class="d-flex justify-content-center mt-4">
    <ul class="pagination">
      <% if (currentPage > 1) { %>
        <li class="page-item">
          <a class="page-link" href="?page=<%= currentPage - 1 %>">السابق</a>
        </li>
      <% } else { %>
        <li class="page-item disabled">
          <span class="page-link">السابق</span>
        </li>
      <% } %>
  
      <% for(let i = 1; i <= totalPages; i++) { %>
        <li class="page-item <%= currentPage === i ? 'active' : '' %>">
          <a class="page-link" href="?page=<%= i %>"><%= i %></a>
        </li>
      <% } %>
  
      <% if (currentPage < totalPages) { %>
        <li class="page-item">
          <a class="page-link" href="?page=<%= currentPage + 1 %>">التالي</a>
        </li>
      <% } else { %>
        <li class="page-item disabled">
          <span class="page-link">التالي</span>
        </li>
      <% } %>
    </ul>
  </nav>
  <% } %>