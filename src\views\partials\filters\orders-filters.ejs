<!-- فلاتر مخصصة للطلبات -->

<!-- فلتر الحالة -->
<div class="col-md-3 mb-3">
    <label for="status" class="form-label">حالة الطلب</label>
    <select class="form-control" id="status" name="status">
        <option value="">جميع الحالات</option>
        <option value="pending" <%= (filters.status === 'pending') ? 'selected' : '' %>>في الانتظار</option>
        <option value="confirmed" <%= (filters.status === 'confirmed') ? 'selected' : '' %>>مؤكد</option>
        <option value="preparing" <%= (filters.status === 'preparing') ? 'selected' : '' %>>قيد التحضير</option>
        <option value="ready" <%= (filters.status === 'ready') ? 'selected' : '' %>>جاهز للتوصيل</option>
        <option value="out_for_delivery" <%= (filters.status === 'out_for_delivery') ? 'selected' : '' %>>في الطريق</option>
        <option value="delivered" <%= (filters.status === 'delivered') ? 'selected' : '' %>>تم التوصيل</option>
        <option value="cancelled" <%= (filters.status === 'cancelled') ? 'selected' : '' %>>ملغي</option>
    </select>
</div>

<!-- فلتر المبلغ الإجمالي -->
<div class="col-md-3 mb-3">
    <label for="totalAmount_min" class="form-label">المبلغ من</label>
    <input type="number" 
           class="form-control" 
           id="totalAmount_min" 
           name="totalAmount_min" 
           value="<%= filters.totalAmount_min || '' %>"
           placeholder="0"
           min="0"
           step="0.01">
</div>

<div class="col-md-3 mb-3">
    <label for="totalAmount_max" class="form-label">المبلغ إلى</label>
    <input type="number" 
           class="form-control" 
           id="totalAmount_max" 
           name="totalAmount_max" 
           value="<%= filters.totalAmount_max || '' %>"
           placeholder="1000"
           min="0"
           step="0.01">
</div>

<!-- فلتر تاريخ الطلب -->
<div class="col-md-3 mb-3">
    <label for="createdAt_from" class="form-label">تاريخ الطلب من</label>
    <input type="date" 
           class="form-control" 
           id="createdAt_from" 
           name="createdAt_from" 
           value="<%= filters.createdAt_from || '' %>">
</div>

<div class="col-md-3 mb-3">
    <label for="createdAt_to" class="form-label">تاريخ الطلب إلى</label>
    <input type="date" 
           class="form-control" 
           id="createdAt_to" 
           name="createdAt_to" 
           value="<%= filters.createdAt_to || '' %>">
</div>

<!-- فلتر نوع الدفع -->
<div class="col-md-3 mb-3">
    <label for="paymentMethod" class="form-label">طريقة الدفع</label>
    <select class="form-control" id="paymentMethod" name="paymentMethod">
        <option value="">جميع الطرق</option>
        <option value="cash" <%= (filters.paymentMethod === 'cash') ? 'selected' : '' %>>نقدي</option>
        <option value="card" <%= (filters.paymentMethod === 'card') ? 'selected' : '' %>>بطاقة</option>
        <option value="online" <%= (filters.paymentMethod === 'online') ? 'selected' : '' %>>دفع إلكتروني</option>
    </select>
</div>

<!-- فلتر حالة التوصيل -->
<div class="col-md-3 mb-3">
    <label for="deliveryStatus" class="form-label">حالة التوصيل</label>
    <select class="form-control" id="deliveryStatus" name="deliveryStatus">
        <option value="">جميع الحالات</option>
        <option value="pending" <%= (filters.deliveryStatus === 'pending') ? 'selected' : '' %>>في الانتظار</option>
        <option value="assigned" <%= (filters.deliveryStatus === 'assigned') ? 'selected' : '' %>>تم التعيين</option>
        <option value="picked_up" <%= (filters.deliveryStatus === 'picked_up') ? 'selected' : '' %>>تم الاستلام</option>
        <option value="delivered" <%= (filters.deliveryStatus === 'delivered') ? 'selected' : '' %>>تم التوصيل</option>
        <option value="failed" <%= (filters.deliveryStatus === 'failed') ? 'selected' : '' %>>فشل التوصيل</option>
    </select>
</div>
