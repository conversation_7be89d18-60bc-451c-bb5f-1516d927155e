'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class Country extends Model {
    static associate(models) {
      Country.hasMany(models.Area, {
        foreignKey: 'countryId',
        as: 'areas'
      });
    }
  }
  
  Country.init({
    name: {
      type: DataTypes.STRING,
      allowNull: false
    },
    notes: DataTypes.TEXT
  }, {
    sequelize,
    modelName: 'Country',
  });
  
  return Country;
}; 