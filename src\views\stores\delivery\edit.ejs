<h1>Edit Delivery #<%= delivery.id %></h1>
<form action="/store/deliveries/<%= delivery.id %>" method="POST">
  <div class="mb-3"><label>Status</label>
    <select name="status" class="form-select">
      <% ['pending','in_transit','delivered','cancelled'].forEach(s => { %>
        <option value="<%= s %>" <%= delivery.status===s?'selected':'' %>><%= s %></option>
      <% }) %>
    </select>
  </div>
  <div class="mb-3"><label>Pickup Time</label><input type="datetime-local" name="pickupTime" value="<%= delivery.pickupTime?.toISOString().slice(0,16) %>" class="form-control"></div>
  <div class="mb-3"><label>Delivery Time</label><input type="datetime-local" name="deliveryTime" value="<%= delivery.deliveryTime?.toISOString().slice(0,16) %>" class="form-control"></div>
  <div class="mb-3"><label>Notes</label><textarea name="notes" class="form-control"><%= delivery.notes %></textarea></div>
  <button class="btn btn-success">Update</button>
</form>