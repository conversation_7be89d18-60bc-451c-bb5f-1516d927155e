<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
      <h2 class="fw-bold mb-1">Products in "<%= store.name %>"</h2>
      <a href="/customers/home" class="btn btn-secondary">← Back</a>
    </div>
  
        <div class="mb-4">
          <div class="row row-cols-1 row-cols-md-3 g-4">
            <% products.forEach(product => { %>
              <div class="col">
                <div class="col">
                    <a href="/customers/products/<%= product.id %>" style="text-decoration: none; color: inherit;">
                      <span class="badge bg-success">
                        <%= product.quantity %>
                      </span>
                      <div class="card h-100 shadow-sm border-0">
                        <div class="d-flex flex-wrap mb-3 gap-2">
                          <% if (product.images && product.images.length > 0) { %>
                            <% product.images.forEach(img => { %>
                              <img 
                              src="<%= img.image %>"
                                alt="Product image" 
                                style="max-width: 120px; max-height: 120px; object-fit: cover; border-radius: 6px; border: 1px solid #ddd;"
                              >
                            <% }) %>
                          <% } else if (product.image) { %>
                            <img 
                            src="<%= product.image %>" 
                              class="card-img-top mb-3" 
                              alt="<%= product.name %>"
                            >
                          <% } %>
                        </div>   
                        <div class="card-body">
                          <h5 class="card-title fw-bold"><%= product.name %></h5>
                          <p class="card-text"><%= product.description ? product.description.substring(0, 80) + '...' : '' %></p>
                          <div class="d-flex justify-content-between align-items-center">
                            <span class="fw-bold text-success">$<%= product.price.toFixed(2) %></span>
                            <span class="badge bg-<%= product.status === 'active' ? 'success' : 'danger' %>">
                              <%= product.status %>
                            </span>
                          </div>
                        </div>
                      </div>
                    </a>
                  </div>
                  
              </div>
            <% }); %>
          </div>
        </div>
  </div>
