const { Notification, Customer, Store, Admin } = require('../models');
const { Op } = require('sequelize');
const logger = require('../utils/logger');

class StoreNotificationsController {
    // عرض إشعارات المتجر
    async index(req, res) {
        try {
            const storeId = req.user.id;

            const page = parseInt(req.query.page) || 1;
            const limit = parseInt(req.query.limit) || 12;
            const offset = (page - 1) * limit;
            const type = req.query.type;
            const priority = req.query.priority;
            const status = req.query.status;

            // بناء شروط البحث
            const whereClause = {
                storeId: storeId,
                expiresAt: {
                    [Op.or]: [
                        null,
                        { [Op.gt]: new Date() }
                    ]
                }
            };

            if (type) whereClause.type = type;
            if (priority) whereClause.priority = priority;
            if (status === 'read') whereClause.readAt = { [Op.not]: null };
            if (status === 'unread') whereClause.readAt = null;

            const { count, rows: notifications } = await Notification.findAndCountAll({
                where: whereClause,
                include: [
                    { model: Customer, as: 'customer', required: false },
                    { model: Admin, as: 'admin', required: false }
                ],
                order: [
                    ['priority', 'DESC'],
                    ['createdAt', 'DESC']
                ],
                limit,
                offset
            });

            const totalPages = Math.ceil(count / limit);

            // حساب الإحصائيات
            const stats = await this.getStoreNotificationStats(storeId);

            res.render('store/notifications/index', {
                notifications,
                currentPage: page,
                totalPages,
                totalCount: count,
                filters: { type, priority, status },
                hasNextPage: page < totalPages,
                hasPrevPage: page > 1,
                nextPage: page + 1,
                prevPage: page - 1,
                ...stats
            });
        } catch (error) {
            logger.error('Error fetching store notifications:', error);
            res.status(500).render('error', { error });
        }
    }

    // جلب إحصائيات الإشعارات للمتجر
    async getStoreNotificationStats(storeId) {
        try {
            const [
                unreadCount,
                orderNotificationsCount,
                totalCount
            ] = await Promise.all([
                Notification.count({
                    where: {
                        storeId: storeId,
                        readAt: null,
                        expiresAt: {
                            [Op.or]: [null, { [Op.gt]: new Date() }]
                        }
                    }
                }),
                Notification.count({
                    where: {
                        storeId: storeId,
                        type: 'order',
                        expiresAt: {
                            [Op.or]: [null, { [Op.gt]: new Date() }]
                        }
                    }
                }),
                Notification.count({
                    where: {
                        storeId: storeId,
                        expiresAt: {
                            [Op.or]: [null, { [Op.gt]: new Date() }]
                        }
                    }
                })
            ]);

            return {
                unreadCount,
                orderNotificationsCount,
                totalCount
            };
        } catch (error) {
            logger.error('Error getting store notification stats:', error);
            return {
                unreadCount: 0,
                orderNotificationsCount: 0,
                totalCount: 0
            };
        }
    }

    // وضع الإشعار كمقروء
    async markAsRead(req, res) {
        try {
            const storeId = req.session.storeId;
            const notificationId = req.params.id;

            if (!storeId) {
                return res.status(401).json({ success: false, message: 'غير مصرح' });
            }

            const notification = await Notification.findOne({
                where: {
                    id: notificationId,
                    storeId: storeId
                }
            });

            if (!notification) {
                return res.status(404).json({ success: false, message: 'الإشعار غير موجود' });
            }

            await notification.update({ readAt: new Date() });

            if (req.xhr || req.headers.accept?.includes('application/json')) {
                return res.json({ success: true, message: 'تم وضع الإشعار كمقروء' });
            }

            req.flash('success', 'تم وضع الإشعار كمقروء');
            res.redirect('/store/notifications');
        } catch (error) {
            logger.error('Error marking notification as read:', error);

            if (req.xhr || req.headers.accept?.includes('application/json')) {
                return res.status(500).json({ success: false, message: 'حدث خطأ' });
            }

            res.status(500).render('error', { error });
        }
    }

    // وضع جميع الإشعارات كمقروءة
    async markAllAsRead(req, res) {
        try {
            const storeId = req.session.storeId;

            if (!storeId) {
                return res.status(401).json({ success: false, message: 'غير مصرح' });
            }

            await Notification.update(
                { readAt: new Date() },
                {
                    where: {
                        storeId: storeId,
                        readAt: null
                    }
                }
            );

            if (req.xhr || req.headers.accept?.includes('application/json')) {
                return res.json({ success: true, message: 'تم وضع جميع الإشعارات كمقروءة' });
            }

            req.flash('success', 'تم وضع جميع الإشعارات كمقروءة');
            res.redirect('/store/notifications');
        } catch (error) {
            logger.error('Error marking all notifications as read:', error);

            if (req.xhr || req.headers.accept?.includes('application/json')) {
                return res.status(500).json({ success: false, message: 'حدث خطأ' });
            }

            res.status(500).render('error', { error });
        }
    }

    // API: جلب عدد الإشعارات غير المقروءة
    async getUnreadCount(req, res) {
        try {
            const storeId = req.session.storeId;

            if (!storeId) {
                return res.status(401).json({ success: false, message: 'غير مصرح' });
            }

            const count = await Notification.count({
                where: {
                    storeId: storeId,
                    readAt: null,
                    expiresAt: {
                        [Op.or]: [null, { [Op.gt]: new Date() }]
                    }
                }
            });

            res.json({ success: true, count });
        } catch (error) {
            logger.error('Error getting unread count:', error);
            res.status(500).json({ success: false, message: 'حدث خطأ' });
        }
    }

    // API: جلب الإشعارات الحديثة للمتجر
    async getRecentNotifications(req, res) {
        try {
            const storeId = req.user.id;
            const limit = parseInt(req.query.limit) || 5;

            const notifications = await Notification.findAll({
                where: {
                    storeId: storeId,
                    expiresAt: {
                        [Op.or]: [null, { [Op.gt]: new Date() }]
                    }
                },
                order: [
                    ['priority', 'DESC'],
                    ['createdAt', 'DESC']
                ],
                limit,
                include: [
                    { model: Customer, as: 'customer', required: false, attributes: ['id', 'name'] },
                    { model: Admin, as: 'admin', required: false, attributes: ['id', 'userName'] }
                ]
            });

            res.json({
                success: true,
                notifications: notifications.map(notification => ({
                    id: notification.id,
                    title: notification.title,
                    message: notification.message,
                    type: notification.type,
                    priority: notification.priority,
                    readAt: notification.readAt,
                    createdAt: notification.createdAt,
                    actionUrl: notification.actionUrl,
                    actionText: notification.actionText,
                    customer: notification.customer,
                    admin: notification.admin
                }))
            });
        } catch (error) {
            logger.error('Error getting recent notifications:', error);
            res.status(500).json({ success: false, message: 'حدث خطأ' });
        }
    }

    // عرض تفاصيل الإشعار
    async show(req, res) {
        try {
            const storeId = req.user.id;
            const notificationId = req.params.id;

            const notification = await Notification.findOne({
                where: {
                    id: notificationId,
                    storeId: storeId
                },
                include: [
                    { model: Customer, as: 'customer', required: false },
                    { model: Admin, as: 'admin', required: false }
                ]
            });

            if (!notification) {
                return res.status(404).render('error', {
                    error: { status: 404, message: 'الإشعار غير موجود' }
                });
            }

            // وضع الإشعار كمقروء تلقائياً عند عرضه
            if (!notification.readAt) {
                await notification.update({ readAt: new Date() });
            }

            res.render('store/notifications/show', { notification });
        } catch (error) {
            logger.error('Error showing notification:', error);
            res.status(500).render('error', { error });
        }
    }

    // حذف الإشعار
    async delete(req, res) {
        try {
            const storeId = req.user.id;
            const notificationId = req.params.id;

            const notification = await Notification.findOne({
                where: {
                    id: notificationId,
                    storeId: storeId
                }
            });

            if (!notification) {
                return res.status(404).json({ success: false, message: 'الإشعار غير موجود' });
            }

            await notification.destroy();

            if (req.xhr || req.headers.accept?.includes('application/json')) {
                return res.json({ success: true, message: 'تم حذف الإشعار بنجاح' });
            }

            req.flash('success', 'تم حذف الإشعار بنجاح');
            res.redirect('/store/notifications');
        } catch (error) {
            logger.error('Error deleting notification:', error);

            if (req.xhr || req.headers.accept?.includes('application/json')) {
                return res.status(500).json({ success: false, message: 'حدث خطأ' });
            }

            res.status(500).render('error', { error });
        }
    }
}

module.exports = new StoreNotificationsController();
