<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-plus-circle text-primary"></i>
                        إنشاء إشعار جديد
                    </h4>
                </div>
                <div class="card-body">
                    <form action="/notifications" method="POST" id="notificationForm">
                        <!-- Basic Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-info-circle"></i> المعلومات الأساسية
                                </h5>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-8">
                                <label for="title" class="form-label">العنوان <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="title" name="title"
                                       value="<%= notification.title || '' %>" required
                                       placeholder="أدخل عنوان الإشعار">
                                <div class="form-text">عنوان مختصر وواضح للإشعار</div>
                            </div>
                            <div class="col-md-4">
                                <label for="type" class="form-label">نوع الإشعار</label>
                                <select class="form-select" id="type" name="type">
                                    <option value="info" <%= (notification.type || 'info') === 'info' ? 'selected' : '' %>>
                                        <i class="fas fa-info-circle"></i> معلومات
                                    </option>
                                    <option value="success" <%= notification.type === 'success' ? 'selected' : '' %>>
                                        <i class="fas fa-check-circle"></i> نجاح
                                    </option>
                                    <option value="warning" <%= notification.type === 'warning' ? 'selected' : '' %>>
                                        <i class="fas fa-exclamation-triangle"></i> تحذير
                                    </option>
                                    <option value="error" <%= notification.type === 'error' ? 'selected' : '' %>>
                                        <i class="fas fa-times-circle"></i> خطأ
                                    </option>
                                    <option value="order" <%= notification.type === 'order' ? 'selected' : '' %>>
                                        <i class="fas fa-shopping-cart"></i> طلب
                                    </option>
                                    <option value="promotion" <%= notification.type === 'promotion' ? 'selected' : '' %>>
                                        <i class="fas fa-tag"></i> عرض
                                    </option>
                                    <option value="system" <%= notification.type === 'system' ? 'selected' : '' %>>
                                        <i class="fas fa-cog"></i> نظام
                                    </option>
                                </select>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="priority" class="form-label">الأولوية</label>
                                <select class="form-select" id="priority" name="priority">
                                    <option value="low" <%= notification.priority === 'low' ? 'selected' : '' %>>
                                        منخفضة
                                    </option>
                                    <option value="normal" <%= (notification.priority || 'normal') === 'normal' ? 'selected' : '' %>>
                                        عادية
                                    </option>
                                    <option value="high" <%= notification.priority === 'high' ? 'selected' : '' %>>
                                        عالية
                                    </option>
                                    <option value="urgent" <%= notification.priority === 'urgent' ? 'selected' : '' %>>
                                        عاجلة
                                    </option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="expiresAt" class="form-label">تاريخ انتهاء الصلاحية</label>
                                <input type="datetime-local" class="form-control" id="expiresAt" name="expiresAt"
                                       value="<%= notification.expiresAt || '' %>">
                                <div class="form-text">اتركه فارغاً إذا كان الإشعار لا ينتهي</div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="message" class="form-label">نص الإشعار <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="message" name="message" rows="4" required
                                      placeholder="أدخل نص الإشعار التفصيلي"><%= notification.message || '' %></textarea>
                            <div class="form-text">النص الكامل للإشعار الذي سيراه المستخدمون</div>
                        </div>

                        <!-- Action Settings -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-mouse-pointer"></i> إعدادات الإجراء (اختياري)
                                </h5>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-8">
                                <label for="actionUrl" class="form-label">رابط الإجراء</label>
                                <input type="url" class="form-control" id="actionUrl" name="actionUrl"
                                       value="<%= notification.actionUrl || '' %>"
                                       placeholder="https://example.com/action">
                                <div class="form-text">الرابط الذي سيتم توجيه المستخدم إليه عند النقر على زر الإجراء</div>
                            </div>
                            <div class="col-md-4">
                                <label for="actionText" class="form-label">نص زر الإجراء</label>
                                <input type="text" class="form-control" id="actionText" name="actionText"
                                       value="<%= notification.actionText || '' %>"
                                       placeholder="عرض التفاصيل">
                                <div class="form-text">النص الذي سيظهر على زر الإجراء</div>
                            </div>
                        </div>

                        <!-- Recipients -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-users"></i> المستقبلون
                                </h5>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="sendToAll" name="sendToAll"
                                       onchange="toggleRecipientSelection()">
                                <label class="form-check-label" for="sendToAll">
                                    <strong>إرسال لجميع المستخدمين</strong>
                                </label>
                                <div class="form-text">سيتم إرسال الإشعار لجميع العملاء والمتاجر</div>
                            </div>
                        </div>

                        <div id="specificRecipients">
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <label for="customerId" class="form-label">عميل محدد</label>
                                    <select class="form-select" id="customerId" name="customerId">
                                        <option value="">اختر عميل</option>
                                        <% customers.forEach(customer => { %>
                                            <option value="<%= customer.id %>"
                                                    <%= notification.customerId == customer.id ? 'selected' : '' %>>
                                                <%= customer.name %> (<%= customer.email %>)
                                            </option>
                                        <% }); %>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label for="storeId" class="form-label">متجر محدد</label>
                                    <select class="form-select" id="storeId" name="storeId">
                                        <option value="">اختر متجر</option>
                                        <% stores.forEach(store => { %>
                                            <option value="<%= store.id %>"
                                                    <%= notification.storeId == store.id ? 'selected' : '' %>>
                                                <%= store.name %> (<%= store.userName %>)
                                            </option>
                                        <% }); %>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label for="adminId" class="form-label">مشرف محدد</label>
                                    <select class="form-select" id="adminId" name="adminId">
                                        <option value="">اختر مشرف</option>
                                        <% admins.forEach(admin => { %>
                                            <option value="<%= admin.id %>"
                                                    <%= notification.adminId == admin.id ? 'selected' : '' %>>
                                                <%= admin.name %> (<%= admin.email %>)
                                            </option>
                                        <% }); %>
                                    </select>
                                </div>
                            </div>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i>
                                <strong>ملاحظة:</strong> إذا لم تحدد أي مستقبل، سيتم إرسال الإشعار كإشعار عام للنظام.
                            </div>
                        </div>

                        <!-- Preview -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-eye"></i> معاينة الإشعار
                                </h5>
                                <div class="card" id="notificationPreview">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <div class="d-flex align-items-center">
                                            <span class="badge me-2" id="previewTypeBadge">
                                                <i class="fas fa-info-circle"></i> معلومات
                                            </span>
                                            <span class="badge" id="previewPriorityBadge">عادية</span>
                                        </div>
                                        <small class="text-muted" id="previewDate">الآن</small>
                                    </div>
                                    <div class="card-body">
                                        <h6 class="card-title" id="previewTitle">عنوان الإشعار</h6>
                                        <p class="card-text text-muted" id="previewMessage">نص الإشعار سيظهر هنا</p>
                                    </div>
                                    <div class="card-footer" id="previewAction" style="display: none;">
                                        <button class="btn btn-sm btn-primary" id="previewActionBtn">عرض التفاصيل</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="d-flex justify-content-between">
                            <a href="/notifications" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane"></i> إرسال الإشعار
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Toggle recipient selection
function toggleRecipientSelection() {
    const sendToAll = document.getElementById('sendToAll').checked;
    const specificRecipients = document.getElementById('specificRecipients');

    if (sendToAll) {
        specificRecipients.style.display = 'none';
        // Clear specific selections
        document.getElementById('customerId').value = '';
        document.getElementById('storeId').value = '';
        document.getElementById('adminId').value = '';
    } else {
        specificRecipients.style.display = 'block';
    }
}

// Update preview
function updatePreview() {
    const title = document.getElementById('title').value || 'عنوان الإشعار';
    const message = document.getElementById('message').value || 'نص الإشعار سيظهر هنا';
    const type = document.getElementById('type').value;
    const priority = document.getElementById('priority').value;
    const actionText = document.getElementById('actionText').value;
    const actionUrl = document.getElementById('actionUrl').value;

    // Update preview content
    document.getElementById('previewTitle').textContent = title;
    document.getElementById('previewMessage').textContent = message;

    // Update type badge
    const typeBadge = document.getElementById('previewTypeBadge');
    const typeLabels = {
        'info': { label: 'معلومات', icon: 'info-circle', color: 'bg-info' },
        'success': { label: 'نجاح', icon: 'check-circle', color: 'bg-success' },
        'warning': { label: 'تحذير', icon: 'exclamation-triangle', color: 'bg-warning' },
        'error': { label: 'خطأ', icon: 'times-circle', color: 'bg-danger' },
        'order': { label: 'طلب', icon: 'shopping-cart', color: 'bg-primary' },
        'promotion': { label: 'عرض', icon: 'tag', color: 'bg-success' },
        'system': { label: 'نظام', icon: 'cog', color: 'bg-secondary' }
    };

    const typeInfo = typeLabels[type] || typeLabels['info'];
    typeBadge.className = `badge me-2 ${typeInfo.color}`;
    typeBadge.innerHTML = `<i class="fas fa-${typeInfo.icon}"></i> ${typeInfo.label}`;

    // Update priority badge
    const priorityBadge = document.getElementById('previewPriorityBadge');
    const priorityLabels = {
        'low': { label: 'منخفضة', color: 'bg-secondary' },
        'normal': { label: 'عادية', color: 'bg-info' },
        'high': { label: 'عالية', color: 'bg-warning' },
        'urgent': { label: 'عاجلة', color: 'bg-danger' }
    };

    const priorityInfo = priorityLabels[priority] || priorityLabels['normal'];
    priorityBadge.className = `badge ${priorityInfo.color}`;
    priorityBadge.textContent = priorityInfo.label;

    // Update action button
    const actionDiv = document.getElementById('previewAction');
    const actionBtn = document.getElementById('previewActionBtn');

    if (actionText && actionUrl) {
        actionDiv.style.display = 'block';
        actionBtn.textContent = actionText;
    } else {
        actionDiv.style.display = 'none';
    }

    // Update date
    document.getElementById('previewDate').textContent = new Date().toLocaleString('ar-SA');
}

// Add event listeners for real-time preview
document.addEventListener('DOMContentLoaded', function() {
    const inputs = ['title', 'message', 'type', 'priority', 'actionText', 'actionUrl'];
    inputs.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('input', updatePreview);
            element.addEventListener('change', updatePreview);
        }
    });

    // Initial preview update
    updatePreview();
});

// Form validation
document.getElementById('notificationForm').addEventListener('submit', function(e) {
    const title = document.getElementById('title').value.trim();
    const message = document.getElementById('message').value.trim();

    if (!title || !message) {
        e.preventDefault();
        alert('يرجى ملء العنوان ونص الإشعار');
        return false;
    }

    const actionUrl = document.getElementById('actionUrl').value.trim();
    const actionText = document.getElementById('actionText').value.trim();

    if ((actionUrl && !actionText) || (!actionUrl && actionText)) {
        e.preventDefault();
        alert('يرجى ملء كل من رابط الإجراء ونص زر الإجراء أو تركهما فارغين');
        return false;
    }

    return true;
});
</script>