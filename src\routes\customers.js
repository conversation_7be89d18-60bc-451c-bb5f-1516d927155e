const express = require('express');
const router = express.Router();
const customersController = require('../controllers/CustomersController');
const authController = require('../controllers/AuthController');
const { auth } = require('../middleware/auth');
// Show login and register forms
router.get('/login', authController.showLogin.bind(authController));
router.get('/register', authController.showRegister.bind(authController));

// Handle form submissions
router.post('/login', authController.login.bind(authController));
router.post('/register', authController.register.bind(authController));

// Logout
router.get('/logout', authController.logout.bind(authController));



router.get('/', authenticateCustomer, customersController.index.bind(customersController));
router.get('/home', authenticateCustomer, customersController.index.bind(customersController));
router.get('/categories/:id', authenticateCustomer, customersController.storesByCategory.bind(customersController));
router.get('/stores/:id', authenticateCustomer, customersController.showStoreProducts.bind(customersController));
router.get('/products/:id', authenticateCustomer, customersController.productDetails.bind(customersController));
router.get('/cart', authenticateCustomer, customersController.showCart.bind(customersController));
router.post('/cart/add', authenticateCustomer, customersController.addToCart.bind(customersController));
router.post('/cart/remove', authenticateCustomer, customersController.removeFromCart.bind(customersController));
router.get('/checkout', authenticateCustomer, customersController.showCheckout.bind(customersController));
router.post('/checkout', authenticateCustomer, customersController.processCheckout.bind(customersController));
router.get('/orders', authenticateCustomer, customersController.getOrders.bind(customersController));
router.get('/profile', authenticateCustomer, customersController.profile.bind(customersController));
router.post('/profile/address/add', authenticateCustomer, customersController.addAddress.bind(customersController));
router.post('/profile/address/:id/delete', authenticateCustomer, customersController.deleteAddress.bind(customersController));
module.exports = router;

