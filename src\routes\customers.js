const express = require('express');
const router = express.Router();
const customersController = require('../controllers/CustomersController');
const authController = require('../controllers/AuthController');
const { auth } = require('../middleware/auth');
// Show login and register forms
router.get('/login', authController.showLogin.bind(authController));
router.get('/register', authController.showRegister.bind(authController));

// Handle form submissions
router.post('/login', authController.login.bind(authController));
router.post('/register', authController.register.bind(authController));

// Logout
router.get('/logout', authController.logout.bind(authController));




module.exports = router;
