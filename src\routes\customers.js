const express = require('express');
const router = express.Router();
const customersController = require('../controllers/CustomersController');
const  authController = require('../controllers/AuthController');

const requireCustomerAuth = (req, res, next) => {
    if (!req.session.customerId) {
      return res.redirect('/customers/login');
    }
    next();
  };
// Show login and register forms
router.get('/login', authController.showLogin.bind(authController));
router.get('/register', authController.showRegister.bind(authController));

// Handle form submissions
router.post('/login', authController.login.bind(authController));
router.post('/register', authController.register.bind(authController));

// Logout
router.get('/logout', authController.logout.bind(authController));

router.get('/',requireCustomerAuth,customersController.index.bind(customersController));
router.get('/home', requireCustomerAuth,customersController.index.bind(customersController));
router.get('/categories/:id', requireCustomerAuth,customersController.storesByCategory.bind(customersController));
router.get('/stores/:id', requireCustomerAuth,customersController.showStoreProducts.bind(customersController));
router.get('/products/:id', requireCustomerAuth,customersController.productDetails.bind(customersController));
router.get('/cart', requireCustomerAuth,customersController.showCart.bind(customersController));
router.post('/cart/add', requireCustomerAuth,customersController.addToCart.bind(customersController));
router.post('/cart/remove', requireCustomerAuth,customersController.removeFromCart.bind(customersController));
router.get('/checkout', requireCustomerAuth,customersController.showCheckout.bind(customersController));
router.post('/checkout', requireCustomerAuth,customersController.processCheckout.bind(customersController));
router.get('/orders', requireCustomerAuth,customersController.getOrders.bind(customersController));
router.get('/profile', requireCustomerAuth,customersController.profile.bind(customersController));
router.post('/profile/address/add', requireCustomerAuth,customersController.addAddress.bind(customersController));
router.post('/profile/address/:id/delete', requireCustomerAuth,customersController.deleteAddress.bind(customersController));
router.get('/create', requireCustomerAuth,customersController.create.bind(customersController));
router.post('/', requireCustomerAuth,customersController.store.bind(customersController));
router.get('/:id/edit', requireCustomerAuth,customersController.edit.bind(customersController));
router.post('/:id', requireCustomerAuth,customersController.update.bind(customersController));
router.post('/:id/delete', requireCustomerAuth,customersController.delete.bind(customersController));

module.exports = router;
