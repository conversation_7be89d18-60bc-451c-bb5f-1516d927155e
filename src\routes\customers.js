const express = require('express');
const router = express.Router();
const customersController = require('../controllers/CustomersController');
const authController = require('../controllers/AuthController');
const { auth } = require('../middleware/auth');
// Show login and register forms
router.get('/login', authController.showLogin.bind(authController));
router.get('/register', authController.showRegister.bind(authController));

// Handle form submissions
router.post('/login', authController.login.bind(authController));
router.post('/register', authController.register.bind(authController));

// Logout
router.get('/logout', authController.logout.bind(authController));

router.get('/', auth.customer, customersController.index.bind(customersController));
router.get('/home', auth.customer, customersController.index.bind(customersController));
router.get('/categories/:id', auth.customer, customersController.storesByCategory.bind(customersController));
router.get('/stores/:id', auth.customer, customersController.showStoreProducts.bind(customersController));
router.get('/products/:id', auth.customer, customersController.productDetails.bind(customersController));
router.get('/cart', auth.customer, customersController.showCart.bind(customersController));
router.post('/cart/add', auth.customer, customersController.addToCart.bind(customersController));
router.post('/cart/remove', auth.customer, customersController.removeFromCart.bind(customersController));
router.get('/checkout', auth.customer, customersController.showCheckout.bind(customersController));
router.post('/checkout', auth.customer, customersController.processCheckout.bind(customersController));
router.get('/orders', auth.customer, customersController.getOrders.bind(customersController));
router.get('/profile', auth.customer, customersController.profile.bind(customersController));
router.post('/profile/address/add', auth.customer, customersController.addAddress.bind(customersController));
router.post('/profile/address/:id/delete', auth.customer, customersController.deleteAddress.bind(customersController));
router.get('/create', auth.customer, customersController.create.bind(customersController));
router.post('/', auth.customer, customersController.store.bind(customersController));
router.get('/:id/edit', auth.customer, customersController.edit.bind(customersController));
router.post('/:id', auth.customer, customersController.update.bind(customersController));
router.post('/:id/delete', auth.customer, customersController.delete.bind(customersController));

module.exports = router;
