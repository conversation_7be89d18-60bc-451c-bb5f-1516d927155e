const BaseController = require('./BaseController');
const { Image, Product } = require('../models');
const path = require('path');
const fs = require('fs').promises;

class ImagesController extends BaseController {
    constructor() {
        super(Image, 'images');
    }

    async index(req, res) {
        try {
            const images = await Image.findAll({
                include: [{ model: Product }],
                order: [['createdAt', 'DESC']]
            });
            res.render('images/index', { images });
        } catch (error) {
            res.status(500).render('error', { error });
        }
    }

    async create(req, res) {
        try {
            const products = await Product.findAll();
            res.render('images/create', { products });
        } catch (error) {
            res.status(500).render('error', { error });
        }
    }

    async store(req, res) {
        try {
            if (!req.files || !req.files.image) {
                return res.status(400).render('images/create', { error: 'Please upload an image.' });
            }

            const { productId } = req.body;
            if (!productId) {
                return res.status(400).render('images/create', { error: 'Product must be selected.' });
            }

            const image = req.files.image;

            // تحقق من نوع الملف
            const allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
            if (!allowedTypes.includes(image.mimetype)) {
                return res.status(400).render('images/create', { error: 'Invalid image format.' });
            }

            const fileName = `${Date.now()}-${image.name}`;
            const uploadPath = path.join(__dirname, '../public/uploads', fileName);

            await image.mv(uploadPath);
            await Image.create({
                path: `/uploads/${fileName}`,
                productId
            });

            req.flash('success', 'Image uploaded successfully');
            res.redirect('/images');
        } catch (error) {
            console.error(error);
            res.status(500).render('error', { error });
        }
    }

    async delete(req, res) {
        try {
            const image = await Image.findByPk(req.params.id);
            if (!image) {
                return res.status(404).render('error', { error: 'Image not found' });
            }

            // حذف الملف من النظام مع تسجيل الخطأ إن حصل
            const filePath = path.join(__dirname, '../public', image.path);
            await fs.unlink(filePath).catch(err => {
                console.error('Failed to delete image file:', err);
            });

            await image.destroy();
            req.flash('success', 'Image deleted successfully');
            res.redirect('/images');
        } catch (error) {
            res.status(500).render('error', { error });
        }
    }
}

module.exports = new ImagesController();
