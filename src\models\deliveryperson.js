'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class DeliveryPerson extends Model {
    static associate(models) {
      // مندوب التوصيل يمكن أن يقوم بعدة سجلات توصيل
      DeliveryPerson.hasMany(models.Delivery, {
        foreignKey: 'deliveryPersonId', as: 'deliveries'
      });

      // مندوب ينتمي لمتجر
      DeliveryPerson.belongsTo(models.Store, {
        foreignKey: 'storeId', as: 'store', onDelete: 'CASCADE'
      });
    }
  }

  DeliveryPerson.init({
    storeId: DataTypes.INTEGER,
    name: { type: DataTypes.STRING, allowNull: false },
    phoneNumber: DataTypes.STRING,
    status: {
      type: DataTypes.ENUM('active','inactive'),
      defaultValue: 'active'
    }
  }, {
    sequelize,
    modelName: 'DeliveryPerson',
    tableName: 'DeliveryPeople'
  });

  return DeliveryPerson;
};